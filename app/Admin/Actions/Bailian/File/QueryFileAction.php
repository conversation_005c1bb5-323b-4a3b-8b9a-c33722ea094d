<?php

namespace App\Admin\Actions\Bailian\File;

use App\Models\BailianKnowledgeFile;
use Dcat\Admin\Grid\RowAction;

class QueryFileAction extends RowAction
{
    protected string $title = '查询文件状态';

    public function handle()
    {
        $info = BailianKnowledgeFile::find($this->getKey());
        $info->resetStatus();
        return $this->response()->success('提交查询成功，请等待结果')->refresh();
    }

}