<?php

namespace App\Admin\Actions\Jm;

use App\Jobs\DoExecJob;
use App\Models\PluginJmDraw;
use Dcat\Admin\Grid\RowAction;

class ResetVideoAction extends RowAction
{
    protected string $title = '重新转换视频';

    public function handle()
    {
        $id    = $this->getKey();
        $model = PluginJmDraw::find($id);
        DoExecJob::dispatch($model, 'resetVideo');
        return $this->response()->success('重新转换视频成功')->refresh();
    }

    public function confirm()
    {
        return ['确定重新转换视频吗？', '重新转换视频后，视频会重新生成'];
    }
}