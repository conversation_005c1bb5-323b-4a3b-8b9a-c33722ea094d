<?php

namespace App\Admin\Actions\DrawAudio;

use App\Models\AiUnifyAsset;
use App\Models\DrawAudio;
use Dcat\Admin\Grid\RowAction;

class RefRunAudio extends RowAction
{
    protected string $title = '重新排队';

    public function handle()
    {
        $audio                = DrawAudio::find($this->getKey());
        $audio->error_message = null;
        $audio->status        = AiUnifyAsset::STATUS_INIT;
        $audio->created_at    = now();
        $audio->save();

        return $this->response()->success('执行成功')->refresh();
    }

}