<?php

namespace App\Admin\Actions\Message;

use Dcat\Admin\Actions\Response;
use Dcat\Admin\Admin;
use Dcat\Admin\Grid\Tools\AbstractTool;

class MarkAllAsRead extends AbstractTool
{
    protected string $title = '全部已读';

    protected string $style = 'btn btn-warning';

    public function handle(): Response
    {
        Admin::user()->unreadNotifications->markAsRead();

        return $this->response()->success('标记成功')->refresh();
    }

    public function confirm(): array
    {
        return [
            '标记已读',
            '确定要将所有的信息都标记为已读么？',
        ];
    }
}
