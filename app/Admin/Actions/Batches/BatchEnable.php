<?php

namespace App\Admin\Actions\Batches;

use Dcat\Admin\Actions\Response;
use Dcat\Admin\Grid\BatchAction;
use Illuminate\Http\Request;

class BatchEnable extends BatchAction
{
    protected string $title = '批量启用';

    public function __construct(protected ?string $model = null)
    {
        parent::__construct();
    }

    public function handle(Request $request): Response
    {
        $model = $request->get('model');

        foreach ((array) $this->getKey() as $key) {
            $model::findOrFail($key)->enable();
        }

        return $this->response()->success('已启用')->refresh();
    }

    public function confirm(): array
    {
        return [
            '批量启用',
            '确定批量启用选中的数据么？',
        ];
    }

    public function parameters(): array
    {
        return [
            'model' => $this->model,
        ];
    }
}