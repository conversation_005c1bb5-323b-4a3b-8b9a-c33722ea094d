<?php

namespace App\Admin\Actions\Batches;

use Dcat\Admin\Actions\Response;
use Dcat\Admin\Grid\BatchAction;
use Illuminate\Http\Request;

class BatchDisable extends BatchAction
{
    protected string $title = '批量禁用';

    public function __construct(protected ?string $model = null)
    {
        parent::__construct();
    }

    public function handle(Request $request): Response
    {
        $model = $request->get('model');

        foreach ((array) $this->getKey() as $key) {
            $model::findOrFail($key)->disable();
        }

        return $this->response()->success('已禁用')->refresh();
    }

    public function confirm(): array
    {
        return [
            '批量禁用',
            '确定批量禁用选中的数据么？',
        ];
    }

    public function parameters(): array
    {
        return [
            'model' => $this->model,
        ];
    }
}