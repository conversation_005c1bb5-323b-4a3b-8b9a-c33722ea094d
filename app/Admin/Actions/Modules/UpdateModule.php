<?php

namespace App\Admin\Actions\Modules;

use Dcat\Admin\Actions\Response;
use Dcat\Admin\Grid\RowAction;
use Illuminate\Http\Request;

class UpdateModule extends RowAction
{
    protected string $title = '更新';

    public function handle(Request $request): Response
    {
        $module = app('modules')->find($this->getKey());
        $class  = sprintf('\\%s\\%s\\Bootstrap', config('modules.namespace'), $module->getName());
        if (class_exists($class)) {
            if (method_exists($class, 'upgrade')) {
                call_user_func([$class, 'upgrade']);
                $this->updateCurrentVersion($module);
                return $this->response()->success('更新成功')->refresh();
            } else {
                return $this->response()->error('模块【upgrade】方法不存在');
            }
        } else {
            return $this->response()->error('模块入口不存在');
        }
    }

    /**
     * Notes   : 更新本地已安装版本信息
     *
     * @Date   : 2023/9/4 11:40
     * <AUTHOR> <Jason.C>
     */
    public function updateCurrentVersion(\Nwidart\Modules\Laravel\Module $module): void
    {
        $files  = app('files');
        $status = base_path('modules_versions.json');
        if (! $files->exists($status)) {
            $files->put($status, '[]');
        }
        $version = $module->get('version');

        $file = $files->get($status);
        $json = json_decode($file, true);

        $json[$module->getName()] = $version;

        $files->put($status, json_encode($json, JSON_PRETTY_PRINT));
    }

    public function confirm(): array
    {
        return [
            '确定要更新[ '.$this->getRow()->name.' ]模块么？',
            '更新前，请做好数据备份工作，以免数据丢失！',
        ];
    }

    protected function authorize($user): bool
    {
        return $user->isAdministrator();
    }
}