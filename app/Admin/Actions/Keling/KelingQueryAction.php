<?php

namespace App\Admin\Actions\Keling;

use App\Jobs\Plugin\KeLingQueryJob;
use App\Models\PluginKeLing;
use Dcat\Admin\Grid\RowAction;

class KelingQueryAction extends RowAction
{
    protected string $title = '重试查询';

    public function handle()
    {
        $task = PluginKeLing::find($this->getKey());
        KelingQueryJob::dispatch($task);
        return $this->response()->success('已加入队列')->refresh();
    }

    public function confirm()
    {
        return ['确定重试查询吗？', '确定重试查询吗？'];
    }
}