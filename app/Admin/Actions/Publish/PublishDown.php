<?php

namespace App\Admin\Actions\Publish;

use Dcat\Admin\Grid\RowAction;
use Exception;
use Illuminate\Http\Request;

class PublishDown extends RowAction
{
    protected string $title = '下架发布';

    public function handle(Request $request)
    {
        $itemClass = $request->model_class;
        $model     = new $itemClass();
        $item      = $model->find($this->getKey());
        if (! $item) {
            throw new Exception('资源不存在');
        }
        if (method_exists($item, 'publishDown')) {
            $item->publishDown();
            return $this->response()->success('下架发布成功')->refresh();
        } else {
            return $this->response()->error('模型不支持下架发布');
        }
    }

    public function confirm()
    {
        return '确定下架发布吗？';
    }

    public function parameters(): array
    {
        return [
            'model_class' => get_class($this->row),
        ];
    }

}