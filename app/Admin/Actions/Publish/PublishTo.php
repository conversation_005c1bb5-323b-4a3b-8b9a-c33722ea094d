<?php

namespace App\Admin\Actions\Publish;

use App\Admin\Forms\Publish\PublishToForm;
use Dcat\Admin\Grid\RowAction;
use Dcat\Admin\Widgets\Modal;

class PublishTo extends RowAction
{
    protected string $title = '公开发布';

    public function render(): string
    {
        return Modal::make()
            ->xl()
            ->centered()
            ->title('公开发布')
            ->body(PublishToForm::make([], $this->getKey())->payload([
                'item_id'    => $this->getKey(),
                'item_class' => get_class($this->row)
            ]))
            ->button($this->title);
    }

    public function parameters(): array
    {
        return [
            'model_class' => get_class($this->row),
        ];
    }
}