<?php

namespace App\Admin\Controllers\AiChat;

use App\Models\AudioRoom;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;
use Dcat\Admin\Widgets\Table;

class AudioRoomController extends AdminController
{
    protected string $title = '房间创建历史';

    protected function grid(): Grid
    {
        return Grid::make(AudioRoom::with([
            'user',
            'events'
        ])->latest(), function (Grid $grid) {
            $grid->disableCreateButton();
            $grid->disableEditButton();
            $grid->column('id');
            $grid->column('type', '类型');
            $grid->column('user.username', '用户');
            $grid->column('voice', '音色');
            $grid->column('room_id', '房间ID');
            $grid->column('记录')->expand(function(){
                $rows= $this->events->whereIn('type',[
                    'UserLeaveRoom',
                    'UserJoinRoom',
                    'RoomDestroy'
                ])
                    ->sortBy('event_data.Timestamp')
                    ->map(function ($item)  {
                        return [
                            'EventId'=>$item->event_id,
                            'Type'=>match ($item->type){
                                'UserLeaveRoom'=>'退出房间',
                                'UserJoinRoom'=>'加入房间',
                                'RoomDestroy'=>'房间销毁',
                                    default=>$item->type,
                            },
                            'UserId'=> ($item->event_data['UserId']??'') . ($item->type === 'UserLeaveRoom'?' (通话时长：'.$item->event_data['Duration'].'秒)':''),
                            'Time'=>date('Y-m-d H:i:s',$item->event_data['Timestamp']/1000),
                        ];
                    })->values()->toArray();
                $table=new Table([
                    '事件ID',
                    '类型',
                    '用户ID',
                    '时间',
                ],$rows);

                return $table;
            });
            $grid->column('ext','扩增信息');
            $grid->column('created_at');

        });
    }
}
