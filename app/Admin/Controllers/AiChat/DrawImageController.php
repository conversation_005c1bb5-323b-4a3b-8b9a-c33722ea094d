<?php

namespace App\Admin\Controllers\AiChat;

use App\Models\DrawImage;
use App\Models\DrawImageSize;
use App\Models\DrawImageStyle;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;

class DrawImageController extends AdminController
{
    protected string $title = 'AI绘画记录';

    protected function grid(): Grid
    {
        return Grid::make(DrawImage::with(['style', 'size'])->orderByDesc('updated_at'), function (Grid $grid) {
            $grid->disableCreateButton();
            $grid->disableEditButton();
            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('id');
                $filter->like('no', '内部单号');
                $filter->in('size_id', '尺寸')->multipleSelect(DrawImageSize::pluck('name', 'id'));
                $filter->in('style_id', '驱动模型')->multipleSelect(DrawImageStyle::pluck('name', 'id'));
                $filter->like('prompt', '修饰词');
                $filter->equal('status', '状态')->select(DrawImage::STATUS);
                $filter->between('over_at', '完成时间')->datetime();
                $filter->between('created_at', '创建时间')->datetime();
            });
            $grid->column('id');
            $grid->column('no', '内部单号');
            $grid->column('score', '积分');
            $grid->column('style.name', '驱动模型');
            $grid->column('size.name', '尺寸');
            $grid->column('prompt', '修饰词');
            $grid->column('cover', '结果图')->image('', 80);
            $grid->column('image_url', '原图')->image('', 80);
            $grid->column('status', '状态')->using(DrawImage::STATUS);
            $grid->column('error_message', '失败说明')->limit(10);
            $grid->column('over_at', '完成时间');
            $grid->column('created_at');
            $grid->column('updated_at');
        });
    }
}