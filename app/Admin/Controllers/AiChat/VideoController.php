<?php

namespace App\Admin\Controllers\AiChat;

use App\Models\DrawImage;
use App\Models\DrawVideo;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;

class VideoController extends AdminController
{
    protected string $title = '视频生成记录';

    protected function grid(): Grid
    {
        return Grid::make(DrawVideo::with([
            'style',
            'size',
        ])->orderByDesc('updated_at'), function (Grid $grid) {
            $grid->disableEditButton();
            $grid->disableCreateButton();
            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('id');
                $filter->like('no', '内部单号');
                $filter->like('prompt', '修饰词');
                $filter->equal('status', '状态')->select(DrawImage::STATUS);
                $filter->between('over_at', '完成时间')->datetime();
                $filter->between('created_at', '创建时间')->datetime();
            });
            $grid->column('id');
            $grid->column('no', '内部单号');
            $grid->column('score', '积分');
            $grid->column('model', '模型');
            $grid->column('prompt', '修饰词');
            $grid->column('image_url', '原图')->image('', 80);
            $grid->column('style.name', '样式');
            $grid->column('size.name', '尺寸');
            $grid->column('quality', '质量')->using(DrawVideo::QUALITY_ARRAY);
            $grid->column('with_audio', '背景音乐')->bool();
            $grid->column('cover', '结果')
                ->display(fn() => '点击查看')
                ->link(fn() => $this->cover_url);
            $grid->column('job_id', '三方ID');
            $grid->column('status', '状态')->using(DrawImage::STATUS);
            $grid->column('error_message', '失败说明')->limit(10);
            $grid->column('over_at', '完成时间');
            $grid->column('created_at');
            $grid->column('updated_at');
        });
    }
}