<?php

namespace App\Admin\Controllers\User;

use App\Admin\Traits\WithUploads;
use App\Models\Realname;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;

class RealnameController extends AdminController
{
    use WithUploads;

    protected string $title = '实名认证';

    public function grid(): Grid
    {
        return Grid::make(Realname::class, function (Grid $grid) {
            $grid->model()
                ->with(['user'])
                ->latest('id');
            $grid->disableActions();
            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('user_id', '所属用户')
                    ->select()
                    ->ajax(route('admin.user.users.ajax'));
                $filter->like('nickname', '姓名');
                $filter->like('phone', '手机号');
                $filter->like('card_num', '身份证号码');
            });

            $grid->column('id', '#ID#');
            $grid->column('username', '所属用户')
                ->display(fn() => $this->user?->show_name);
            $grid->column('nickname', '姓名');
            $grid->column('phone', '手机号');
            $grid->column('card_num', '身份证号码');
            $grid->column('created_at');
        });
    }

}
