<?php

namespace App\Admin\Controllers\Publish;

use App\Models\PublishCategory;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;

class CategoryController extends AdminController
{
    protected string $title = '发布内容分类';

    public function grid(): Grid
    {
        return Grid::make(PublishCategory::ordered(), function (Grid $grid) {
            $grid->column('id');
            $grid->column('name', '分类名称');
            $grid->column('order', '排序')->orderable();
            $grid->column('status', '状态')->switch();
            $grid->column('created_at');
            $grid->column('updated_at');
        });
    }

    protected function form(): Form
    {
        return Form::make(PublishCategory::class, function (Form $form) {
            $form->text('name', '分类名称');
            $form->number('order', '排序')->default(0);
            $form->switch('status', '状态')->default(true);
        });
    }
}