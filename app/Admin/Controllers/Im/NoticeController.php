<?php

namespace App\Admin\Controllers\Im;

use App\Admin\Actions\Im\SendImNotification;
use App\Admin\Traits\WithUploads;
use App\Models\ImNotice;
use App\Models\User;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;

class NoticeController extends AdminController
{
    use WithUploads;

    protected string $title = '推送管理';

    public function grid(): Grid
    {
        return Grid::make(new ImNotice(), function (Grid $grid) {
            $grid->filter(function (Grid\Filter $filter) {
                $filter->like('title', '内容标题');

                $filter->equal('type')
                    ->select(ImNotice::TYPE_MAP);
                $filter->between('created_at', '创建时间')->datetime();
            });

            // 添加发布按钮
            $grid->actions(function (Grid\Displayers\Actions $actions) {
                $actions->disableDelete();
                if ($actions->row->canSend()) {
                    $actions->append(new SendImNotification());
                } else {
                    $actions->disableEdit();
                }
            });

            $grid->column('id', '#ID#');
            $grid->column('title', '标题');
            $grid->column('type', '类型')->using(ImNotice::TYPE_MAP);
            $grid->column('status', '状态')
                ->using(ImNotice::STATUS_MAP)
                ->label(ImNotice::STATUS_LABEL);
            $grid->column('remark', '结果');
            $grid->column('send_at', '发送时间');
            $grid->column('created_at');
        });
    }

    public function form(): Form
    {
        return Form::make(ImNotice::class, function (Form $form) {
            $form->text('title', '标题')->required();
            $form->radio('type', '类型')
                ->options(ImNotice::TYPE_MAP)
                ->default(ImNotice::TYPE_ALL)
                ->when(ImNotice::TYPE_SELECT, function (Form $form) {
                    $form->multipleSelect('user_ids', '发放用户')
                        ->options(function ($userId) {
                            if ($userId) {
                                return [$userId => User::find($userId)->showName];
                            } else {
                                return [];
                            }
                        })
                        ->ajax(route('admin.user.users.ajax'));
                });
            $form->textarea('description', '内容')->required();
            $form->radio('status', '状态')
                ->options(ImNotice::STATUS_MAP)
                ->default(1);
            $form->saving(function (Form $form) {
                if (request()->type == ImNotice::TYPE_SELECT && empty(request()->user_ids)) {
                    return $form->response()->error('请选择发放用户');
                }
            });
        });
    }
}
