<?php

namespace App\Admin\Controllers\AiTool;

use App\Admin\Traits\WithUploads;
use App\Models\AiTool;
use App\Models\AiToolCategory;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;

class IndexController extends AdminController
{
    use WithUploads;

    protected string $title = 'Ai工具';

    public function grid(): Grid
    {
        return Grid::make(AiTool::class, function (Grid $grid) {
            $grid->model()
                ->with(['category'])
                ->orderBy('order')
                ->latest('id');

            $grid->filter(function (Grid\Filter $filter) {
                $filter->like('name', '工具名称');
                $filter->like('category.name', '分类名称');
            });

            $grid->column('id', '#ID#');
            $grid->column('cover', '封面图')
                ->image('', 50);
            $grid->column('name', '名称');
            $grid->column('category.name', '分类名称');

            $grid->column('times', '使用量');
            $grid->column('price', '价格');
            $grid->column('created_at');
        });
    }

    public function form(): Form
    {
        return Form::make(AiTool::with('category'), function (Form $form) {
            $form->text('name', '名称')
                ->required();
            $form->select('category_id', '分类')
                ->options(function () {
                    return AiToolCategory::pluck('name', 'id');
                });
            $form->textarea('description', '简介');

            $this->cover($form);
            $form->number('price', '价格')->default(0);
            $form->text('url', '跳转地址');
            $form->switch('status', '状态')->default(1);
        });
    }
}
