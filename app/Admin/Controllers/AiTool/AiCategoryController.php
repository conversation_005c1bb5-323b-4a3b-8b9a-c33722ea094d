<?php

namespace App\Admin\Controllers\AiTool;

use App\Admin\Traits\WithUploads;
use App\Models\AiToolCategory;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;

class AiCategoryController extends AdminController
{
    use WithUploads;

    protected string $title = '分类';

    public function grid(): Grid
    {
        return Grid::make(AiToolCategory::class, function (Grid $grid) {
            $grid->filter(function (Grid\Filter $filter) {
                $filter->like('name', '分类名称');
                $filter->equal('status', '状态')
                    ->radio([
                        0 => '禁用',
                        1 => '启用',
                    ]);
                $filter->between('created_at', '添加时间')->datetime();
            });

            $grid->column('id');
            $grid->column('name', '分类名称');
            $grid->column('description', '简介');
            $grid->column('status', '状态')->switch();
            $grid->column('created_at');
        });
    }

    public function form(): Form
    {
        return Form::make(AiToolCategory::class, function (Form $form) {
            $form->text('name', '分类名称')
                ->required();
            $this->cover($form)
                ->width(4);
            $form->textarea('description', '分类简介');
            $form->switch('status', '状态')->default(1);
        });
    }
}
