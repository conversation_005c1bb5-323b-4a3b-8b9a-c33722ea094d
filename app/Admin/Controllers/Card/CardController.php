<?php

namespace App\Admin\Controllers\Card;

use App\Models\Card;
use App\Models\CardBatch;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;

class CardController extends AdminController
{
    protected string $title = '卡管理';

    protected function grid(): Grid
    {
        return Grid::make(Card::with(['batch', 'user'])->orderByDesc('created_at'), function (Grid $grid) {
            $grid->disableCreateButton();
            $grid->disableEditButton();
            $grid->disableDeleteButton();
            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('id');
                $filter->equal('batch_id', '卡批次')->select(CardBatch::pluck('name', 'id'));
                $filter->like('no', '卡号');
                $filter->equal('status', '状态')->select(Card::STATUS);
                $filter->between('used_at', '使用时间')->datetime();
                $filter->between('failure_at', '失效时间')->datetime();
                $filter->between('created_at', '创建时间')->datetime();
            });
            $grid->column('id');
            $grid->column('batch.name', '批次名称');
            $grid->column('no', '卡号');
            $grid->column('secret', '卡密');
            $grid->column('status', '状态')->using(Card::STATUS);
            $grid->column('used_at', '使用时间');
            $grid->column('user.username', '使用用户');
            $grid->column('failure_at', '失效时间');
            $grid->column('created_at', '创建时间');
            $grid->export()->xlsx()->filename('兑换卡导出'.date('YmdHis'));
        });
    }
}