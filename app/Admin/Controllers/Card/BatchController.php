<?php

namespace App\Admin\Controllers\Card;

use App\Admin\Actions\Card\BatchCreateCard;
use App\Models\Card;
use App\Models\CardBatch;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;

class BatchController extends AdminController
{
    protected string $title = '兑换卡批次管理';

    protected function grid(): Grid
    {
        return Grid::make(CardBatch::with(['cards'])
            ->withCount([
                'cards',
                'cards as init_count'    => function ($query) {
                    $query->init();
                },
                'cards as used_count'    => function ($query) {
                    $query->used();
                },
                'cards as failure_count' => function ($query) {
                    $query->failure();
                }
            ])->latest(), function (Grid $grid) {
            $grid->actions(function (Grid\Displayers\Actions $actions) {
                $actions->append(new BatchCreateCard());
            });
            $grid->column('id');
            $grid->column('name', '批次名称');
            $grid->column('def_score', '默认积分');
            $grid->column('prefix', '前缀');
            $grid->column('def_time', '默认有效期(自卡生成开始)')->append(' / 天');
            $grid->column('cards_count', '总数量')
                ->append(' / 个')
                ->link(fn() => admin_url('card/cards').'?batch_id='.$this->id);
            $grid->column('init_count', '未使用')
                ->append(' / 个')
                ->link(fn() => admin_url('card/cards').'?batch_id='.$this->id.'&status='.Card::STATUS_INIT);
            $grid->column('used_count', '已使用')
                ->append(' / 个')
                ->link(fn() => admin_url('card/cards').'?batch_id='.$this->id.'&status='.Card::STATUS_USED);
            $grid->column('failure_count', '已过期')
                ->append(' / 个')
                ->link(fn() => admin_url('card/cards').'?batch_id='.$this->id.'&status='.Card::STATUS_FAILURE);
            $grid->column('status', '状态')->switch();
            $grid->column('created_at');
            $grid->column('updated_at')->sortable();
        });
    }

    protected function form(): Form
    {
        return Form::make(CardBatch::class, function (Form $form) {
            $form->text('name')->required();
            $form->number('def_score', '默认积分')->min(0)->required()->disable($form->isEditing());
//            $form->number('no_length', '编号长度')->min(10)->required()->disable($form->isEditing())->help('不含前缀');
            $form->number('def_time', '默认有效期(天)')->min(0)->required()->help('自卡生成开始计算');
            $form->text('prefix', '前缀')->disable($form->isEditing());
            $form->switch('status')->default(1)->required();
        });
    }
}