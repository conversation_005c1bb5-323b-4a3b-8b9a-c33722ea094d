<?php

namespace App\Admin\Controllers\Company;

use App\Models\CompanyUser;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;

class UserController extends AdminController
{
    protected string $title = '员工';

    protected function grid()
    {
        return Grid::make(CompanyUser::class, function (Grid $grid) {
            $grid->disableRowSelector();
            $grid->model()->with(['user', 'company']);

            $grid->filter(function (Grid\Filter $filter) {
                $filter->like('nickname', '姓名');
                $filter->between('created_at', '创建时间')->datetime();
            });

            $grid->column('id', 'ID')->sortable();
            $grid->column('company.company_name', '所属企业');
            $grid->column('nickname', '姓名');
            $grid->column('position', '职位');
            $grid->column('is_check', '审核状态')
                ->using(CompanyUser::IS_CHECK)
                ->label();
            $grid->column('remark', '拒绝原因');
            $grid->column('is_manage', '身份')->using(CompanyUser::IS_MANAGE)->label();
            $grid->column('is_work', '状态')->using(CompanyUser::IS_WORK)->label();

            $grid->column('created_at');
        });
    }

    public function form(): Form
    {
        return Form::make(CompanyUser::class, function (Form $form) {
            $form->text('nickname', '姓名')->required();
            $form->text('position', '职位')->required();
            $form->text('remark', '拒绝原因');
            $form->radio('is_check', '审核状态')
                ->options(CompanyUser::IS_CHECK);
            $form->radio('is_work', '在职状态')
                ->options(CompanyUser::IS_WORK);
        });
    }
}