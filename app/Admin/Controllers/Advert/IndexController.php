<?php

namespace App\Admin\Controllers\Advert;

use App\Admin\Traits\WithUploads;
use App\Models\Advert;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;

class IndexController extends AdminController
{
    use WithUploads;

    protected string $title = '广告';

    public function grid(): Grid
    {
        return Grid::make(Advert::class, function (Grid $grid) {
            $grid->disableRowSelector();

            $grid->filter(function (Grid\Filter $filter) {
                $filter->like('title', '名称');
                $filter->equal('type', '类型')
                    ->select(Advert::TYPE_MAP);
                $filter->equal('position', '位置')
                    ->select(Advert::POSITION_MAP);
            });

            $grid->column('id', 'ID')->sortable();
            $grid->column('cover', '图片')->thumb(32);
            $grid->column('title', '名称');
            $grid->column('type', '类型')
                ->using(Advert::TYPE_MAP)
                ->label(Advert::TYPE_LABEL_MAP);
            $grid->column('position', '位置')
                ->using(Advert::POSITION_MAP)
                ->label(Advert::POSITION_LABEL_MAP);
            $grid->column('jump_target', '跳转的地址');
            $grid->column('mini_program_id', '小程序id');
            $grid->column('status', '状态')->bool();
            $grid->column('order', '排序')->editable();
            $grid->column('created_at');
        });
    }

    public function form(): Form
    {
        return Form::make(Advert::class, function (Form $form) {
            $form->text('title', '名称')->required();
            $form->textarea('description', '简介');
            $form->select('position', '位置')
                ->options(Advert::POSITION_MAP);

            $form->radio('type', '类型')
                ->options(Advert::TYPE_MAP)
                ->default(Advert::TYPE_WEB)
                ->when(Advert::TYPE_MINI_PROGRAM, function (Form $form) {
                    $form->text('mini_program_id', '小程序id');
                })
                ->help('类型是图片时，只展示banner图，无跳转功能')
                ->required();
            $form->text('jump_target', '跳转地址');
            $form->keyValue('params', '参数');
            $this->cover($form)->width(4);
            $form->number('order')->default(0);
            $form->switch('status')->default(1);
        });
    }

}