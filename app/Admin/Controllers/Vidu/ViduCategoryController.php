<?php

namespace App\Admin\Controllers\Vidu;

use App\Admin\Traits\WithUploads;
use App\Models\PluginViduTemplateCategory;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;

class ViduCategoryController extends AdminController
{
    use WithUploads;

    protected string $title = '模板分类';

    public function grid(): Grid
    {
        return Grid::make(PluginViduTemplateCategory::class, function (Grid $grid) {
            $grid->filter(function (Grid\Filter $filter) {
                $filter->like('name', '名称');
            });

            $grid->actions(function (Grid\Displayers\Actions $actions) {
                $actions->disableDelete();
            });

            $grid->column('id', '#ID#');
            $grid->column('name', '名称');
            $grid->column('created_at');
        });
    }

    public function form(): Form
    {
        return Form::make(PluginViduTemplateCategory::class, function (Form $form) {
            $form->text('name', '名称')
                ->required();
        });
    }
}
