<?php

namespace App\Admin\Controllers\Vidu;

use App\Admin\Actions\Vidu\DoDrawAction;
use App\Admin\Actions\Vidu\QueryDrawAction;
use App\Admin\Traits\WithUploads;
use App\Models\AiUnifyAsset;
use App\Models\PluginViduDraw;
use App\Models\PluginViduTemplateCategory;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;

class ViduDrawController extends AdminController
{
    use WithUploads;

    protected string $title = '特效任务';

    public function grid(): Grid
    {
        return Grid::make(PluginViduDraw::class, function (Grid $grid) {
            $grid->model()
                ->with(['template'])
                ->latest('id');

            $grid->filter(function (Grid\Filter $filter) {
                $filter->like('name', '名称');
                $filter->like('vidu_template_category_id', '所属分类')
                    ->select(PluginViduTemplateCategory::query()->pluck('name', 'id'));
                $filter->like('scene', '模板key');
            });

            $grid->actions(function (Grid\Displayers\Actions $actions) {
                $actions->disableDelete();
                $actions->disableEdit();
                if ($actions->row->canDraw()) {
                    $actions->append(new DoDrawAction());
                }
                if ($actions->row->task_id) {
                    $actions->append(new QueryDrawAction());
                }
            });

            $grid->column('id', '#ID#');
            $grid->column('user', '操作人')
                ->display(fn() => $this->user->showName);
            $grid->column('no', '编号');
            $grid->column('task_id', '任务编号');
            $grid->column('template.name', '模版名称');
            $grid->column('inputs', '输入')->display(function ($values) {
                $urls   = collect($values)->pluck('url');
                $result = [];
                foreach ($urls as $url) {
                    if (is_array($url)) {
                        foreach ($url as $item) {
                            $result[] = $item;
                        }
                    } else {
                        $result[] = $url;
                    }
                }
                return $result;
            })->image('', 40);
            $grid->column('cover', '结果封面')->image('', 60, 60);
            $grid->column('video', '结果视频')
                ->display(fn() => '点击查看')
                ->link(fn() => $this->videoUrlAttr);
            $grid->column('start_at', '开始时间');
            $grid->column('over_at', '结束时间');
            $grid->column('error_message', '错误信息');
            $grid->column('status', '状态')
                ->using(AiUnifyAsset::STATUS);
            $grid->column('created_at', '创建时间');
            $grid->column('updated_at', '更新时间');
        });
    }

    public function form(): Form
    {
        return Form::make(PluginViduTemplateCategory::class, function (Form $form) {
            $form->text('name', '名称')
                ->required();
        });
    }
}
