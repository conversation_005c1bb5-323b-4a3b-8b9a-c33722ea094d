<?php

namespace App\Admin\Controllers\Vidu;

use App\Admin\Actions\Company\BatchSetRiskLevel;
use App\Admin\Actions\Vidu\BatchSetInstructions;
use App\Admin\Renders\Vidu\DetailData;
use App\Admin\Renders\Vidu\InputInstructionData;
use App\Admin\Traits\WithUploads;
use App\Models\PluginViduTemplate;
use App\Models\PluginViduTemplateCategory;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;

class ViduTemplateController extends AdminController
{
    use WithUploads;

    protected string $title = '特效模板';

    public function grid(): Grid
    {
        return Grid::make(PluginViduTemplate::class, function (Grid $grid) {
            $grid->model()
                ->with(['category'])
                ->latest('id');
            $grid->filter(function (Grid\Filter $filter) {
                $filter->like('name', '名称');
                $filter->like('vidu_template_category_id', '所属分类')
                    ->select(PluginViduTemplateCategory::query()->pluck('name', 'id'));
                $filter->like('scene', '模板key');
            });

            $grid->actions(function (Grid\Displayers\Actions $actions) {
                $actions->disableDelete();
            });
            $grid->batchActions([
                new BatchSetInstructions('批量设置描述信息'),
            ]);

            $grid->column('id', '#ID#');
            $grid->column('name', '名称');
            $grid->column('category.name', '所属分类');
            $grid->column('scene', '模板key');
            $grid->column('cover_url', '示例图片')->image('', 60, 60);
            $grid->column('video_url', '示例视频')
                ->display('点击查看')
                ->link(fn() => $this->video_url);
            $grid->column('detail', '场景详情')
                ->display('详情')
                ->modal(DetailData::make());
            $grid->column('input_instruction', '输入说明')
                ->display('查看')
                ->modal(InputInstructionData::make());
            $grid->column('instructions', '说明')->toArray();
            $grid->column('prompt', '提示词')->limit(100);
        });
    }

    public function form(): Form
    {
        return Form::make(PluginViduTemplate::class, function (Form $form) {
            $form->text('instructions.title', '主描述')
                ->required();
            $form->text('instructions.subtitle', '副描述')
                ->required();
        });
    }
}
