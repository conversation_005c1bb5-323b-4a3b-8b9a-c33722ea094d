<?php

namespace App\Admin\Controllers\Voice\Volcengine;

use App\Models\AudioVolcengineTts;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;

class AudioTtsController extends AdminController
{
    protected string $title = '火山音色';

    public function grid(): Grid
    {
        /**
         * 语音角色配置数组
         * 包含角色名称、语言、标识等信息
         */
        return Grid::make(AudioVolcengineTts::class, function (Grid $grid) {
            $grid->model()->latest('id');
            $grid->column('id');
            $grid->column('name', '音色名称')->sortable();
            $grid->column('scene', '推荐场景');
            $grid->column('language', '语种');
            $grid->column('voice_type', 'Voice_type');
            $grid->column('audio_url', '音频地址')
                ->display(function ($value) {
                    return '<audio controls>
  <source src="'.$this->audio_url.'" type="audio/mpeg">
</audio>';
                });
            $grid->column('created_at');
        });
    }

    public function form(): Form
    {
        return Form::make(new AudioVolcengineTts(), function (Form $form) {
            $form->text('name', '音色名称');
            $form->select('scene', '推荐场景')->options(AudioVolcengineTts::SCENE_TYPE_MAP);
            $form->select('language', '语种')->options(AudioVolcengineTts::LANGUAGE_TYPE_MAP);
            $form->text('voice_type', 'Voice_type');
            $form->text('audio_url', '音频地址');
        });
    }
}
