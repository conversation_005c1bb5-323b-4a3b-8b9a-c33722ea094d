<?php

namespace App\Admin\Controllers\Plugin;

use App\Admin\Actions\Keling\KelingQueryAction;
use App\Admin\Actions\Keling\ResetVideoAction;
use App\Models\AiUnifyAsset;
use App\Models\PluginKeLing;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;

class KeLingController extends AdminController
{
    protected string $title = '可灵';

    public function form(): Form
    {
        return Form::make(new PluginKeLing(), function (Form $form) {
        });
    }

    protected function grid(): Grid
    {
        return Grid::make(PluginKeLing::latest('created_at'), function (Grid $grid) {
            $grid->disableCreateButton();
            $grid->actions(function (Grid\Displayers\Actions $actions) {
                if ($actions->row->canPolling()) {
                    $actions->append(new KelingQueryAction());
                }
                if ($actions->row->canResetVideo()) {
                    $actions->append(new ResetVideoAction());
                }
            });
            $grid->column('id');
            $grid->column('no', '单号');
            $grid->column('task_id', '任务ID');
            $grid->column('type', '类型')->using(PluginKeLing::TYPES);
            $grid->column('prompt', '描述');
            $grid->column('params.inputs', '上传资源')->display(function ($values) {
                return collect($values)->whereNotIn('name', [
                    'raw_ref_img_0',
                    'raw_ref_img_1',
                    'raw_ref_img_2',
                    'raw_ref_img_3',
                ])->pluck('url')->toArray();
            })
                ->image('', 30, 30);
            $grid->column('cover', '结果封面')->image('', 50, 50);
            $grid->column('video_url', '结果视频')
                ->display(fn() => '点击查看')
                ->link(fn() => $this->videoUrlAttr);
            $grid->column('status', '状态')->using(AiUnifyAsset::STATUS);
            $grid->column('error_message', '说明')->limit(10);
            $grid->column('created_at');
            $grid->column('start_at', '执行时间');
            $grid->column('over_at', '完成时间');
        });
    }
}