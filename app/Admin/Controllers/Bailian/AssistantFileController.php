<?php

namespace App\Admin\Controllers\Bailian;

use App\Admin\Actions\Bailian\Assistant\QueryFileAction;
use App\Models\BailianAssistantFile;
use App\Models\BailianKnowledgeFile;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;

class AssistantFileController extends AdminController
{
    protected string $title = '智能体文件';

    public function grid(): Grid
    {
        return Grid::make(BailianAssistantFile::class, function (Grid $grid) {
            $grid->model()
                ->with(['user', 'storage'])
                ->latest('id');

            $grid->disableCreateButton();
            $grid->actions(function (Grid\Displayers\Actions $actions) {
                $actions->disableView();
                $actions->disableEdit();
                $actions->disableDelete();

                $actions->append(new QueryFileAction());
            });
            $grid->column('id')->sortable();
            $grid->column('name', '名称');

            $grid->column('user', '所属用户')
                ->display(function ($user) {
                    return $this->user?->show_name;
                });
            $grid->column('file_id', '阿里云文件ID');
            $grid->column('category_type', '类目类型')
                ->using(BailianKnowledgeFile::CATEGORY_TYPES);

            $grid->column('status', '状态')
                ->using(BailianKnowledgeFile::STATUS_MAP)
                ->label(BailianKnowledgeFile::STATUS_LABEL);
            $grid->column('created_at');
        });
    }

}