<?php

namespace App\Admin\Controllers\Bailian;

use App\Admin\Traits\WithUploads;
use App\Models\BailianFile;
use App\Models\User;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;

class FileController extends AdminController
{
    use WithUploads;

    protected string $title = '文档列表';

    public function grid(): Grid
    {
        return Grid::make(BailianFile::class, function (Grid $grid) {
            $grid->model()
                ->with(['user', 'knowledgeItem.baiLianKnowledge'])
                ->latest('id');
            $grid->showBatchDelete();
            $grid->disableCreateButton();

            $grid->filter(function (Grid\Filter $filter) {
                $filter->like('title', '标题');
                $filter->like('user_id', '所属用户')
                    ->select()
                    ->ajax(route('admin.user.users.ajax'));
                $filter->equal('is_public', '是否公开')
                    ->select([
                        0 => '否',
                        1 => '是'
                    ]);
                $filter->equal('is_featured', '是否精选')
                    ->select([
                        0 => '否',
                        1 => '是'
                    ]);
            });

            $grid->column('id', '#ID#');
            $grid->column('title', '标题');
            $grid->column('knowledge', '知识库')
                ->display(function () {
                    return $this->knowledgeItem->bailianKnowledge->name ?? '---';
                });
            $grid->column('username', '所属用户')
                ->display(fn() => $this->user->show_name);
            $grid->column('is_public', '公开')->bool();
            $grid->column('is_featured', '精选')->bool();
            $grid->column('size', '大小')
                ->display(fn() => $this->readable_size);
            $grid->column('created_at');
        });
    }

    public function form(): Form
    {
        return Form::make(BailianFile::class, function (Form $form) {
            $form->text('title', '标题')->required();
            $form->select('user_id', '隶属用户')
                ->options(function ($userId) {
                    if ($userId) {
                        return [$userId => User::find($userId)->showName];
                    } else {
                        return [];
                    }
                })
                ->ajax(route('admin.user.users.ajax'))
                ->required();
            $form->switch('is_public', '是否公开');
            $form->switch('is_featured', '是否精选');
        });
    }

}
