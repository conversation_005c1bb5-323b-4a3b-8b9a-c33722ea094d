<?php

namespace App\Admin\Controllers\Bailian;

use App\Enums\Bailian\ItemSyncStatusEnum;
use App\Models\BailianKnowledgeItem;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;

class KnowledgeItemController extends AdminController
{
    protected string $title = '目录';

    public function grid(): Grid
    {
        return Grid::make(BailianKnowledgeItem::class, function (Grid $grid) {
            $grid->disableCreateButton();
            $grid->model()
                ->with(['user', 'baiLianKnowledge', 'itemable'])
                ->latest('id');

            $grid->filter(function (Grid\Filter $filter) {
                $filter->like('title', '名称');
                $filter->like('knowledge_id', '知识库名称')
                    ->select()
                    ->ajax(route('admin.bailian.knowledge.filter_ajax'));
            });

            $grid->column('id')->sortable();
            $grid->column('user_name', '创建人')
                ->display(fn() => $this->user->show_name);
            $grid->column('baiLianKnowledge.name', '知识库名称');
            $grid->column('title', '名称');
            $grid->column('itemable_type', '类型')
                ->using(BailianKnowledgeItem::ITEMABLE_TYPES)
                ->label(BailianKnowledgeItem::ITEMABLE_TYPE_LABEL);
            $grid->column('sync_status', '同步状态')
                ->using(ItemSyncStatusEnum::STATUS_MAP)
                ->label(ItemSyncStatusEnum::LABEL_MAP);
            $grid->column('synced_at', '同步时间');
            $grid->column('sync_error', '同步错误')->limit(10);
            $grid->column('created_at');
        });
    }

    public function form(): Form
    {
        return Form::make(BailianKnowledgeItem::class, function (Form $form) {
            $form->text('title', '标题')->required();
        });
    }

}