<?php

namespace App\Admin\Controllers\Bailian;

use App\Admin\Actions\Bailian\Knowledge\GetIndexDocumentsAction;
use App\Admin\Actions\Bailian\Knowledge\GetRetrieveAction;
use App\Admin\Actions\Bailian\Knowledge\SubmitIndexAddDocumentsJobAction;
use App\Admin\Renders\Bailian\Knowledge\GetIndexDocumentsTable;
use App\Enums\Bailian\BailianLevelEnum;
use App\Models\BailianCategory;
use App\Models\BailianKnowledge;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;
use Illuminate\Http\Request;

class KnowledgeController extends AdminController
{
    protected string $title = '知识库';

    public function grid(): Grid
    {
        return Grid::make(BailianKnowledge::with(['user', 'categories'])->latest(), function (Grid $grid) {
            $grid->model()
                ->latest('id');

            $grid->filter(function (Grid\Filter $filter) {
                $filter->like('name', '名称');
                $filter->like('user_id', '创建人')
                    ->select()
                    ->ajax(route('admin.user.users.ajax'));
                $filter->equal('workspace_id', '业务空间 ID');
                $filter->equal('knowledge_id', '阿里云知识库ID');
                $filter->equal('source_type', '来源类型')
                    ->select(BailianKnowledge::SOURCE_TYPES);
                $filter->equal('type', '类型')
                    ->select(BailianKnowledge::TYPES);
                $filter->equal('sink_type', '存储类型')
                    ->select(BailianKnowledge::SINK_TYPES);
                $filter->equal('is_featured', '是否精选')
                    ->select([
                        0 => '否',
                        1 => '是',
                    ]);
            });

            $grid->actions(function (Grid\Displayers\Actions $actions) {
                $actions->disableView();
                $actions->disableDelete();
                $actions->append(new SubmitIndexAddDocumentsJobAction());
//                $actions->append(new GetIndexJobStatusAction());
//                $actions->append(new GetListIndexAction());
                $actions->append(new GetRetrieveAction());
                $actions->append(new GetIndexDocumentsAction());
            });

            $grid->column('id')->sortable();
            $grid->column('name', '名称');
            $grid->column('categories', '分类')
                ->display(function ($categories) {
                    return collect($categories)->pluck('name');
                })
                ->label();
            $grid->column('user', '创建人')
                ->display(function ($user) {
                    return $this->user?->show_name;
                });
            $grid->column('workspace_id', '业务空间 ID');
            $grid->column('knowledge_id', '阿里云知识库ID');
            $grid->column('description', '描述');
            $grid->column('source_type', '来源类型')->using(BailianKnowledge::SOURCE_TYPES);
            $grid->column('sink_type', '存储类型')->using(BailianKnowledge::SINK_TYPES);
            $grid->column('type', '类型')
                ->using(BailianKnowledge::TYPES)
                ->label();
            $grid->column('error_message', '错误信息');
            $grid->column('status', '状态')->switch();
            $grid->column('is_featured', '是否精选')->switch();
            $grid->column('level', '权限')
                ->using(BailianLevelEnum::STATUS_MAP);
            $grid->column('百炼文档状态')
                ->display('详情')
                ->modal('百炼文档状态', function () {
                    return GetIndexDocumentsTable::make()->payload(['key' => $this->id]);
                });
            $grid->column('size', '大小')->display(fn() => $this->readable_size);
            $grid->column('created_at');
        });
    }

    public function form(): Form
    {
        return Form::make(BailianKnowledge::with(['categories']), function (Form $form) {
            $form->text('name', '名称')->required();

            $form->textarea('description', '描述');
            $form->radio('type', '类型')
                ->options(BailianKnowledge::TYPES)
                ->default(BailianKnowledge::TYPE_NORMAL)
                ->load('categories', '/bailian/category/ajax');

            $form->select('source_type', '来源类型')
                ->options(BailianKnowledge::SOURCE_TYPES)
                ->default(BailianKnowledge::SOURCE_TYPE_CATEGORY)
                ->when(BailianKnowledge::SOURCE_TYPE_CATEGORY, function (Form $form) {
                    $form->multipleSelect('categories', '分类')
                        ->options(function () {
                            return BailianCategory::where('type', BailianCategory::TYPE_NORMAL)
                                ->pluck('name', 'id');
                        })
                        ->customFormat(function ($v) {
                            return array_column($v, 'id');
                        });
                })
                ->required();

            $form->switch('status', '状态')->default(1);
            $form->switch('is_featured', '是否精选')->default(0);
            $form->radio('level', '权限')
                ->options(BailianLevelEnum::STATUS_MAP)
                ->default(BailianLevelEnum::PRIVATE);
            $form->hidden('structure_type')->default('unstructured');
            $form->hidden('sink_type')->default(BailianKnowledge::SINK_TYPE_BUILT_IN);

            $form->saving(function (Form $form) {
                if ($form->source_type == BailianKnowledge::SOURCE_TYPE_CATEGORY && empty(request()->categories)) {
                    return $form->response()->error('请选择分类');
                }
            });
        });
    }

    public function filterAjax(Request $request)
    {
        $q = $request->q;
        return BailianKnowledge::query()
            ->where('name', 'like', "%{$q}%")
            ->paginate(null, ['id', 'name as text']);
    }

}