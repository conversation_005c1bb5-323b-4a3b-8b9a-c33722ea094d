<?php

namespace App\Admin\Controllers\Note;

use App\Admin\Traits\WithUploads;
use App\Models\NoteTag;
use App\Models\User;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;
use Illuminate\Http\Request;

class NoteTagController extends AdminController
{
    use WithUploads;

    protected string $title = '标签管理';

    public function grid(): Grid
    {
        return Grid::make(NoteTag::class, function (Grid $grid) {
            $grid->showBatchDelete();

            $grid->filter(function (Grid\Filter $filter) {
                $filter->like('name', '名称');
                $filter->like('user_id', '所属用户')
                    ->select()
                    ->ajax(route('admin.user.users.ajax'));
            });

            $grid->column('id', '#ID#');
            $grid->column('username', '所属用户')
                ->display(fn() => $this->user->show_name);
            $grid->column('name', '名称');
            $grid->column('created_at');
        });
    }

    public function form(): Form
    {
        return Form::make(NoteTag::with('tags'), function (Form $form) {
            $form->select('user_id', '隶属用户')
                ->options(function ($userId) {
                    if ($userId) {
                        return [$userId => User::find($userId)->showName];
                    } else {
                        return [];
                    }
                })
                ->ajax(route('admin.user.users.ajax'))
                ->required();
            $form->text('name', '标题')->required();
        });
    }

    public function ajax(Request $request)
    {
        $q = $request->get('q');

        return NoteTag::query()
            ->where('user_id', $q)
            ->select('id', 'name as text')
            ->get();
    }
}
