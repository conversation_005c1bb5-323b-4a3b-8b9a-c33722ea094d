<?php

namespace App\Admin\Controllers\Assistant;

use App\Admin\Traits\WithUploads;
use App\Models\BailianAssistantLog;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;
use Dcat\Admin\Widgets\Markdown;
use Dcat\Admin\Widgets\Modal;

class ChatController extends AdminController
{
    use WithUploads;

    protected string $title = 'AI助手聊天记录';

    public function grid(): Grid
    {
        return Grid::make(BailianAssistantLog::whereNull('ai_unify_asset_id')
            ->with(['aiUnifyAsset'])
            ->withCount(['actions'])
            ->latest('id'), function (Grid $grid) {
            $grid->showBatchDelete();
            $grid->column('id', '#ID#');
            $grid->column('用户')->display(function () {
                return $this->user->showAllName;
            })->append(function () {
                return '【'.$this->user_id.'】';
            });
            $grid->column('prompt', '提示词')->limit(20);
            $grid->column('output', '输出')
                ->display(function () {
                    $modal = Modal::make()
                        ->xl()
                        ->title('输出结果')
                        ->body(Markdown::make($this->output))
                        ->button('<button class="btn btn-primary">点击查看</button>');
                    return $modal->render();
                });
            $grid->column('actions_count', '步骤')
                ->append(' 步');
            $grid->column('created_at');
        });
    }

    public function form()
    {
    }
}
