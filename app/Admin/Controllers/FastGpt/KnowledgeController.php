<?php

namespace App\Admin\Controllers\FastGpt;

use App\Admin\Traits\WithUploads;
use App\Models\Enums\FastgptKnowledgeLevelEnum;
use App\Models\FastgptKnowledge;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;

class KnowledgeController extends AdminController
{
    use WithUploads;

    protected string $title = '知识库管理';

    public function grid(): Grid
    {
        return Grid::make(FastgptKnowledge::class, function (Grid $grid) {
            $grid->model()->with(['user'])->latest('id');
            $grid->disableCreateButton();
            $grid->disableActions();

            $grid->quickSearch(['name'])
                ->placeholder('标题');

            $grid->filter(function (Grid\Filter $filter) {
                $filter->like('name', '名称');
                $filter->like('user_id', '所属用户')
                    ->select()
                    ->ajax(route('admin.user.users.ajax'));
            });

            $grid->column('id', '#ID#');
            $grid->column('name', '标题');
            $grid->column('username', '所属用户')
                ->display(fn() => $this->user->show_name);
            $grid->column('level', '权限')
                ->using(FastgptKnowledgeLevelEnum::LEVEL_MAP)
                ->label(FastgptKnowledgeLevelEnum::LEVEL_LABEL);
            $grid->column('created_at');
        });
    }

}
