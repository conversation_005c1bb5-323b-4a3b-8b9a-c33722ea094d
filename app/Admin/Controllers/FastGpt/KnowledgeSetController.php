<?php

namespace App\Admin\Controllers\FastGpt;

use App\Admin\Renders\Fastgpt\SetInputData;
use App\Admin\Renders\Fastgpt\SetOutputData;
use App\Admin\Traits\WithUploads;
use App\Models\Enums\FastgptKnowledgeSetEnum;
use App\Models\FastgptKnowledge;
use App\Models\FastgptKnowledgeSet;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;

class KnowledgeSetController extends AdminController
{
    use WithUploads;

    protected string $title = '目录';

    public function grid(): Grid
    {
        return Grid::make(FastgptKnowledgeSet::class, function (Grid $grid) {
            $grid->model()
                ->with(['note', 'knowledge', 'parent', 'knowledgeArticle'])
                ->latest('id');
            $grid->disableCreateButton();
            $grid->disableActions();

            $grid->filter(function (Grid\Filter $filter) {
                $filter->like('name', '名称');
                $filter->equal('knowledge_id', '所属知识库')
                    ->select(FastgptKnowledge::all()->pluck('name', 'id'));
                $filter->equal('type', '类型')
                    ->select(FastgptKnowledgeSetEnum::SET_TYPE_MAP);
            });

            $grid->column('id', '#ID#');
            $grid->column('name', '名称');
            $grid->column('parent.name', '上级');
            $grid->column('knowledgeArticle.title', '关联文档');
            $grid->column('knowledge.name', '所属知识库');
            $grid->column('note.title', '小记名称');
            $grid->column('type', '类型')
                ->using(FastgptKnowledgeSetEnum::SET_TYPE_MAP)
                ->label(FastgptKnowledgeSetEnum::SET_TYPE_MAP_LABEL);

            $grid->column('input_data', '输入数据')
                ->display('详情')
                ->modal(SetInputData::make());
            $grid->column('output_data', '返回数据')
                ->display('详情')
                ->modal(SetOutputData::make());
            $grid->column('created_at');
        });
    }

}
