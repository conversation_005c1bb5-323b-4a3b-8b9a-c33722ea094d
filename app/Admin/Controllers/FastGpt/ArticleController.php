<?php

namespace App\Admin\Controllers\FastGpt;

use App\Admin\Traits\WithUploads;
use App\Models\FastgptKnowledgeArticle;
use App\Models\User;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;

class ArticleController extends AdminController
{
    use WithUploads;

    protected string $title = '文档';

    public function grid(): Grid
    {
        return Grid::make(FastgptKnowledgeArticle::class, function (Grid $grid) {
            $grid->model()
                ->with(['user', 'knowledgeSet.knowledge'])
                ->latest('id');
            $grid->disableCreateButton();

            $grid->quickSearch(['name'])
                ->placeholder('标题');

            $grid->filter(function (Grid\Filter $filter) {
                $filter->like('title', '名称');
                $filter->like('user_id', '所属用户')
                    ->select()
                    ->ajax(route('admin.user.users.ajax'));
            });

            $grid->actions(function (Grid\Displayers\Actions $actions) {
                $actions->disableDelete();
            });

            $grid->column('id', '#ID#');
            $grid->column('title', '标题');
            $grid->column('knowledgeSet.name', '关联集合');
            $grid->column('knowledge', '关联的知识库')
                ->display(function () {
                    return $this->knowledgeSet?->knowledge->name;
                });
            $grid->column('username', '所属用户')
                ->display(fn() => $this->user->show_name);
            $grid->column('created_at');
        });
    }

    public function form(): Form
    {
        return Form::make(FastgptKnowledgeArticle::with('user'), function (Form $form) {
            $form->text('title', '标题');
            $form->select('user_id', '隶属用户')
                ->options(function ($userId) {
                    if ($userId) {
                        return [$userId => User::find($userId)->showName];
                    } else {
                        return [];
                    }
                })
                ->ajax(route('admin.user.users.ajax'))
                ->load('tags', route('admin.note_tags.ajax'))
                ->required();
            $form->editor('content', '内容详情')->required();
        });
    }

}
