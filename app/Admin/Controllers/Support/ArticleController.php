<?php

namespace App\Admin\Controllers\Support;

use App\Admin\Traits\WithUploads;
use App\Enums\Bailian\BailianArticleTypeEnum;
use App\Models\BailianArticle;
use App\Models\BailianCategory;
use App\Models\BailianKnowledge;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;

class ArticleController extends AdminController
{
    use WithUploads;

    protected string $title = '帮助问题列表';

    public function grid(): Grid
    {
        return Grid::make(BailianArticle::class, function (Grid $grid) {
            $grid->model()
                ->whereNull('user_id')
                ->with(['knowledgeItem.baiLianKnowledge', 'storage', 'category'])
                ->latest('id');

            $grid->filter(function (Grid\Filter $filter) {
                $filter->like('title', '标题');
            });

            $grid->column('id', '#ID#');
            $grid->column('title', '标题');
            $grid->column('category.name', '分类名称');
            $grid->column('knowledge', '知识库')
                ->display(function () {
                    return $this->knowledgeItem->bailianKnowledge->name ?? '---';
                });
            $grid->column('size', '大小')
                ->display(fn() => $this->readable_size);
            $grid->column('created_at');
        });
    }

    public function form(): Form
    {
        return Form::make(BailianArticle::class, function (Form $form) {
            $form->text('title', '标题')->required();
            $form->select('category_id', '分类')
                ->options(function () {
                    return BailianCategory::where('type', BailianCategory::TYPE_HELP)->pluck('name', 'id');
                })
                ->required();

            $form->select('type', '笔记类型')
                ->options(BailianArticleTypeEnum::STATUS_MAP)
                ->required()
                ->when(BailianArticleTypeEnum::IMAGE->value, function (Form $form) {
                    $form->text('content.image', '图片');
                })
                ->when(BailianArticleTypeEnum::RECORD->value, function (Form $form) {
                    $form->text('content.audio', '音频');
                    $form->number('content.audioDuration', '音频时长(秒)');
                })
                ->when(BailianArticleTypeEnum::LINK->value, function (Form $form) {
                    $form->url('content.link', '链接地址');
                    $form->text('content.linkTitle', '链接标题');
                    $form->image('content.linkImage', '链接图标');
                });
            $form->markdown('content.data', '内容详情')
                ->options([
                    'htmlDecode'         => true,
                    'saveHTMLToTextarea' => false
                ]);
            $form->saving(function (Form $form) {
                $helpKnowledge = BailianKnowledge::where('type', BailianKnowledge::TYPE_HELP)->first();
                if (! $helpKnowledge) {
                    return $form->response()->error('请先创建帮助知识库');
                }

                $input = $form->input();
                $type  = $input['type'] ?? 'article';

                $content = BailianArticle::getContentDataByInput($type, $input);

                $form->content = $content;
            });

            $form->saved(function (Form $form) {
                $helpKnowledge = BailianKnowledge::where('type', BailianKnowledge::TYPE_HELP)->first();
                $id            = $form->getKey();
                $article       = BailianArticle::find($id);

                if ($form->isCreating()) {
                    $article->addToKnowledgeBase($helpKnowledge->id, $article->category->bailian_id);
                }
                if ($form->isEditing()) {
                    // 异步更新知识库
                    \App\Jobs\BaiLian\ArticleUpdateJob::dispatch($article);
                }
            });
            $form->deleting(function (Form $form) {
                $article = BailianArticle::find($form->getKey());
                if ($article) {
                    $article->removeFromKnowledge();
                }
            });
        });
    }
}
