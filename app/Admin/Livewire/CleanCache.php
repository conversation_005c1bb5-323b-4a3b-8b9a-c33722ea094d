<?php

namespace App\Admin\Livewire;

use Illuminate\Contracts\View\View;
use Illuminate\Support\Facades\Artisan;
use Livewire\Attributes\On;
use Livewire\Component;

class CleanCache extends Component
{
    public function render(): View
    {
        return view('admin.clean-cache');
    }

    #[On('clean-cache')]
    public function cleanCache(): void
    {
        Artisan::call('modelCache:clear');

        $this->dispatch('cache-cleaned', message: '缓存清除成功');
    }
}
