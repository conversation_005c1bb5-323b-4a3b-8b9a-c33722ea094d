<?php

namespace App\Admin\Renders\Fastgpt;

use App\Models\FastgptKnowledgeSet;
use Dcat\Admin\Support\LazyRenderable;
use Dcat\Admin\Widgets\Table;

class SetInputData extends LazyRenderable
{
    public function render(): string
    {
        $setId = $this->payload['key'];
        $set   = FastgptKnowledgeSet::find($setId);
        $data  = [
            [
                'name'         => $set->input_data['name'],
                'text'         => $set->input_data['text'] ?? '',
                'trainingType' => $set->input_data['trainingType'] ?? '',
            ]
        ];
        return Table::make(['名称', '内容', '训练方式'], $data)->render();
    }
}