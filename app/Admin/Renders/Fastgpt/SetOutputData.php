<?php

namespace App\Admin\Renders\Fastgpt;

use App\Models\FastgptKnowledgeSet;
use Dcat\Admin\Support\LazyRenderable;
use Dcat\Admin\Widgets\Box;
use Illuminate\Database\Eloquent\ModelNotFoundException;

class SetOutputData extends LazyRenderable
{
    public function render(): string
    {
        // 获取知识库集合 ID，并确保其为整数类型
        $setId = (int) $this->payload['key'];

        try {
            // 根据 ID 查找知识库集合
            $set = FastgptKnowledgeSet::findOrFail($setId); // 使用 findOrFail, 如果找不到会抛出异常

            $outputData = $set->output_result;

            // 检查 output_data 的类型
            if (is_array($outputData)) {
                // 如果 output_data 是一个数组，则将其转换为 JSON 字符串
                $outputData = json_encode($outputData, JSON_UNESCAPED_UNICODE);
            }

            $outputData = '<pre>'.htmlspecialchars($outputData).'</pre>';

            $boxPub = new Box(
                '返回数据',
                $outputData
            );

            return $boxPub->render();
        } catch (ModelNotFoundException $e) {
            return '<div class="alert alert-danger">知识库集合未找到. ID: '.$setId.'</div>';
        } catch (\Exception $e) {
            return '<div class="alert alert-danger">发生错误: '.$e->getMessage().'</div>';
        }
    }
}
