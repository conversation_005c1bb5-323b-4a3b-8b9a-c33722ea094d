<?php

namespace App\Admin\Renders\Vidu;

use App\Models\PluginViduTemplate;
use Dcat\Admin\Support\LazyRenderable;
use Dcat\Admin\Widgets\Table;

class DetailData extends LazyRenderable
{
    public function render(): string
    {
        $id     = $this->payload['key'];
        $set    = PluginViduTemplate::find($id);
        $detail = $set->getDetail();
        $data   = [
            [
                'scene'        => $detail['scene'],
                'duration'     => $detail['duration'],
                'resolution'   => $detail['resolution'],
                'credit_cost'  => $detail['credit_cost'],
                'aspect_ratio' => $detail['aspect_ratio'],
            ]
        ];
        return Table::make(['调用参数', '时长', '分辨率', '消耗的积分', '比例'], $data)->render();
    }
}