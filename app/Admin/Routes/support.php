<?php

use App\Admin\Controllers\Support\ArticleController;
use App\Admin\Controllers\Support\FeedbackController;
use App\Admin\Controllers\Support\FileController;
use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Route;

Route::group([
    'prefix' => 'support',
], function (Router $router) {
    $router->resource('help_articles', ArticleController::class);
    $router->resource('help_feedbacks', FeedbackController::class);
    $router->resource('help_files', FileController::class);
});