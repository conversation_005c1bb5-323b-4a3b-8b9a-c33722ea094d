<?php

use App\Admin\Controllers\FastGpt\AppController;
use App\Admin\Controllers\FastGpt\ArticleController;
use App\Admin\Controllers\FastGpt\KnowledgeController;
use App\Admin\Controllers\FastGpt\KnowledgeSetController;
use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Route;

Route::group([
    'namespace' => 'FastGpt',
    'prefix'    => 'fastgpt'
], function (Router $router) {
    $router->resource('apps', AppController::class);
    $router->resource('knowledges', KnowledgeController::class);
    $router->resource('sets', KnowledgeSetController::class);
    $router->resource('articles', ArticleController::class);
});
