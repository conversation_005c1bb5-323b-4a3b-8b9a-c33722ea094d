<?php

use App\Admin\Controllers\Publish\CategoryController;
use App\Admin\Controllers\Publish\MediumController;
use App\Admin\Controllers\Publish\TagsController;
use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Route;

Route::group([
    'prefix' => 'publish',
], function (Router $router) {
    $router->resource('category', CategoryController::class);
    $router->resource('tags', TagsController::class);
    $router->resource('index', MediumController::class);
});