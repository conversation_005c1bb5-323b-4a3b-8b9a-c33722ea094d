<?php

namespace App\Jobs\Draw;

use App\Jobs\BaseJob;
use App\Models\DrawImage;

class DrawImageJob extends BaseJob
{
    public string $queue = 'DrawImage';

    public function __construct(protected DrawImage $drawImage)
    {
    }

    public function handle(): void
    {
        $user = $this->drawImage->user;
        if ($user->account->score <= $this->drawImage->score) {
            $this->fail('积分余额不足');
        }
        $user->aiSpend('create_work', $this->drawImage, -$this->drawImage->score, [
            'remark' => '绘制图片【'.$this->drawImage->prompt.'】',
        ]);
        if ($this->drawImage->canDraw()) {
            $this->drawImage->setIng();
            $this->drawImage->draw();
        } else {
            $this->fail('该任务已完成或正在进行中');
        }
    }
}