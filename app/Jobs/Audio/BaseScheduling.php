<?php

namespace App\Jobs\Audio;

use App\Jobs\BaseJob;
use App\Models\DrawAudio;
use Illuminate\Contracts\Queue\ShouldBeUnique;

class BaseScheduling extends BaseJob implements ShouldBeUnique
{
    public string $queue = 'AudioScheduling';

    public function handle(): void
    {
        $normalCount = DrawAudio::ofIng()
            ->count();

        if ($normalCount < 5) {
            DrawAudio::ofInit()
                ->orderBy('created_at')
                ->limit(5 - $normalCount)
                ->chunkById(5, function ($audios) {
                    foreach ($audios as $audio) {
                        $audio->draw();
                    }
                });
        }
//        AudioSinger::ofInit()
//            ->orderBy('created_at')
//            ->chunkById(5, function ($singers) {
//                foreach ($singers as $singer) {
//                    $singer->draw();
//                }
//            });
    }
}