<?php

namespace App\Jobs;

use App\Models\Model;

class DoExecJob extends BaseJob
{
    public int $timeout = 120;

    public function __construct(protected Model $model, protected string $callFun, protected array $params = [])
    {
    }

    public function handle(): void
    {
        $callFun = $this->callFun;
        if (method_exists($this->model, $callFun)) {
            call_user_func([$this->model, $callFun], ...$this->params);
        }
    }
}