<?php

namespace App\Jobs\OfflineTask;

use App\Jobs\BaseJob;
use App\Models\OfflineTask;
use App\OfflineTask\OfflineTaskKernel;

class OfflineTaskRunJob extends BaseJob
{
    public string $queue   = 'OfflineTask';
    public int    $timeout = 0;

    public function __construct(public OfflineTask $task)
    {
    }

    public function handle(): void
    {
        $kernel = new OfflineTaskKernel($this->task->command);
        $kernel->run($this->task);
    }
}