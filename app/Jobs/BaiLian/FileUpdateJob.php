<?php

namespace App\Jobs\BaiLian;

use App\Jobs\BaseJob;
use App\Models\BailianFile;
use App\Services\KnowledgeItemStrategies\KnowledgeItemStrategyFactory;
use Exception;
use Illuminate\Support\Facades\Log;

class FileUpdateJob extends BaseJob
{
    public string $queue   = 'BaiLian';
    public int    $timeout = 120;
    public int    $tries   = 3; // 尝试次数
    public int    $backoff = 5; // 失败后等待时间（秒）

    /**
     * 创建一个新的任务实例
     *
     * @param  \App\Models\BailianFile  $file
     */
    public function __construct(protected BailianFile $file)
    {
    }

    /**
     * 执行任务
     *
     * @return void
     * @throws \Exception
     */
    public function handle(): void
    {
        try {
            $file = $this->file;

            // 检查文件是否在知识库中
            if (! $file->isInKnowledge()) {
                Log::info('文件不在知识库中，跳过更新', [
                    'file_id' => $file->id
                ]);
                return;
            }

            // 使用优化的异步同步流程
            $strategy = KnowledgeItemStrategyFactory::createForModel($file);
            $strategy->handleContentUpdate($file);
        } catch (Exception $e) {
            Log::error('文件更新任务失败', [
                'file_id' => $this->file->id,
                'error'   => $e->getMessage()
            ]);
            // 重新抛出异常，触发重试机制
            throw $e;
        }
    }

    /**
     * 处理任务失败
     *
     * @param  \Exception  $exception
     * @return void
     */
    public function failed(Exception $exception): void
    {
        Log::error('更新文档任务最终失败', [
            'file_id' => $this->file->id,
            'error'   => $exception->getMessage()
        ]);
    }
}