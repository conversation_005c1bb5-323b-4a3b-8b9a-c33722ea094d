<?php

namespace App\Jobs\BaiLian;

use App\Jobs\BaseJob;
use App\Models\BailianKnowledgeItem;
use App\Packages\BailianAssistant\BLAssistant;
use Exception;
use Illuminate\Support\Facades\Log;

class DeleteKnowledgeJob extends BaseJob
{
    public string $queue   = 'BaiLian';
    public int    $timeout = 120;
    public int    $tries   = 3; // 尝试次数
    public int    $backoff = 5; // 失败后等待时间（秒）

    /**
     * 知识库ID
     *
     * @var int
     */
    protected int $knowledgeId;

    /**
     * 百炼知识库ID
     *
     * @var string|null
     */
    protected ?string $bailianKnowledgeId;

    /**
     * 工作空间ID
     *
     * @var string|null
     */
    protected ?string $workspaceId;

    /**
     * 创建一个新的任务实例
     *
     * @param  \App\Models\BailianKnowledge  $knowledge  知识库模型
     */
    public function __construct(int $knowledgeId, string $bailianKnowledgeId, string $workspaceId)
    {
        $this->knowledgeId        = $knowledgeId;
        $this->bailianKnowledgeId = $bailianKnowledgeId;
        $this->workspaceId        = $workspaceId;
    }

    /**
     * 执行任务
     *
     * @return void
     * @throws \Exception
     */
    public function handle(): void
    {
        try {
            if ($this->bailianKnowledgeId && $this->workspaceId) {
                $bailian = new BLAssistant();
                $result  = $bailian->knowledge()->deleteIndex($this->workspaceId, $this->bailianKnowledgeId);

                if (! $result->success) {
                    Log::error('删除知识库失败', [
                        'knowledge_id'         => $this->knowledgeId,
                        'bailian_knowledge_id' => $this->bailianKnowledgeId,
                        'error'                => $result->message ?? ''
                    ]);
                }
            }
        } catch (Exception $e) {
            Log::error('删除知识库异常', [
                'knowledge_id' => $this->knowledgeId,
                'error'        => $e->getMessage(),
                'trace'        => $e->getTraceAsString()
            ]);
        }

        try {
            // 获取所有知识库项目，逐个触发清理任务
            $knowledgeItems = BailianKnowledgeItem::where('knowledge_id', $this->knowledgeId)->get();

            foreach ($knowledgeItems as $item) {
                DeleteKnowledgeItemJob::dispatch([
                    'itemable_type' => $item->itemable_type,
                    'itemable_id'   => $item->itemable_id,
                ], false);
            }

            // 删除知识库项目记录
            BailianKnowledgeItem::where('knowledge_id', $this->knowledgeId)->delete();
        } catch (Exception $e) {
            Log::error('删除知识库资源和文件异常', [
                'knowledge_id' => $this->knowledgeId,
                'error'        => $e->getMessage(),
                'trace'        => $e->getTraceAsString()
            ]);

            // 重新抛出异常，触发重试机制
            throw $e;
        }
    }

}