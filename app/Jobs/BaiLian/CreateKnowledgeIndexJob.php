<?php

namespace App\Jobs\BaiLian;

use App\Jobs\BaseJob;
use App\Models\BailianKnowledge;
use App\Packages\BailianAssistant\BLAssistant;
use Exception;
use Illuminate\Support\Facades\Log;

class CreateKnowledgeIndexJob extends BaseJob
{
    public string $queue   = 'BaiLian';
    public int    $timeout = 120;
    public int    $tries   = 3; // 尝试次数
    public int    $backoff = 5; // 失败后等待时间（秒）

    /**
     * 创建一个新的任务实例
     *
     * @param  \App\Models\BailianKnowledge  $knowledge  知识库模型
     */
    public function __construct(protected BailianKnowledge $knowledge)
    {
    }

    /**
     * 执行任务
     *
     * @return void
     * @throws \Exception
     */
    public function handle(): void
    {
        try {
            // 重新获取知识库模型，确保数据是最新的
            $knowledge = BailianKnowledge::find($this->knowledge->id);

            if (! $knowledge) {
                Log::error('知识库不存在', ['knowledge_id' => $this->knowledge->id]);
                return;
            }

            // 创建百炼助手实例
            $bailian = new BLAssistant();

            // 创建知识库索引
            $result = $bailian->knowledge()->createIndex($knowledge);
            if ($result->success) {
                // 更新知识库的knowledge_id
                $knowledge->update([
                    'knowledge_id' => $result->data->id,
                ]);

                // 提交索引任务
                $submitResult = $bailian->knowledge()->submitIndexJob($result->data->id, $knowledge->workspace_id);

                if ($submitResult->success) {
                    $config            = $knowledge->config;
                    $config['job_id']  = $submitResult->data->id;
                    $config['indexId'] = $submitResult->data->indexId;
                    $knowledge->update([
                        'config'        => $config,
                        'error_message' => null
                    ]);
                }
            } else {
                Log::error('创建知识库失败', [
                    'knowledge_id' => $knowledge->id,
                    'error'        => $result->message
                ]);
                $knowledge->update([
                    'error_message' => $result->message
                ]);
                throw new Exception($result->message);
            }
        } catch (Exception $e) {
            Log::error('创建知识库异常', [
                'knowledge_id' => $this->knowledge->id,
                'error'        => $e->getMessage(),
                'trace'        => $e->getTraceAsString()
            ]);

            // 重新抛出异常，触发重试机制
            throw $e;
        }
    }

    /**
     * 处理任务失败
     *
     * @param  \Exception  $exception
     * @return void
     */
    public function failed(Exception $exception): void
    {
        Log::error('创建知识库任务最终失败', [
            'knowledge_id' => $this->knowledge->id,
            'error'        => $exception->getMessage()
        ]);
    }
}