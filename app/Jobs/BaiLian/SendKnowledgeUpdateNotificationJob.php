<?php

namespace App\Jobs\BaiLian;

use App\Jobs\BaseJob;
use App\Models\BailianKnowledge;
use App\Models\User;
use App\Notifications\KnowledgeContentUpdatedNotification;
use Illuminate\Support\Facades\Log;

class SendKnowledgeUpdateNotificationJob extends BaseJob
{
    public string $queue         = 'BaiLian';
    public int    $tries         = 3;
    public int    $maxExceptions = 3;
    public int    $timeout       = 60;

    protected int    $userId;
    protected int    $knowledgeId;
    protected string $updateType;
    protected string $itemTitle;

    /**
     * 创建新的任务实例
     *
     * @param  int  $userId  接收通知的用户ID
     * @param  int  $knowledgeId  知识库ID
     * @param  string  $updateType  更新类型
     * @param  string  $itemTitle  项目标题
     * @param  int  $updaterId  更新者ID
     */
    public function __construct(int $userId, int $knowledgeId, string $updateType, string $itemTitle)
    {
        $this->userId      = $userId;
        $this->knowledgeId = $knowledgeId;
        $this->updateType  = $updateType;
        $this->itemTitle   = $itemTitle;
    }

    /**
     * 执行任务
     *
     * @return void
     * @throws \Exception
     */
    public function handle(): void
    {
        try {
            // 获取用户
            $user = User::find($this->userId);
            if (! $user) {
                return;
            }

            // 获取知识库
            $knowledge = BailianKnowledge::find($this->knowledgeId);
            if (! $knowledge) {
                return;
            }

            // 发送通知
            $user->notify(new KnowledgeContentUpdatedNotification(
                $knowledge,
                $this->updateType,
                $this->itemTitle
            ));
        } catch (\Exception $e) {
            throw $e; // 重新抛出异常以触发重试
        }
    }

    /**
     * 处理失败的任务
     *
     * @param  \Exception  $exception
     * @return void
     */
    public function failed(\Exception $exception): void
    {
        Log::error('知识库更新通知发送失败', [
            'user_id'      => $this->userId,
            'knowledge_id' => $this->knowledgeId,
            'update_type'  => $this->updateType,
            'item_title'   => $this->itemTitle,
            'error'        => $exception->getMessage()
        ]);
    }
}
