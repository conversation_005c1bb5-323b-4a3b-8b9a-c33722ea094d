<?php

namespace App\Traits;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\MorphTo;

trait MorphToUser
{
    /**
     * Notes   : 操作用户
     *
     * @Date   : 2023/3/30 15:04
     * <AUTHOR> <Jason.C>
     * @return \Illuminate\Database\Eloquent\Relations\MorphTo
     */
    public function user(): MorphTo
    {
        return $this->morphTo();
    }

    public function setUserAttribute(Model $user): void
    {
        $this->attributes['user_type'] = $user->getMorphClass();
        $this->attributes['user_id']   = $user->getKey();
    }
}