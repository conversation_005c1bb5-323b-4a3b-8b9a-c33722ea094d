<?php

namespace App\Traits;

use Illuminate\Database\Eloquent\Relations\Relation;

trait NoticeTrait
{
    public static function getResource($model)
    {
        $resource = null;
        if (isset($model['id']) && isset($model['type'])) {
            $modelInfo = Relation::getMorphedModel($model['type'])::find($model['id']);
            if ($modelInfo) {
                $resource = $modelInfo->getAssetResource();
            }
        }
        return $resource;
    }
}
