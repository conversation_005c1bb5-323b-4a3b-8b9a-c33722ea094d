<?php

namespace App\Traits;

use App\Models\Contact;

trait BusinessTrait
{
    public function findLink($uid, $bid, $dst_id, $default_id = "")
    {
        $contact_to = Contact::where('dstid', $dst_id)
            ->where('from_bid', $bid)
            ->first();
        if ($contact_to) {
            if ($contact_to->status == 0) {
                //等待我方同意
                return 3;
            } else {
                //建立联系
                return 2;
            }
        }

        $contact = Contact::where('dstid', $uid)
            ->where('to_bid', $bid)
            ->where('uid', $dst_id)
//                ->where('from_bid', $default['id'])
            ->first();
        if ($contact) {
            if ($contact->status == 0) {
                //等待对方同意
                return 1;
            } else {
                return 2;
            }
        }

        return 0;
    }
}
