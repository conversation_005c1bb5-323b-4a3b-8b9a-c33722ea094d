<?php

namespace App\Traits;

use App\Exceptions\ValidatorException;
use App\Models\CompanyStaff;

trait CompanyTraits
{
    public function checkCompany($company)
    {
        if (! $company) {
            throw new ValidatorException("企业不存在");
        }

        if ($company->is_check == 0) {
            throw new ValidatorException("该企业还未通过审核");
        }

        if ($company->is_check == 2) {
            throw new ValidatorException("该企业未通过审核，请重新认证");
        }
    }

    public function getTypeName($type)
    {
        switch ($type) {
            case 0:
                $type_name = "创始人";
            case 1:
                $type_name = "邀请码加入";
                break;
            case 2:
                $type_name = "工牌加入";
                break;
            case 3:
                $type_name = "名片加入";
                break;
            case 4:
                $type_name = "营业执照加入";
                break;
            case 5:
                $type_name = "系统录入";
                break;
            default:
                $type_name = "其他方式";
                break;
        }
        return $type_name;
    }

    public function getIsManageOfCompanyStaff($userId, $company_id)
    {
        return CompanyStaff::where('uid', $userId)
            ->where('company_id', $company_id)
            ->where('is_work', 0)
            ->value('is_manage');
    }

}
