<?php

namespace App\Contracts;

use Dcat\Admin\Traits\HasDateTimeFormatter;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Foundation\Auth\User;
use Illuminate\Notifications\Notifiable;
use Laravel\Sanctum\HasApiTokens;

abstract class AuthenticateUser extends User
{
    use HasApiTokens,
        HasDateTimeFormatter,
        Notifiable,
        SoftDeletes;

    protected $hidden = [
        'password',
    ];

    abstract public function getMobile(): string;

    abstract public function showName(): Attribute;
}