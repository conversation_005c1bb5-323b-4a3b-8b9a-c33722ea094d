<?php

namespace App\Providers;

use App\Events\CommentLikeEvent;
use App\Events\KnowledgeContentUpdatedEvent;
use App\Events\KnowledgeItemCreatedEvent;
use App\Events\LikeCreatedEvent;
use App\Events\PluginJmDrawStatusChangedEvent;
use App\Events\RechargeOrderPaidEvent;
use App\Events\UserCreatedEvent;
use App\Listeners\Bailian\KnowledgeContentUpdatedListener;
use App\Listeners\Bailian\KnowledgeItemCreatedListener;
use App\Listeners\Comment\CommentLikeListener;
use App\Listeners\IdentityOrderPaidListener;
use App\Listeners\Like\LikeCreatedListener;
use App\Listeners\Plugin\PluginJmDrawStatusChangedListener;
use App\Listeners\Recharge\OrderPaidListener;
use App\Listeners\User\ImportUserDefaultArticleListener;
use App\Listeners\User\ImportUserToImListener;
use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;
use Modules\User\Events\IdentityOrderPaid;

class EventServiceProvider extends ServiceProvider
{
    protected $listen = [
        RechargeOrderPaidEvent::class         => [
            OrderPaidListener::class
        ],
        IdentityOrderPaid::class              => [
            IdentityOrderPaidListener::class
        ],
        LikeCreatedEvent::class               => [
            LikeCreatedListener::class
        ],
        UserCreatedEvent::class               => [
            ImportUserToImListener::class,
            ImportUserDefaultArticleListener::class
        ],
        CommentLikeEvent::class               => [
            CommentLikeListener::class
        ],
        PluginJmDrawStatusChangedEvent::class => [
            PluginJmDrawStatusChangedListener::class,
        ],
        KnowledgeContentUpdatedEvent::class   => [
            KnowledgeContentUpdatedListener::class,
        ],
        KnowledgeItemCreatedEvent::class      => [
            KnowledgeItemCreatedListener::class,
        ],
    ];

    public function boot(): void
    {
    }

    public function shouldDiscoverEvents(): bool
    {
        return false;
    }
}
