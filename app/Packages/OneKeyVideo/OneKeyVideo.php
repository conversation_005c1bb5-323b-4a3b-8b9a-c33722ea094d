<?php

namespace App\Packages\OneKeyVideo;

use App\Packages\OneKeyVideo\BaseClient;
use App\Packages\OneKeyVideo\Client\Images;
use App\Packages\OneKeyVideo\Client\Video;
use App\Packages\OneKeyVideo\Client\Jimeng;

class OneKeyVideo extends BaseClient
{
    public static function Images(): Images
    {
        return new Images();
    }
    public static function Video(): Video
    {
        return new Video();
    }
    public static function Jimeng(): Jimeng
    {
        return new Jimeng();
    }
}
