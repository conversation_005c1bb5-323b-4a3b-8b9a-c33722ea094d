<?php
namespace App\Packages\OneKeyVideo\Client;

use App\Exceptions\ValidatorException;
use App\Packages\OneKeyVideo\BaseClient;
use Exception;
use Illuminate\Support\Facades\Log;

class Video extends BaseClient
{

    public array $headers = [
        'Authorization' =>  'Bearer **************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************',
        'Content-Type'  =>  'application/json'
    ];

    public function createVideo(string $prompt, string $first_frame_image)
    {
        $url = 'https://api.minimax.chat/v1/video_generation';
        $query = [
            'model' =>  'video-01',
            'prompt'    =>  $prompt,
            'first_frame_image' =>  $first_frame_image
        ];
        try {
            return $this->setParams($query)->setHeaders($this->headers)->post($url);
        } catch (Exception $exception) {
            Log::info($exception->getMessage());
            throw new ValidatorException($exception->getMessage());
        }
    }

    public function lunxun($tackID = '222650267369614')
    {
        $url = 'https://api.minimax.chat/v1/query/video_generation';
        try {
            return $this->setParams(['task_id'=>$tackID])->setHeaders($this->headers)->get($url);
        } catch (Exception $exception) {
            Log::info($exception->getMessage());
            throw new ValidatorException($exception->getMessage());
        }
    }

    public function download($FileID){
        $url = "https://api.minimax.chat/v1/files/retrieve";
        try {
            return $this->setParams(['file_id'=>$FileID])->setHeaders($this->headers)->get($url);
        } catch (Exception $exception) {
            Log::info($exception->getMessage());
            throw new ValidatorException($exception->getMessage());
        }
    }
}
