<?php

namespace App\Packages\WateAiChat\Client;

use App\Models\AiChatConfig;
use App\Models\ChatGroup;
use App\Models\User;
use App\Packages\WateAiChat\BaseClient;
use App\Packages\WateAiChat\Functions\FunctionLoad;
use App\Packages\WateAiChat\Interfaces\ChatInterface;
use App\Packages\WateAiChat\StreamResponse;
use App\Packages\WateAiChat\Traits\OpenAiTrait;
use Illuminate\Support\Str;

class ZhipuClient extends BaseClient implements ChatInterface
{
    use OpenAiTrait;

    protected bool   $reasoning  = false;
    protected bool   $runStop    = false;
    protected string $tempString = '';

    public function __construct(AiChatConfig $config, ChatGroup $group, User $user)
    {
        $this->config = $config;
        $this->engine = $config->engine;
        $this->group  = $group;
        $this->user   = $user;
    }

    public function checkText(StreamResponse $response)
    {
        foreach ($response->getIterator() as $chunk) {
            if ($chunk == 'DONE') {
                if ($this->functionKey) {
                    $loads         = new FunctionLoad();
                    $functions     = $loads->getLoads();
                    $functionClass = $functions[$this->functionKey] ?? '';
                    if ($functionClass) {
                        $this->function = new $functionClass();
                        $this->function
                            ->setClient($this)
                            ->doFunction($this->functionParamsString);
                        continue;
                    }
                }
                break;
            }

            $tokens = $chunk['usage']['total_tokens'] ?? 0;
            if ($tokens > 0) {
                $completion_tokens = $chunk['usage']['completion_tokens'] ?? 0;
                $prompt_tokens     = $chunk['usage']['prompt_tokens'] ?? 0;
                $response->setTokens($tokens, $prompt_tokens, $completion_tokens);
            }

            $delta         = $chunk['choices'][0]['delta'] ?? [];
            $toolsFunction = $delta['tool_calls'] ?? [];
            if (count($toolsFunction) > 0) {
                $callFunction = $toolsFunction[0]['function'] ?? [];
                if (count($callFunction) > 0) {
                    $name = $callFunction['name'] ?? '';
                    if ($name && blank($this->functionKey)) {
                        $this->functionKey = $name;
                    }
                    $arguments = $callFunction['arguments'] ?? '';
                    if ($arguments) {
                        $this->functionParamsString .= $arguments;
                    }
                    continue;
                }
            }

            $message = $delta['content'] ?? null;
            if (Str::contains($message, '<th')) {
                $this->tempString = '<th';
                $this->runStop    = true;
                continue;
            }
            if (Str::contains($message, '</')) {
                $this->tempString = '</';
                $this->runStop    = true;
                continue;
            }
            if ($this->runStop) {
                if ($this->runStop && Str::contains($message, 'think')) {
                    $this->tempString .= $message;
                    continue;
                }
                if ($this->runStop && Str::contains($message, 'ink')) {
                    $this->tempString .= $message;
                    continue;
                }
                if ($this->runStop && Str::contains($message, ">")) {
                    $this->tempString .= '>';
                    $message          = Str::afterLast($message, '>');
                }
                if ($this->runStop && $this->tempString == '<think>') {
                    $this->reasoning = true;
                    $this->runStop   = false;
                }
                if ($this->runStop && $this->tempString == '</think>') {
                    $this->reasoning = false;
                    $this->runStop   = false;
                }
            }
            if ($this->reasoning) {
                $response->pushReasoning($message);
                $this->sendReasoning($message);
            } else {
                $response->pushMessage($message);
                $this->sendText($message);
            }
        }
    }

}