<?php

namespace App\Packages\WateAiChat\Functions;

use App\Packages\WateAiChat\Functions\Event\GenImageFunction;
use App\Packages\WateAiChat\Functions\Event\GenVideoFunction;

class FunctionLoad
{
    protected $functions = [
        'gen_image' => GenImageFunction::class,
        'gen_video' => GenVideoFunction::class,
    ];

    public function getLoads()
    {
        return $this->functions;
    }
}