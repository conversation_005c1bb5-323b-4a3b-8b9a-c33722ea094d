<?php

namespace App\Packages\WateAiChat;

use App\Models\ChatGroup;
use App\Models\User;
use App\Packages\Suno\Exceptions\StreamException;
use App\Packages\WateAiChat\Functions\BaseFunction;
use Closure;
use Psr\Http\Message\ResponseInterface;
use Psr\Http\Message\StreamInterface;

class StreamResponse
{
    protected string $responseMessage   = '';
    protected int    $responseTokens    = 0;
    protected int    $inputToken        = 0;
    protected int    $outputToken       = 0;
    protected string $responseReasoning = '';
    protected array  $search            = [];

    public function __construct(
        protected ResponseInterface $response,
        protected array $params,
        protected string $message,
        protected array $imageUrl
    ) {
    }

    public function pushMessage(string $message)
    {
        $this->responseMessage .= $message;
    }

    public function setTokens(int $tokens, int $input, int $output)
    {
        $this->responseTokens = $tokens;
        $this->inputToken     = $input;
        $this->outputToken    = $output;
    }

    public function pushReasoning(string $reasoning)
    {
        $this->responseReasoning .= $reasoning;
    }

    public function emptySearch()
    {
        return empty($this->search);
    }

    public function setSearch(array $search, Closure $mapCall)
    {
        $this->search = collect($search)->map($mapCall)->values()->toArray();
        return $this->search;
    }

    public function getIterator()
    {
        while (! $this->response->getBody()->eof()) {
            $line = $this->readLine($this->response->getBody());
            if (! str_starts_with($line, 'data:')) {
                continue;
            }
            $data = trim(substr($line, strlen('data:')));
            if ($data === '[DONE]') {
                yield 'DONE';
                break;
            }
            $response = json_decode($data, true);
            if (isset($response['error'])) {
                throw new StreamException($response['error'],
                    $this->response->getStatusCode());
            }

            yield $response;
        }
    }

    private function readLine(StreamInterface $stream): string
    {
        $buffer = '';

        while (! $stream->eof()) {
            if ('' === ($byte = $stream->read(1))) {
                return $buffer;
            }
            $buffer .= $byte;
            if ($byte === "\n") {
                break;
            }
        }

        return $buffer;
    }

    public function finish(
        ChatGroup $group,
        User $user,
        string $channel,
        string $engine,
        ?BaseFunction $function = null
    ) {
        $taskParams = $function?->getTaskParams() ?: [];
        $log        = $group->logs()->create([
            'user_id'     => $user->id,
            'channel'     => $channel,
            'engine'      => $engine,
            'message'     => $this->message,
            'images'      => $this->imageUrl,
            'response'    => $this->responseMessage,
            'reasoning'   => $this->responseReasoning,
            'input_token' => $this->inputToken,
            'out_token'   => $this->outputToken,
            'tokens'      => $this->responseTokens,
            'task_params' => $taskParams['task'] ?? null,
            'asset_no'    => $taskParams['asset'] ?? null,
        ]);
        if ($this->hasSearch()) {
            $log->search()->createMany($this->search);
        }
        return $log;
    }

    public function hasSearch(): bool
    {
        return ! empty($this->search);
    }
}