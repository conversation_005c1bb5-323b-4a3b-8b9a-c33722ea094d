<?php

namespace App\Packages\WateAiChat\Tools;

use App\Models\ChatLog;

class SuggestTool extends BaseTool
{
    public function overDo(array $data)
    {
        $choices     = $data['choices'][0];
        $suggestions = json_decode($choices['message']['content'] ?: [], true);
        $this->sendSuggest($suggestions);
    }

    public function setCustParams(ChatLog $log): self
    {
        $data               = [
            [
                'role'    => 'user',
                'content' => $log->message,
            ],
            [
                'role'    => 'assistant',
                'content' => $log->response,
            ],
            [
                'role'    => 'user',
                'content' => '你要完成的任务是根据用户的问题和AI的回答，给出三个意图上可能继续提问的问题推荐给用户，单个问题不要超过20个字。并且已json的格式告诉我。JSON格式如下：{"suggestions":["问题1","问题2","问题3"]}'
            ],
        ];
        $this->customParams = [
            'messages'        => $data,
            'model'           => $this->engine->name,
            'max_tokens'      => (int) $this->engine->maxout,
            'stream'          => false,
            'response_format' => [
                'type' => 'json_object'
            ]
        ];
        return $this;
    }

}