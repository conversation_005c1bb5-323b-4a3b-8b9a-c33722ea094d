<?php

namespace App\Packages\Coze\Client;

use Exception;
use GuzzleHttp\Client;
use Illuminate\Support\Str;
use OpenAI;

class OpenAiClient
{
    protected string $baseUri = 'https://dashscope.aliyuncs.com/compatible-mode/v1/';
    protected string $apiKey;
    protected string $model   = '';
    protected string $prompt  = '';
    protected string $system  = '';
    protected array  $data    = [];
    protected string $message = '';
    protected bool   $status  = false;

    /**
     * config:
     *   baseUri - 大模型openAi基础地址
     *
     * @param  array  $config
     */
    public function __construct(array $config = [])
    {
        $this->baseUri = $config['baseUri'] ?? env('COZE_AI_BASEURI', '');
        $this->apiKey  = $config['apiKey'] ?? env('COZE_AI_APIKEY', '');
        $this->model   = $config['model'] ?? env('COZE_AI_MODEL', '');
    }

    public function chat(): void
    {
        $params = [
            'model'         => $this->model,
            'messages'      => [
                [
                    'role'    => 'system',
                    'content' => $this->system,
                ],
                [
                    'role'    => 'user',
                    'content' => $this->prompt,
                ],
            ],
            'max_tokens'    => 2048,
            'enable_search' => true,
        ];
        try {
            $client = $this->getClient();

            $response = $client->chat()->create($params);
            $content  = $response->choices[0]->message->content;
            if (Str::isJson($content)) {
                $content = json_decode($content, true);
            } else {
                $content = [
                    'content' => $content,
                ];
            }
            $this->success($content);
        } catch (Exception $exception) {
            $this->error($exception->getMessage());
        }
    }

    private function getClient()
    {
        return OpenAI::factory()
            ->withApiKey($this->apiKey)
            ->withBaseUri($this->baseUri)
            ->withHttpClient(new Client([
                'verify' => false,
            ]))
            ->make();
    }

    protected function success(array $data = [], string $message = 'success')
    {
        $this->status  = true;
        $this->data    = $data;
        $this->message = $message;
    }

    protected function error(string $message)
    {
        $this->status  = false;
        $this->data    = [];
        $this->message = $message;
    }

    public function getMessage(): string
    {
        return $this->message;
    }

    public function getStatus(): bool
    {
        return $this->status;
    }

    public function getData(): array
    {
        return $this->data;
    }

    public function setPrompt(string $prompt): self
    {
        $this->prompt = $prompt;
        return $this;
    }

    public function setSystem(string $system): self
    {
        $this->system = $system;
        return $this;
    }

    public function setApiKey(string $apiKey): self
    {
        $this->apiKey = $apiKey;
        return $this;
    }

    public function setBaseUri(string $baseUri): self
    {
        $this->baseUri = $baseUri;
        return $this;
    }

    public function setModel(string $model): self
    {
        $this->model = $model;
        return $this;
    }
}