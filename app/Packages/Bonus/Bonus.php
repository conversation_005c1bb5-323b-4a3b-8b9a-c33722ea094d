<?php

namespace App\Packages\Bonus;

use App\Models\RechargeOrder;
use App\Packages\Bonus\Model\Identity;
use App\Packages\Bonus\Model\Recharge;
use Modules\User\Models\IdentityOrder;

class Bonus
{
    public static function Recharge(RechargeOrder $order)
    {
        return new Recharge($order);
    }

    public static function Identity(IdentityOrder $order)
    {
        return new Identity($order);
    }
}