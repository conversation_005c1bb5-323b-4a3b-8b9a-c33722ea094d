<?php

namespace App\Packages\Assistant\Assistant;

use App\Models\ChatAssistantLog;
use App\Packages\Assistant\Assistant;
use App\Packages\Assistant\ChatTools\ChatToolsProvider;
use App\Packages\Assistant\EchoSSETrait;

class Kernel
{
    use EchoSSETrait;

    protected int   $step      = 0;
    protected array $workflows = [];

    public function __construct(protected ChatAssistantLog $log)
    {
    }

    public function run()
    {
        $this->sendStepRemark("开始筛选工具分类\n");
        $this->checkIntention();
        if ($this->log->intent) {
            $toolsClasses = [];
            foreach ($this->log->intent as $workflow) {
                $toolsClasses = array_merge($toolsClasses, ChatToolsProvider::getToolClasses($workflow));
            }
            $toolsPrompt = '';
            foreach ($toolsClasses as $toolClass) {
                $tool = new $toolClass();
                if (method_exists($tool, 'getPrompt')) {
                    $toolsPrompt .= json_encode($tool->getPrompt(), JSON_UNESCAPED_UNICODE)."\n";
                }
            }
            $this->checkWorkflow($toolsPrompt);
        }
    }

    protected function checkIntention()
    {
        $workflows = Assistant::intention()->getIntention($this->log->message);
        if (! blank($workflows)) {
            $this->log->intent = $workflows;
        }
        $this->log->save();
    }

    protected function checkWorkflow(string $toolsPrompt)
    {
        $this->sendStepRemark("开始分析任务流程\n");
        $this->workflows = Assistant::intention()->getTask($this->log->message, $toolsPrompt);

        $this->log->update([
            'tools' => $this->workflows,
        ]);
        $this->sendStepRemark("开始执行任务\n");
        if ($this->workflows['tool_name'] ?? '') {
            $task = $this->workflows;
            while ($task['tool_name'] ?? false) {
                $toolName  = $task['tool_name'];
                $params    = $task['params'] ?? [];
                $toolModel = ChatToolsProvider::getModel($toolName);
                if ($toolModel) {
                    $this->step++;
                    $toolModel->setLog($this->log)
                        ->setStep($this->step)
                        ->doFunction($params);
                }
                $task = $task['next'] ?? [];
            }
        }
        $this->step = 0;
        $this->sendDone();
    }
}