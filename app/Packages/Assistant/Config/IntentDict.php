<?php

namespace App\Packages\Assistant\Config;

enum IntentDict
{
    const PlayGame     = 'PlayGame';
    const Gen          = 'Gen';
    const Word         = 'Word';
    const Time         = 'Time';
    const Notification = 'Notification';
    const Weather      = 'Weather';
    const Travel       = 'Travel';
    const Question     = 'Question';
    const DICTS        = [
        self::Question     => '提问/聊天/获取(文学作品、音乐介绍歌词、影视介绍等)',
        self::Time         => '时间/提醒/闹钟/日历',
        self::Weather      => '查询天气:关键字"查询天气"',
        self::Travel       => '出行/距离/导航',
        self::Gen          => '生成图片/生成视频:关键字"生成"、"制作"、"绘制"、"创作"',
        self::Notification => '通知/告诉',
    ];

    public static function getDict(string $key): string
    {
        return self::DICTS[$key] ?? '';
    }
}
