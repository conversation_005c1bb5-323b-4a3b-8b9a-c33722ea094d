<?php

namespace App\Packages\Assistant\Tools;

use Exception;
use GuzzleHttp\Client;

class BigModel
{
    protected Client $client;
    protected string $token = '';
    protected string $model = 'GLM-4-Flash-250414';

    public function __construct()
    {
        $this->token  = env('BIGMODEL_TOKEN', '');
        $this->client = new Client([
            'verify'   => false,
            'base_uri' => 'https://open.bigmodel.cn/api/paas/v4/chat/completions'
        ]);
    }

    public function getAnswer(string $question)
    {
        $system = <<<EOT
                # 目标
                    查询文学作品
                ## 提问内容
                    $question
                ## 输入类型
                - 文学作品名称
                - 文学作品中的句子
                ## 输出
                - 只输出答案或文学标题、作者、内容,不要添加其他提示。
                - 使用 MarkDown 格式输出
                EOT;
        try {
            $response = $this->client->post('', [
                'headers' => [
                    'Authorization' => 'Bearer '.$this->token,
                ],
                'json'    => [
                    'model'    => $this->model,
                    'messages' => [
                        [
                            'role'    => 'system',
                            'content' => $system
                        ],
                        [
                            'role'    => 'user',
                            'content' => $question
                        ]
                    ]
                ],
            ]);
            if ($response->getStatusCode() === 200) {
                $data = json_decode($response->getBody()->getContents(), true);
                return $data['choices'][0]['message']['content'];
            } else {
                return $question;
            }
        } catch (Exception $exception) {
            return $question;
        }
    }

    public function optimizationPrompt(string $prompt)
    {
        try {
            $response = $this->client->post('', [
                'headers' => [
                    'Authorization' => 'Bearer '.$this->token,
                ],
                'json'    => [
                    'model'    => $this->model,
                    'messages' => [
                        [
                            'role'    => 'system',
                            'content' => '你是一个助手，请根据用户输入的文本，进行优化。'
                        ],
                        [
                            'role'    => 'user',
                            'content' => $prompt
                        ]
                    ]
                ],
            ]);
            if ($response->getStatusCode() === 200) {
                $data = json_decode($response->getBody()->getContents(), true);
                return $data['choices'][0]['message']['content'];
            } else {
                return $prompt;
            }
        } catch (Exception $exception) {
            return $prompt;
        }
    }

}