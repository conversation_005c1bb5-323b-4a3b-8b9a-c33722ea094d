<?php

namespace App\Packages\Assistant\ChatTools\Gen;

use App\Jobs\Plugin\JmDrawJob;
use App\Models\PluginJmDraw;
use App\Packages\Assistant\ChatTools\BaseEvent;
use App\Packages\Assistant\Tools\BigModel;

class GenImage extends BaseEvent
{
    const KEY = 'gen_image';
    public string   $name   = '生成图片';
    protected array $params = [];

    public function __construct()
    {
        parent::__construct(self::KEY, $this->name);
    }

    public function getKey()
    {
        return self::KEY;
    }

    public function getPrompt(): array
    {
        return [
            'name'        => self::KEY,
            'description' => "当你想要生成图片时，请使用此功能。",
            'parameters'  => [
                'prompt'     => [
                    'type'        => 'string',
                    'description' => '图片描述不包含依赖工具内容',
                ],
                'tool'       => [
                    'type'        => 'genPrompt',
                    'description' => '依赖工具的内容,使用当前工具提供的格式<tool>tool_name|genPrompt</tool>',
                ],
                'write_text' => [
                    'type'        => 'string',
                    'description' => '要求被写到图片的文字',
                ],
            ],
        ];
    }

    public function doFunction(array $params): mixed
    {
        $this->sendStepRemark("开始执行任务\n");
        $this->sendStepRemark("获取依赖信息\n");
        $prompt     = $params['prompt'] ?? '';
        $tool       = $params['tool'] ?? '';
        $writeText  = $params['write_text'] ?? '';
        $overPrompt = $tool ? $this->getCallParams($tool) : '';
        $overPrompt .= $prompt ?: '';
        $overPrompt .= $writeText ? '\"'.$writeText.'\"' : '';

        $this->sendStepRemark("优化图片描述\n");
        $bigModel   = new BigModel();
        $overPrompt = <<<EOT
                # 目标
                    优化描述内容,不要破坏主要内容.
                ## 原内容
                    $overPrompt
                ## 要求
                - 优化描述内容，优化内容要带有合理的运镜、风格、光照
                - 重点描述好天气下的心情
                - 要多一点自然元素
                ## 输出
                    仅显示被优化后的内容，不要添加其他提示。
                EOT;

        $overPrompt = $bigModel->optimizationPrompt($overPrompt);

        if (blank($overPrompt)) {
            $this->error('参数错误');
            $this->sendDone();
            return false;
        }
        $type = PluginJmDraw::TYPE_NORMAL_IMAGE;
        //        $type = PluginJmDraw::TYPE_TEXT_IMAGE;
        $data = [
            'user_id' => $this->log->user_id,
            'prompt'  => $overPrompt,
            'type'    => $type,
            'inputs'  => [],
            'params'  => [
                'sample_strength' => 1,
                'ratio'           => '9:16',
                'width'           => 864,
                'height'          => 1152,
            ],
        ];
        $this->sendStepRemark("开始生成图片\n");
        $log = PluginJmDraw::create($data);
        JmDrawJob::dispatchSync($log);
        $log->refresh();
        $data = $log->getAssetResource();
        $this->log->manyTools()->updateOrCreate([
            'key'  => self::KEY,
            'name' => $this->name,
        ], [
            'step'    => $this->step,
            'params'  => $params,
            'results' => $data
        ]);
        $this->sendStepData($data);
        $this->sendDone();
        return true;
    }
}