<?php

namespace App\Packages\Assistant\ChatTools\Question;

use App\Models\AiChatConfig;
use App\Models\AiChatEngine;
use App\Packages\Assistant\ChatTools\BaseEvent;
use App\Packages\Suno\Exceptions\StreamException;
use GuzzleHttp\Client;
use Illuminate\Support\Str;
use Psr\Http\Message\StreamInterface;

class Chat extends BaseEvent
{
    const KEY = 'chat_question';

    public string          $name            = '提问/聊天/正常对话';
    protected array        $params          = [];
    protected AiChatConfig $config;
    protected AiChatEngine $engine;
    protected Client       $client;
    protected bool         $multiEngine     = false;
    protected              $response;
    protected string       $outPutResponse  = '';
    protected string       $outPutReasoning = '';

    public function __construct()
    {
        parent::__construct(self::KEY, $this->name);
        $this->config = AiChatConfig::where('assistant', 1)->first();

        $this->engine = $this->config?->engine;
        $this->client = new Client([
            'verify'   => false,
            'base_uri' => Str::finish($this->engine?->url, '/'),
        ]);
    }

    public function getKey()
    {
        return self::KEY;
    }

    public function getPrompt(): array
    {
        return [
            'name'        => self::KEY,
            'description' => <<<EOT
                                当你想和助手聊天时，请使用此功能。
                                EOT,
            'parameters'  => [
                'message' => [
                    'type'        => 'string',
                    'description' => '用户的聊天内容',
                ],
            ],
        ];
    }

    public function doFunction(array $params): mixed
    {
        if (! $this->config) {
            $this->error('AI助理未配置');
            $this->sendDone();
        }
        $this->sendStepRemark("正在与助手对话\n");
        $message        = $this->getParams($params['message']);
        $this->response = $this->client->post('chat/completions', [
            'headers' => [
                'Authorization' => 'Bearer '.$this->config->token,
                'Content-Type'  => 'application/json',
            ],
            'json'    => $message,
            'stream'  => true,
        ]);
        $this->checkText();
        $this->log->manyTools()->updateOrCreate([
            'key'  => self::KEY,
            'name' => $this->name,
        ], [
            'step'      => $this->step,
            'params'    => $params,
            'response'  => $this->outPutResponse,
            'reasoning' => $this->outPutReasoning,
        ]);
        $this->sendDone();
        return true;
    }

    public function getParams(
        string $message,
        array $images = [],
    ): array {
        $maxTokenKey = property_exists($this, 'maxTokenKey') ? $this->maxTokenKey : 'max_tokens';
        $data        = [
            'messages'       => $this->getGroupMessage(
                message: $message,
                images: $images,
            ),
            'model'          => $this->engine->name,
            $maxTokenKey     => (int) $this->engine->maxout,
            'stream'         => true,
            'stream_options' => [
                'include_usage' => true,
            ],
        ];

        return $data;
    }

    public function getGroupMessage(string $message, array $images = []): array
    {
        if ($this->multiEngine) {
            return $this->multiParams($message, $images);
        } else {
            return $this->textParams($message);
        }
    }

    protected function multiParams(string $message, array $images = [])
    {
        $data = [
            [
                'role'    => 'user',
                'content' => [
                    [
                        'type' => 'text',
                        'text' => $message,
                    ],
                ],
            ]
        ];
        if (! blank($this->engine->system)) {
            array_unshift($data, [
                'role'    => 'system',
                'content' => [
                    [
                        'type' => 'text',
                        'text' => $this->engine->system,
                    ]
                ],
            ]);
        }
        return $data;
    }

    protected function textParams(string $message)
    {
        $data = [
            [
                'role'    => 'user',
                'content' => $message,
            ]
        ];

        if (! blank($this->engine->system)) {
            array_unshift($data, [
                'role'    => 'system',
                'content' => $this->engine->system,
            ]);
        }
        return $data;
    }

    public function checkText()
    {
        foreach ($this->getIterator() as $chunk) {
            $delta     = $chunk['choices'][0]['delta'] ?? [];
            $reasoning = $delta['reasoning_content'] ?? null;
            if (! is_null($reasoning)) {
                $this->outPutReasoning .= $reasoning;
                $this->sendStepReasoning($this->step, $reasoning);
            }
            $message = $delta['content'] ?? null;
            if (! is_null($message)) {
                $this->outPutResponse .= $message;
                $this->sendStepMessage($this->step, $message);
            }
        }
    }

    public function getIterator()
    {
        while (! $this->response->getBody()->eof()) {
            $line = $this->readLine($this->response->getBody());
            if (! str_starts_with($line, 'data:')) {
                continue;
            }
            $data = trim(substr($line, strlen('data:')));
            if ($data === '[DONE]') {
                yield 'DONE';
                break;
            }
            $response = json_decode($data, true);
            if (isset($response['error'])) {
                throw new StreamException($response['error'],
                    $this->response->getStatusCode());
            }

            yield $response;
        }
    }

    private function readLine(StreamInterface $stream): string
    {
        $buffer = '';

        while (! $stream->eof()) {
            if ('' === ($byte = $stream->read(1))) {
                return $buffer;
            }
            $buffer .= $byte;
            if ($byte === "\n") {
                break;
            }
        }

        return $buffer;
    }

    protected function getImgBase64(string $url)
    {
        $url = $this->getImgUrl($url);
        return sprintf("data:image/jpeg;base64,%s", base64_encode(file_get_contents($url)));
    }

    protected function getImgUrl(string $url)
    {
        return sprintf("%s?x-oss-process=image/resize,m_lfit,w_1024,h_1024/format,jpeg", $url);
    }
}