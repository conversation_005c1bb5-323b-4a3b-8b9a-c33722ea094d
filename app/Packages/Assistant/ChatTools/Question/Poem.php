<?php

namespace App\Packages\Assistant\ChatTools\Question;

use App\Packages\Assistant\ChatTools\BaseEvent;
use App\Packages\Assistant\Tools\BigModel;

class Poem extends BaseEvent
{
    const KEY = 'poem_question';
    public string $name = '查询古诗内容';

    public function __construct()
    {
        parent::__construct(self::KEY, $this->name);
    }

    public function getKey()
    {
        return self::KEY;
    }

    public function getPrompt(): array
    {
        return [
            'name'        => self::KEY,
            'description' => <<<EOT
                                当需要查找古诗内容时，请使用此功能。
                                EOT,
            'parameters'  => [
                'name' => [
                    'type'        => 'string',
                    'description' => '古诗名称或当中的句子',
                ],
            ],
        ];
    }

    public function genPrompt(array $toolResults): string
    {
        return $toolResults['answer'] ?? '';
    }

    public function doFunction(array $params): mixed
    {
        $this->sendStepRemark("开始查询文学内容\n");
        $name     = $params['name'];
        $bigModel = new BigModel();
        $response = $bigModel->getAnswer($name);
        $this->log->manyTools()->updateOrCreate([
            'key'  => self::KEY,
            'name' => $this->name,
        ], [
            'step'    => $this->step,
            'params'  => $params,
            'results' => ['answer' => $response]
        ]);
        $this->sendStepSplit($response);
        return null;
    }
}