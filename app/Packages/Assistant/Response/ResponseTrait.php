<?php

namespace App\Packages\Assistant\Response;

trait ResponseTrait
{
    protected bool   $success = false;
    protected string $message = '';
    protected array  $data    = [];

    public static function error(string $message): self
    {
        return new static(null, $message);
    }

    public function toArray(): array
    {
        return $this->data;
    }

    public function getErrorMessage(): string
    {
        return $this->message;
    }

    public function isSuccess(): bool
    {
        return $this->success;
    }

    public function isError(): bool
    {
        return ! $this->success;
    }
}