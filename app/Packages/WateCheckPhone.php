<?php

namespace App\Packages;

use App\Exceptions\ValidatorException;
use App\Models\User;
use Modules\User\Extensions\Sms\BindPhoneMessage;
use Modules\User\Extensions\Sms\CertMessage;
use Modules\User\Extensions\Sms\ChangePhoneMessage;
use Modules\User\Extensions\Sms\LoginMessage;
use Modules\User\Models\Sms;

class WateCheckPhone
{
    const TYPE_LOGIN       = 0;
    const TYPE_CERT        = 1;
    const TYPE_CERT_CHECK  = 3;
    const TYPE_BIND        = 4;
    const TPPE_CHANGE_BIND = 5;
    const TYPES            = [
        self::TYPE_LOGIN       => '用户登录',
        self::TYPE_CERT        => '实名认证',
        self::TYPE_CERT_CHECK  => '实名验证',
        self::TYPE_BIND        => '绑定手机号',
        self::TPPE_CHANGE_BIND => '换绑手机号',
    ];

    const TYPE_CLASS = [
        self::TYPE_LOGIN       => LoginMessage::class,
        self::TYPE_CERT        => CertMessage::class,
        self::TYPE_CERT_CHECK  => CertMessage::class,
        self::TYPE_BIND        => BindPhoneMessage::class,
        self::TPPE_CHANGE_BIND => ChangePhoneMessage::class,
    ];

    const TYPE_EXISTS       = [self::TYPE_BIND];
    const TYPE_DOESNTEXISTS = [self::TPPE_CHANGE_BIND];

    public static function send($phone, $type)
    {
        if (in_array($phone, config('auth.test_mobile', []))) {
            config(['user.sms.debug' => true]);
        }
        
        if (in_array($type, self::TYPE_EXISTS) && User::where('username',
                $phone)->exists()) {
            throw new ValidatorException('手机号码已绑定其他账号!');
        }
        if (in_array($type, self::TYPE_DOESNTEXISTS) && User::where('username',
                $phone)->doesntExist()) {
            throw new ValidatorException('手机号码不存在!');
        }
        $message = new (self::TYPE_CLASS[$type]);
        Sms::where('mobile', $phone)
            ->where('channel', $message->getChannel())
            ->where('used', 0)
            ->update(['used' => 2]);
        Sms::send($phone, $message);
    }

    public static function checkPhone($phone, $type, $code)
    {
        if (in_array($phone, config('auth.test_mobile', []))) {
            return true;
        }
        $message = new (self::TYPE_CLASS[$type]);
        $sms     = Sms::where('mobile', $phone)
            ->where('channel', $message->getChannel())
            ->orderByDesc('created_at')
            ->first();
        if (! $sms || $sms->used) {
            throw new ValidatorException('验证码不存在或已被使用');
        }

        if (! config('user.SMS_DEBUG') && now()->diffInMinutes($sms->created_at) > config('user.SMS_EXPIRE_TIME',
                5)) {
            throw new ValidatorException('验证码已过期');
        }

        if ($sms->content !== $code) {
            throw new ValidatorException('验证码错误');
        }
        $sms->used = ! config('user.SMS_DEBUG');
        $sms->save();
    }
}