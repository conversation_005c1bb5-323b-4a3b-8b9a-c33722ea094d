<?php

namespace App\Packages\AlibabaCloud\Models;

use App\Packages\AlibabaCloud\Config\WssConfig;

class CosyVoice extends WssConfig
{
    const SYSTEM_VCN = [
        'x4_CtnCn_ZiYun_oral',
        'x5_lingfeiyi_flow',
        'x5_lingyuyan_flow',
        'x5_lingxiaoyue_flow',
        'x5_EnUs_Grant_emo',
        'x5_EnUs_Lila_emo',
        'x4_zirong_oral',
        'x5_lingfeiwen_oral',
        'x5_lingxiaoyue_oral',
        'x5_lingfeiyi_oral',
        'x4_EnUs_Grant_emo',
        'x4_EnUs_Lila_emo',
        'x4_xinyun_oral_math',
        'x4_xinyun_oral_english',
        'x4_xinyun_oral_chinese',
        'x4_ziyang_oral',
        'x4_xinyun_oral',
        'x4_lingyouyou_oral',
        'x4_lingxiaoli_oral',
        'x4_lingxiaoxuan_oral',
        'x4_lingfeizhe_oral',
        'x4_lingyuzhao_oral',
        'x4_lingxiaoqi_oral',
        'x4_lingyuyan_oral',
        'x4_lingfeiyi_oral',
        'x4_lingxiaoyue_oral',
        'x4_lingfeiyi_oral_emo',
        'x4_lingxiaoyue_oral_emo',
        'x4_zijin_oral',
        'x5_clone'
    ];

    public function tts(string $vcn, string $text, float $speed, string $fileName)
    {
        if (in_array($vcn, self::SYSTEM_VCN)) {
            $headers   = [
                'app_id' => $this->appId,
                'status' => 2,
            ];
            $parameter = [
                'tts' => [
                    'vcn'    => $vcn,
                    'speed'  => (int) bcmul(50, $speed < 1 ? bcmul(bcsub($speed, 0.5, 1), 2, 1) : $speed, 0),
                    //语速：0对应默认语速的1/2，100对应默认语速的2倍
                    'volume' => 50,//音量：0是静音，1对应默认音量1/2，100对应默认音量的2倍
                    'pitch'  => 50,//语调：0对应默认语速的1/2，100对应默认语速的2倍
                    'audio'  => [
                        'encoding'    => 'lame',
                        'sample_rate' => 24000,
                        'channels'    => 1,
                        'bit_depth'   => 16,
                        'frame_size'  => 0,
                    ],
                ]
            ];
            $payload   = [
                'text' => [
                    "encoding" => "utf8",
                    "compress" => "raw",
                    "format"   => "plain",
                    "status"   => 2,
                    "seq"      => 0,
                    "text"     => base64_encode($text)
                ],
            ];
            $this->setHost('cbm01.cn-huabei-1.xf-yun.com')
                ->setUrl('/v1/private/mcd9m97e6')
                ->connect($vcn, $headers, $parameter, $payload, $fileName);
        } else {
            $headers   = [
                'app_id' => $this->appId,
                'status' => 2,
                'res_id' => $vcn,
            ];
            $parameter = [
                'tts' => [
                    'vcn'    => 'x5_clone',
                    'speed'  => (int) bcmul(50, $speed < 1 ? bcmul(bcsub($speed, 0.5, 1), 2, 1) : $speed, 0),
                    'volume' => 50,
                    'pitch'  => 50,
                    'audio'  => [
                        'encoding'    => 'lame',
                        'sample_rate' => 24000,
                    ],
                ]
            ];
            $payload   = [
                'text' => [
                    "encoding" => "utf8",
                    "compress" => "raw",
                    "format"   => "plain",
                    "status"   => 2,
                    "seq"      => 0,
                    "text"     => base64_encode($text)
                ],
            ];
            $this->setHost('cn-huabei-1.xf-yun.com')
                ->setUrl('/v1/private/voice_clone')
                ->connect($vcn, $headers, $parameter, $payload, $fileName);
        }
    }
}