<?php

namespace App\Packages\Volcengine\Request;

use Volcengine\Common\Configuration;
use Volcengine\Common\Utils;

class RtcRequest
{
    protected array  $params    = [];
    protected array  $queryBody = [
        'Action'  => '',
        'Version' => '2024-12-01',
        'AppId'   => '',
        'RoomId'  => '',
        'TaskId'  => '',
    ];
    protected string $host      = 'https://rtc.volcengineapi.com';
    protected string $method    = 'POST';
    protected string $cv        = 'rtc';
    protected array  $headers   = [
        'Host' => 'rtc.volcengineapi.com',
    ];
    protected string $xDate     = '';

    public function __construct()
    {
        $this->xDate = date('Ymd\THis\Z');
    }

    public function setAppId(string $AppId): self
    {
        $this->queryBody['AppId'] = $AppId;
        return $this;
    }

    public function setRoomId(string $RoomId): self
    {
        $this->queryBody['RoomId'] = $RoomId;
        return $this;
    }

    public function setTaskId(string $TaskId): self
    {
        $this->queryBody['TaskId'] = $TaskId;
        return $this;
    }

    public function setAction(string $Action): self
    {
        $this->queryBody['Action'] = $Action;
        return $this;
    }

    public function setVersion(string $Version): self
    {
        $this->queryBody['Version'] = $Version;
        return $this;
    }

    public function getUrl()
    {
        return $this->host.'?'.http_build_query($this->getQuery());
    }

    public function getQuery()
    {
        return [
            'Action'  => $this->queryBody['Action'],
            'Version' => $this->queryBody['Version'],
        ];
    }

    public function getQueryBody()
    {
        return $this->queryBody;
    }

    public function getAuthorization(Configuration $config)
    {
        $query  = $this->queryBody;
        $params = $this->getParams();
        ksort($query);
        ksort($params);

        return Utils::signv4($config->getAk(), $config->getSk(), $config->getRegion(),
            $this->getService(),
            json_encode($params), http_build_query($query), $this->getMethod(), '/',
            $this->getHeaders());
    }

    public function getParams()
    {
        return $this->params;
    }

    public function setParams(array $params): self
    {
        $this->params = $params;
        return $this;
    }

    public function getService()
    {
        return $this->cv;
    }

    public function getMethod()
    {
        return $this->method;
    }

    public function getHeaders()
    {
        return $this->headers;
    }

    public function setHeaders(array $headers): self
    {
        $this->headers = array_merge($this->headers, $headers);
        return $this;
    }
}