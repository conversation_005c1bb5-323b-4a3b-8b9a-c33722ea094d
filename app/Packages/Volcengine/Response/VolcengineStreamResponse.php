<?php

namespace App\Packages\Volcengine\Response;

use App\Packages\Suno\Exceptions\StreamException;
use Psr\Http\Message\StreamInterface;

class VolcengineStreamResponse
{
    public function __construct(protected $response, protected array $params)
    {
    }

    public function getIterator()
    {
        while (! $this->response->getBody()->eof()) {
            $line  = $this->readLine($this->response->getBody());
            $event = null;
            if (str_starts_with($line, 'event:')) {
                $event = trim(substr($line, strlen('event:')));
                $line  = $this->readLine($this->response->getBody());
            }
            if (! str_starts_with($line, 'data:')) {
                continue;
            }
            $data = trim(substr($line, strlen('data:')));
            if ($data === '[DONE]') {
                break;
            }
            $response = json_decode($data, true);
            if (isset($response['error'])) {
                throw new StreamException($response['error'],
                    $this->response->getStatusCode());
            }

            if ($event !== null) {
                $response['__event'] = $event;
            }

            yield $response;
        }
    }

    private function readLine(StreamInterface $stream): string
    {
        $buffer = '';

        while (! $stream->eof()) {
            if ('' === ($byte = $stream->read(1))) {
                return $buffer;
            }
            $buffer .= $byte;
            if ($byte === "\n") {
                break;
            }
        }

        return $buffer;
    }
}