<?php

namespace App\Packages\FastGpt\Knowledge;

use App\Packages\FastGpt\BaseModuleClient;

class Client extends BaseModuleClient
{
    protected string $path = '/api';

    /**
     * 创建知识库
     *
     * @param  string  $name  知识库名称
     * @param  string  $type  dataset或者folder，代表普通知识库和文件夹。不传则代表创建普通知识库
     * @param  string  $intro  介绍
     * @param  string  $avatar  头像地址
     * @return array
     * @throws \App\Exceptions\ValidatorException
     */
    public function create(
        string $name,
        string $type = '',
        string $intro = '',
        string $avatar = '',
    ): array {
        return $this->sendRequest('post', '/core/dataset/create', [
            'name'   => $name,
            'type'   => $type,
            'intro'  => $intro,
            'avatar' => $avatar,
        ]);
    }

    /**
     * Notes: 修改知识库信息，不支持token鉴权，只能使用cookie鉴权
     *
     * @Author: 玄尘
     * @Date: 2025/3/17 13:41
     * @param  string  $dataset_id
     * @param  string  $name
     * @param  string  $intro
     * @return array
     * @throws \App\Exceptions\ValidatorException
     */
    public function update(string $dataset_id, string $name, string $intro = ''): array
    {
        $query = array_filter([
            'id'         => $dataset_id,
            'name'       => $name,
            'intro'      => $intro,
            'permission' => 'public',
        ]);
        return $this->sendRequest('post', '/core/dataset/update', $query, true);
    }

    /**
     * Notes: 知识库详情
     *
     * @Author: 玄尘
     * @Date: 2024/12/18 14:24
     * @param  string  $dataset_id
     * @return array
     * @throws \App\Exceptions\ValidatorException
     */
    public function knowledgeDetail(string $dataset_id)
    {
        $query = [
            'id' => $dataset_id,
        ];
        return $this->sendRequest('get', '/core/dataset/detail', $query);
    }

    /**
     * Notes: 删除
     *
     * @Author: 玄尘
     * @Date: 2024/12/16 15:39
     * @param  string  $dataset_id
     * @return array
     * @throws \App\Exceptions\ValidatorException
     */
    public function deleteKnowledge(string $dataset_id)
    {
        $query = [
            'id' => $dataset_id,
        ];
        return $this->sendRequest('delete', '/core/dataset/delete', $query);
    }

    /**
     * Notes: 创建训练订单
     *
     * @Author: 玄尘
     * @Date: 2024/12/16 16:12
     * @param  string  $dataset_id
     * @return array
     * @throws \App\Exceptions\ValidatorException
     */
    public function trainingOrder(string $dataset_id): array
    {
        $query = [
            'datasetId' => $dataset_id,
        ];
        return $this->sendRequest('post', '/support/wallet/usage/createTrainingUsage', $query);
    }

    /**
     * Notes: 搜索测试
     *
     * @Author: 玄尘
     * @Date: 2024/12/18 14:24
     * @param $dataset_id
     * @param $content
     * @return array
     * @throws \App\Exceptions\ValidatorException
     */
    public function search($dataset_id, $content)
    {
        $query = [
            'datasetId'   => $dataset_id,
            'text'        => $content,
            'limit'       => 5000,
            'searchMode'  => 'embedding',//搜索模式：embedding | fullTextRecall | mixedRecall
            'usingReRank' => 0,
        ];
        return $this->sendRequest('post', '/core/dataset/searchTest', $query);
    }

}