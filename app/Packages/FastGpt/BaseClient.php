<?php

namespace App\Packages\FastGpt;

use App\Exceptions\ValidatorException;
use App\Models\SystemConfig;

class BaseClient
{
    protected string $baseUri;
    protected array  $headers        = [];
    protected array  $params         = [];
    protected bool   $hasFile        = false;
    protected bool   $debug          = false;
    protected        $file;
    protected string $authorization;
    protected bool   $fastgpt_is_new = false;

    public function __construct()
    {
        $this->fastgpt_is_new = env('FASTPGT_IS_NEW', false);

        $this->headers['Content-Type'] = 'application/json';
        $this->setBaseData();
    }

    public function setBaseData()
    {
        if ($this->fastgpt_is_new) {
            $this->baseUri       = SystemConfig::getValue('new_fastgpt_url');
            $this->authorization = SystemConfig::getValue('new_fastgpt_token');
        } else {
            $this->baseUri       = SystemConfig::getValue('FastGptUrl');
            $this->authorization = SystemConfig::getValue('FastGpt_Authorization');
        }
    }

    public function debug()
    {
        $this->debug = true;
        return $this;
    }

    /**
     * @return $this
     */
    protected function setEnvAuthorization(): self
    {
        $this->headers['Authorization'] = 'Bearer '.$this->authorization;
        return $this;
    }

    protected function setApiTokenAuthorization($token): self
    {
        $this->headers['Authorization'] = 'Bearer '.$token;
        return $this;
    }

    /**
     * @return $this
     */
    protected function setAuthorization(): self
    {
        $token = new Token();
        if ($this->fastgpt_is_new) {
            $this->headers['Cookie'] = 'fastgpt_token='.$token->getToken();
        } else {
            $this->headers['Cookie'] = 'token='.$token->getToken();
        }
        return $this;
    }

    protected function setParams(array $params)
    {
        $this->params = $params;
        return $this;
    }

    protected function setFile($file)
    {
        $this->file    = $file;
        $this->hasFile = true;
        return $this;
    }

    public function getCookie()
    {
        $this->setAuthorization();
        return $this->headers['Cookie'];
    }

    public function getEnvAuthorization()
    {
        return $this->authorization;
    }

    protected function get(string $path): array
    {
        return $this->request($path, 'GET');
    }

    /**
     * 数据包发送
     *
     * @param  string  $method
     * @param  string  $url
     * @return array
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    private function request(string $url, string $method, string $type = ''): array
    {
        try {
            if (! $this->baseUri) {
                throw new ValidatorException("缺少接口地址");
            }

            $paramsName = 'json';
            if ($method == 'GET' || $method == 'DELETE') {
                $paramsName = 'query';
            }

            $client  = new \GuzzleHttp\Client();
            $options = [
                'headers' => $this->headers,
            ];

            // 添加禁用 SSL 验证的选项 (仅用于开发/测试，不要在生产环境中使用!)
            $options['verify'] = true;

            if ($type == 'MULTIPART') {
                unset($this->headers['Content-Type']);
                $options['multipart'] = $this->params;
            } else {
                $options[$paramsName] = $this->params;
            }

            $response = $client->request($method, $this->baseUri.$url, $options);

            if ($this->debug) {
                dump($method);
                dump($this->baseUri.$url);
                dump($this->headers);
                dump($this->params);
                die;
            }

            $statusCode = $response->getStatusCode();

            if ($statusCode == 200 || $statusCode == 201 || $statusCode == 0) {
                return json_decode($response->getBody()->getContents(), true);
            } elseif ($statusCode == 400) {
                $error = json_decode($response->getBody()->getContents(), true);
                if ($error['message'] ?? false) {
                    throw new FastGptException($error['message']);
                }
                throw new \Exception("返回内容错误【Un】");
            } else {
                throw new \Exception("接口服务器访问异常【{$response->getStatusCode()}】");
            }
        } catch (\Exception $exception) {
            if (method_exists($exception, 'getResponse')) {
                // 如果存在，则可以调用
                $statusCode = $exception->getResponse()?->getStatusCode();
                if ($statusCode == 401) {
                    throw new \Exception("接口服务器访问异常【{$statusCode}】");
                }
            }
            throw new \Exception($exception->getMessage());
        }
    }

    protected function patch(string $path): array
    {
        return $this->request($path, 'PATCH');
    }

    protected function multipart(string $path): array
    {
        return $this->request($path, 'POST', 'MULTIPART');
    }

    protected function post(string $path): array
    {
        return $this->request($path, 'POST');
    }

    protected function delete(string $path): array
    {
        return $this->request($path, 'DELETE');
    }

    protected function put(string $path): array
    {
        return $this->request($path, 'PUT');
    }

    /**
     * Notes: 检查基础数据
     *
     * @Author: 玄尘
     * @Date: 2025/3/17 13:16
     */
    public function checkBaseData(): bool
    {
        if (! $this->baseUri) {
            throw new ValidatorException("缺少接口地址");
        }
        if (! $this->authorization) {
            throw new ValidatorException("缺少接口token");
        }
        return true;
    }

    public static function isNew()
    {
        return env('FASTPGT_IS_NEW', false);
    }

    public function setFastgptIsNew($value): self
    {
        $this->fastgpt_is_new = $value;
        $this->setBaseData();
        return $this;
    }

}