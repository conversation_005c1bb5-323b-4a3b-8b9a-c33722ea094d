<?php

namespace App\Packages\FastGpt\Chat;

use App\Packages\FastGpt\BaseModuleClient;

class Client extends BaseModuleClient
{

    public function chat($chatMes, $app, $is_cookie = false)
    {
        // 设置请求的 URL 和 Cookie
        $url    = $this->baseUri.$this->path.'/v1/chat/completions';
        $cookie = $this->getCookie();
        // 配置 PHP 环境设置
        ignore_user_abort(true);
        set_time_limit(300);
        session_write_close();
        header('Content-Type: text/event-stream');
        header('Cache-Control: no-cache');
        header('Connection: keep-alive');
        header('X-Accel-Buffering: no');
        ob_implicit_flush(1);

        // 初始化 CURL 会话
        $ch   = curl_init();
        $data = [
            'Content-Type: application/json',
            'Accept: text/event-stream',
        ];
        if ($is_cookie) {
            $data[] = 'Cookie: '.$cookie;
        } else {
            $data[] = 'Authorization: Bearer '.$app->api_token;
        }
        // 设置 CURL 参数
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, false);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, false);
        curl_setopt($ch, CURLOPT_TIMEOUT, 0);         // 无限超时
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($chatMes));
        curl_setopt($ch, CURLOPT_HTTPHEADER, $data);

        // 设置连接超时时间和缓冲区大小
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 30); // 增加连接超时时间
        curl_setopt($ch, CURLOPT_BUFFERSIZE, 120);    // 调整缓冲区大小

        // 定义自定义的写入回调函数
        curl_setopt($ch, CURLOPT_WRITEFUNCTION, function ($ch, $data) {
            echo $data;            // 输出数据
            ob_flush();            // 刷新输出缓存
            flush();               // 清空缓冲区
            return strlen($data);  // 返回处理的字节数
        });

        // 执行请求
        $response = curl_exec($ch);

        if ($response === false) {
            error_log('Curl error: '.curl_error($ch));  // 记录日志
            die('Curl error: '.curl_error($ch));
        }

        // 关闭 CURL 会话
        curl_close($ch);
    }

    public function chatList($app)
    {
        $data = [
            'appId' => $app->app_id
        ];
        if ($this->fastgpt_is_new) {
            $data['source'] = 'api';
        }
        return $this->sendRequest('post', '/core/chat/getHistories', $data);
    }

    /**
     * Notes: 单个聊天的详情
     *
     * @Author: 玄尘
     * @Date: 2025/3/19 13:04
     * @param $app_id
     * @param $chat_id
     * @return mixed
     * @throws \App\Exceptions\ValidatorException
     */
    public function chatDetail($app_id, $chat_id)
    {
        if ($this->fastgpt_is_new) {
            return $this->sendRequest('post', '/core/chat/getPaginationRecords', [
                'appId'  => $app_id,
                'chatId' => $chat_id
            ]);
        } else {
            return $this->sendRequest('get', '/core/chat/init', [
                'appId'  => $app_id,
                'chatId' => $chat_id
            ]);
        }
    }

    public function updateUserFeedback($app_id, $chat_id, $chatItemId, $userGoodFeedback, $userBadFeedback)
    {
        if ($this->fastgpt_is_new) {
            $query = array_filter([
                'appId'            => $app_id,
                'chatId'           => $chat_id,
                'dataId'           => $chatItemId,
                'userGoodFeedback' => $userGoodFeedback,
                'userBadFeedback'  => $userBadFeedback
            ]);
        } else {
            $query = array_filter([
                'appId'            => $app_id,
                'chatId'           => $chat_id,
                'chatItemId'       => $chatItemId,
                'userGoodFeedback' => $userGoodFeedback,
                'userBadFeedback'  => $userBadFeedback
            ]);
        }

        return $this->sendRequest('post', '/core/chat/feedback/updateUserFeedback', $query);
    }

    public function updateChatTitle($app_id, $chat_id, $title)
    {
        return $this->sendRequest('post', '/core/chat/updateHistory', [
            'appId'       => $app_id,
            'chatId'      => $chat_id,
            'customTitle' => $title,
        ]);
    }

    public function chatTop($app_id, $chat_id, $top)
    {
        return $this->sendRequest('post', '/core/chat/updateHistory', [
            'appId'  => $app_id,
            'chatId' => $chat_id,
            'top'    => $top,
        ]);
    }

    public function chatDelete($app_id, $chat_id)
    {
        return $this->sendRequest('delete', '/core/chat/delHistory', [
            'chatId' => $chat_id,
            'appId'  => $app_id
        ]);
    }

    public function chatClear($app)
    {
        return $this->sendRequest('delete', '/core/chat/clearHistories', [
            'appId' => $app->app_id
        ]);
    }

    /**
     * Notes: 获取对话记录列表
     *
     * @Author: 玄尘
     * @Date: 2025/2/27 13:49
     */
    public function records($app_id, $chat_id)
    {
        return $this->sendRequest('post', '/core/chat/getPaginationRecords', [
            'appId'    => $app_id,
            'chatId'   => $chat_id,
            'pageSize' => 10
        ]);
    }

}