<?php

namespace App\Packages\FastGpt\Set;

use App\Exceptions\ValidatorException;
use App\Models\FastgptKnowledgeSet;
use App\Packages\FastGpt\BaseModuleClient;
use App\Packages\FastGpt\FastGpt;

class Client extends BaseModuleClient
{

    /**
     * Notes: 空集合
     *
     * @Author: 玄尘
     * @Date: 2024/12/16 16:39
     */
    public function setEmpty($datasetId, $name, $type)
    {
        $query = [
            'datasetId' => $datasetId,
            'name'      => $name,
            'type'      => $type,
        ];
        return $this->sendRequest('post', '/core/dataset/collection/create', $query);
    }

    /**
     * Notes: 获取空集合下的列表
     *
     * @Author: 玄尘
     * @Date: 2024/12/18 15:38
     * @param $datasetId
     * @param $parentId
     * @return array
     * @throws \App\Exceptions\ValidatorException
     */
    public function getList($datasetId, $parentId, $page = 1, $pageSize = 10)
    {
        $path = '/core/dataset/collection/list';
        if ($this->fastgpt_is_new) {
            $path = '/core/dataset/collection/listV2';
        }
        $query = [
            'datasetId' => $datasetId,
            'parentId'  => $parentId,
            'pageNum'   => $page,
            'pageSize'  => $pageSize,
        ];
        return $this->sendRequest('post', $path, $query);
    }

    /**
     * Notes: description
     *
     * @Author: 玄尘
     * @Date: 2024/12/16 17:15
     * @param  string  $datasetId  知识库id
     * @param  string  $name  知识库名称
     * @param  string  $text  文本内容
     * @param  string  $trainingType  训练模式
     * @return array
     * @throws \App\Exceptions\ValidatorException
     */
    public function setText(string $datasetId, string $name, string $text, string $trainingType)
    {
        $query = [
            'datasetId'    => $datasetId,
            'name'         => $name,
            'text'         => $text,
            'trainingType' => $trainingType,
        ];
        return $this->sendRequest('post', '/core/dataset/collection/create/text', $query);
    }

    /**
     * Notes: 创建链接集合
     *
     * @Author: 玄尘
     * @Date: 2024/12/16 17:23
     * @param  string  $datasetId
     * @param  string  $name
     * @param  string  $link
     * @param  string  $trainingType
     * @return array
     * @throws \App\Exceptions\ValidatorException
     */
    public function setLink(string $datasetId, string $link, string $trainingType)
    {
        $query = [
            'datasetId'    => $datasetId,
            'link'         => $link,
            'trainingType' => $trainingType,
        ];
        return $this->sendRequest('post', '/core/dataset/collection/create/link', $query);
    }

    /**
     * Notes: 文件集合
     *
     * @Author: 玄尘
     * @Date: 2024/12/16 17:30
     * @param  string  $datasetId
     * @param  string  $fileUrl
     * @param  string  $trainingType
     * @return array
     * @throws \App\Exceptions\ValidatorException
     */
    public function setFileData(string $datasetId, string $fileUrl, string $trainingType)
    {
        $query = [
            [
                'name'     => 'file',
                'contents' => fopen($fileUrl, 'r'),
                'filename' => basename(parse_url($fileUrl, PHP_URL_PATH)),
            ],
            [
                'name'     => 'data',
                'contents' => json_encode([
                    'datasetId'    => $datasetId,
                    'trainingType' => $trainingType,
                ])
            ]
        ];
        return $this->sendRequest('multipart', '/core/dataset/collection/create/localFile', $query);
    }

    /**
     * Notes: 修改合集数据
     *
     * @Author: 玄尘
     * @Date: 2025/2/26 08:50
     * @param  array  $data
     * @return mixed
     * @throws \App\Exceptions\ValidatorException
     */
    public function setUpdate(array $data)
    {
        $query = array_filter($data);
        if (empty($query)) {
            throw new ValidatorException('必须传入数据');
        }
        return $this->sendRequest('post', '/core/dataset/collection/update', $query);
    }

    /**
     * Notes: 删除合集
     *
     * @Author: 玄尘
     * @Date: 2025/2/26 08:50
     * @param $collection_id
     * @return mixed
     * @throws \App\Exceptions\ValidatorException
     */
    public function deleteSet($collection_id)
    {
        return $this->sendRequest('delete', '/core/dataset/collection/delete', [
            'id' => $collection_id
        ]);
    }

    /**
     * Notes: 获取合集是否完成
     *
     * @Author: 玄尘
     * @Date: 2025/2/26 08:52
     */
    public function getStatus($collection_id)
    {
        $collection = FastgptKnowledgeSet::where('collection_id', $collection_id)->first();
        $knowledge  = $collection->knowledge;

        $result = FastGpt::set()->getList($knowledge->dataset_id, 0);
        if ($this->fastgpt_is_new) {
            $list = $result['data']['list'];
        } else {
            $list = $result['data']['data'];
        }
        return collect($list)->where('_id', $collection_id)->first();
    }

}