<?php

namespace App\Packages\FastGpt;

use App\Exceptions\ValidatorException;
use Illuminate\Support\Facades\Log;

class BaseModuleClient extends BaseClient
{
    protected string $path = '/api';  // 每个子类需要定义自己的 API 路径

    /**
     * 发送请求的通用方法
     *
     * @param  string  $method  请求方法 ('GET', 'POST', 'DELETE' 等)
     * @param  string  $uri  请求的 URI
     * @param  array  $params  请求的参数
     * @return mixed
     * @throws \App\Exceptions\ValidatorException
     */
    protected function sendRequest(string $method, string $uri, array $params = [], bool $is_use_cookie = false)
    {
        try {
            $this->checkBaseData();

            if ($is_use_cookie) {
                $result = $this->setAuthorization();
            } elseif ($this->fastgpt_is_new) {
                $result = $this->setEnvAuthorization();
            } else {
                $result = $this->setAuthorization();
            }
            // 设置参数并发送请求
            $result = $result->setParams($params)
                ->$method($this->path.$uri);
            if ($result['code'] != 200) {
                throw new ValidatorException($result['message']);
            }

            return $result;
        } catch (\Exception $e) {
            $message = $e->getMessage();

            // 尝试从错误消息中提取 JSON 部分
            if (preg_match('/{.*}/', $message, $matches)) {
                $errorBody = json_decode($matches[0], true);
                if (json_last_error() === JSON_ERROR_NONE && isset($errorBody['message'])) {
                    throw new ValidatorException('接口服务器访问异常【'.$errorBody['message'].'】');
                }
            }

            Log::error($message);
            throw new ValidatorException($message);
        }
    }
}