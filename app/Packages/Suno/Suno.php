<?php

namespace App\Packages\Suno;

use App\Packages\Suno\Client\Account;
use App\Packages\Suno\Client\Task;
use App\Packages\Suno\Client\Tools;

class Suno
{
    public static function account(): Account
    {
        return new Account();
    }

    public static function task(): Task
    {
        return new Task();
    }

    public static function tools(): Tools
    {
        return new Tools();
    }
}