<?php

namespace App\Packages\Suno\Client;

use App\Models\DrawAudio;
use App\Packages\Suno\BaseClient;

class Task extends BaseClient
{
    public function makeAudio(DrawAudio $audio)
    {
        $prompt = $audio->prompt;
        if ($audio->gender) {
            $prompt = "[{$audio->gender} vocals]\n".$prompt;
        }
        $params = [
            'captcha_data'           => $audio->ext['captcha_data'],
            'captcha_key'            => $audio->ext['captcha_key'],
            "mode"                   => $audio->mode,
            "mv"                     => $audio->model,
            "prompt"                 => "",
            "gpt_description_prompt" => $prompt,
            "make_instrumental"      => $audio->instrumental,
            "is_completed"           => true
        ];
        if ($audio->mode == 2) {
            unset($params['gpt_description_prompt']);
            $params['prompt']             = $prompt;
            $params['tags']               = $audio->tags;
            $params['title']              = $audio->name;
            $params['continue_serial_no'] = null;
            $params['is_completed']       = false;
        }
        return $this->json('task', $params);
    }

    public function query(DrawAudio $audio)
    {
        return $this->get('task/'.$audio->job_id);
    }
}