<?php

namespace App\Packages\Suno;

use Psr\Http\Message\ResponseInterface;

class SunoResponse
{
    protected bool   $success;
    protected string $message;
    protected array  $data;

    /**
     * @param  \Psr\Http\Message\ResponseInterface|string  $response
     * @param  array  $params
     */
    public function __construct(
        ResponseInterface|string $response,
        protected array $params = []
    ) {
        if ($response instanceof ResponseInterface) {
            if ($response->getStatusCode() == 200) {
                $result = json_decode($response->getBody()->getContents(),
                    true);
                if (! isset($result['code']) && isset($result['data'])) {
                    $result = Aes::decrypt($result['data']);
                }
                if ($result['code'] == 0) {
                    $this->success = true;
                    $this->data    = $result['data'];
                } else {
                    $this->success = false;
                    $this->message = $result['msg'];
                }
            } else {
                $this->message = $response->getBody()->getContents();
            }
        } else {
            $this->success = false;
            $this->message = $response;
        }
    }

    /**
     * @param  string  $message
     * @param  array  $params
     * @return \App\Packages\Suno\SunoResponse
     */
    public static function error(string $message, array $params): SunoResponse
    {
        return new static($message, $params);
    }

    public function isError(): bool
    {
        return ! $this->isSuccess();
    }

    public function isSuccess(): bool
    {
        return $this->success;
    }

    public function getMessage(): string
    {
        return $this->message;
    }

    public function toArray(): array
    {
        return $this->data;
    }

    public function __get(string $name)
    {
        return $this->data[$name] ?? null;
    }
}