<?php

namespace App\Packages\Suno;

use App\Models\SystemConfig;
use App\Packages\Suno\Helper\UploadKey;
use Exception;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;
use Illuminate\Support\Str;

class BaseClient
{
    protected       $soft_id     = 'suno_web';
    protected       $ekey        = '';
    protected array $files       = [];
    protected array $uploadFiles = [];
    private string  $baseUrl;
    private string  $basePath;
    private string  $token;
    private array   $pathQuery   = [];

    public function __construct()
    {
        $this->ekey     = Str::random(16);
        $this->baseUrl  = SystemConfig::getValue('SunoApiUrl');
        $this->basePath = SystemConfig::getValue('SunoPath');
        $this->token    = SystemConfig::getValue('SunoToken');
    }

    protected function setPathQuery(array $pathQuery): self
    {
        $this->pathQuery = $pathQuery;
        return $this;
    }

    protected function setFiles(array $files): self
    {
        $this->files = $files;
        if (count($files) > 0) {
            $uploadKey = new UploadKey();
            foreach ($this->files as $file) {
                $this->uploadFiles[$file['key']] = $uploadKey->upload($file['url'],
                    $file['scene']);
            }
        }

        return $this;
    }

    protected function get(string $path, array $params = []): SunoResponse
    {
        return $this->request($path, $params);
    }

    public function request(
        string $path,
        array $params = [],
        string $method = 'GET',
        string $format = 'query',
        array $options = [],
    ): SunoResponse|SunoStreamResponse {
        $client = new Client([
            'base_uri' => $this->baseUrl,
        ]);
        $query  = [
            'soft_id' => $this->soft_id,
            'ekey'    => $this->ekey,
        ];
        if (count($this->uploadFiles) > 0) {
            $params = array_merge($params, $this->uploadFiles);
        }
        if ($format == 'query') {
            $params = array_merge($params, $query);
        } else {
            if (blank($this->pathQuery)) {
                $path .= '?'.http_build_query($query);
            } else {
                $path .= '?'.http_build_query($this->pathQuery);
            }
        }
        try {
            if ($options['stream'] ?? false) {
                $headers = $this->getStreamHeaders();
            } else {
                $headers = $this->getHeaders();
            }
            $response = $client->request($method, $this->basePath.'/'.$path,
                array_merge([
                    'headers' => $headers,
                    'verify'  => false,
                    $format   => $params,
                ], $options));
            if ($options['stream'] ?? false) {
                return new SunoStreamResponse($response, $params);
            } else {
                return new SunoResponse($response, $params);
            }
        } catch (RequestException $requestException) {
            dd($requestException);
        } catch (Exception $e) {
            return SunoResponse::error($e->getMessage(), $params);
        }
    }

    protected function getStreamHeaders(): array
    {
        return [
            'authorization' => $this->token,
        ];
    }

    protected function getHeaders()
    {
        return [
            'origin'           => 'https://www.suno.cn',
            'referer'          => 'https://www.suno.cn/',
            'atye61q0wmqrnz9y' => 'BFR3vkiBY73GGeNm',
            'authorization'    => $this->token,
            'Content-Type'     => 'application/json;charset=utf-8',
            'User-Agent'       => 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/112.0.0.0 Safari/537.36 QuarkPC/1.10.5.187'
        ];
    }

    protected function setUrl(string $baseUrl): self
    {
        $this->baseUrl = $baseUrl;
        return $this;
    }

    protected function setPath(string $basePath): self
    {
        $this->basePath = $basePath;
        return $this;
    }

    protected function post(string $path, array $params = []): SunoResponse
    {
        return $this->request($path, $params, 'POST', 'form_params');
    }

    protected function json(string $path, array $params = []): SunoResponse
    {
        return $this->request($path, $params, 'POST', 'json');
    }

    protected function stream($path, array $params = []): SunoStreamResponse
    {
        return $this->request($path, $params, 'POST', 'json', [
            'stream' => true,
        ]);
    }
}