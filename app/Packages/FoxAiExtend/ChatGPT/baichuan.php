<?php

namespace App\Packages\FoxAiExtend\ChatGPT;

class baichuan
{
    protected static $apiurl      = 'https://api.baichuan-ai.com/v1/chat/completions';
    protected static $model       = 'Baichuan2-Turbo';
    protected static $apiKey      = '';
    protected static $temperature = 0.3;
    protected static $max_tokens  = 2048;

    /**
     * sdk constructor.
     *
     * @param  string  $apiKey
     * @param  string  $model
     * @param  string  $temperature
     * @param  string  $max_tokens
     */
    public function __construct($apiKey = '', $model = '', $temperature = '', $max_tokens = 2048)
    {
        if ($model) {
            self::$model = $model;
        }
        if ($temperature) {
            self::$temperature = $temperature;
        }
        if ($max_tokens) {
            self::$max_tokens = $max_tokens;
        }
        self::$apiKey = $apiKey;
    }

    /**
     * @param  string  $message
     * @return array
     * GPT3.5以上模型
     * 流式输出
     */
    public function sendText($message = [], $callback = null)
    {
        $post   = [
            'messages'    => $message,
            'model'       => self::$model,
            'temperature' => floatval(self::$temperature),
            'max_tokens'  => intval(self::$max_tokens),
            'stream'      => true
        ];
        $result = $this->httpRequest(self::$apiurl, $post, $callback);

        return $this->handleError($result);
    }

    /**
     * http请求
     */
    protected function httpRequest($url, $post = null, $callback = null)
    {
        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
        curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, false);
        curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, false);
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json',
            'Authorization: Bearer '.self::$apiKey
        ]);
        if ($post) {
            curl_setopt($ch, CURLOPT_POST, true);
            curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($post));
        }
        if ($callback) {
            curl_setopt($ch, CURLOPT_WRITEFUNCTION, $callback);
        }
        $result = curl_exec($ch);
        if (curl_errno($ch)) {
            return [
                'errno'   => 1,
                'message' => 'curl 错误信息: '.curl_error($ch)
            ];
        }
        curl_close($ch);
        return json_decode($result, true);
    }

    /**
     * @param $result
     * @return array|mixed
     * 格式化接口报错
     */
    protected function handleError($result)
    {
        if (isset($result['errno'])) {
            return [
                'errno'   => 1,
                'message' => $result['message']
            ];
        }
        if (isset($result['error'])) {
            return [
                'errno'   => 1,
                'message' => $result['error']['message']
            ];
        }

        return $result;
    }
}
