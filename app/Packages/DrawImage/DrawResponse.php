<?php

namespace App\Packages\DrawImage;

use Psr\Http\Message\ResponseInterface;

class DrawResponse
{
    protected int          $code    = 200;
    protected string       $message = 'OK';
    protected bool         $success = false;
    protected array|string $data;

    public function __construct(ResponseInterface $response, protected array $params = [])
    {
        $this->code = $response->getStatusCode();
        if ($this->code == 200) {
            $this->success = true;
            $this->data    = json_decode($response->getBody()->getContents(), true);
        } else {
            $this->success = false;
            $this->message = $response->getBody()->getContents();
        }
    }

    public function getMessage(): string
    {
        return $this->message;
    }

    public function isError(): bool
    {
        return ! $this->isSuccess();
    }

    public function isSuccess(): bool
    {
        return $this->success;
    }

    public function toArray(): array
    {
        return $this->data;
    }

    public function __get(string $name)
    {
        return $this->data[$name] ?? null;
    }
}