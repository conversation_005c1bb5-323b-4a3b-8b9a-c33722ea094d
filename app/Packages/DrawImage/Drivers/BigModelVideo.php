<?php

namespace App\Packages\DrawImage\Drivers;

use App\Models\DrawVideo;
use App\Packages\DrawImage\BaseDriver;
use App\Packages\DrawImage\DrawResponse;

class BigModelVideo extends BaseDriver
{
    private $api_key;
    private $url = "https://open.bigmodel.cn/api/paas/v4/videos/generations";

    public function __construct()
    {
        $this->api_key = env('ZHIPU_APPKEY', '');
    }

    public function draw(DrawVideo $video): DrawResponse
    {
        $this->setUrl($this->url);
        $prompt = $video->prompt;
        if ($video->style) {
            $prompt = $video->style->prefix.','.$prompt;
        }
        $this->setParams([
            'model'      => $video->model,
            'prompt'     => $prompt,
            'quality'    => $video->quality,
            'image_url'  => $video->image_url,
            'size'       => $video->size->getText(),
            'with_audio' => $video->with_audio ? true : false,
            'request_id' => $video->no,
        ]);
        $this->setHeaders([
            'Content-Type'  => 'application/json',
            'Authorization' => 'Bearer '.$this->api_key,
        ]);
        return $this->json();
    }

    public function query(string $id): DrawResponse
    {
        $this->url = 'https://open.bigmodel.cn/api/paas/v4/async-result/'.$id;
        $this->setUrl($this->url);
        $this->setParams([]);
        $this->setHeaders([
            'Content-Type'  => 'application/json',
            'Authorization' => 'Bearer '.$this->api_key,
        ]);
        return $this->get();
    }
}