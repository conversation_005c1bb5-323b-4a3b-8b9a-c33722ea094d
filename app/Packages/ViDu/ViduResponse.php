<?php

namespace App\Packages\ViDu;

use Psr\Http\Message\ResponseInterface;

class ViduResponse
{
    protected bool   $success = false;
    protected string $message = '';
    protected array  $data    = [];
    protected array  $params  = [];

    public function __construct(ResponseInterface|string $response, array $params = [])
    {
        $this->params = $params;

        if ($response instanceof ResponseInterface) {
            $this->handleResponse($response);
        } else {
            $this->handleError($response);
        }
    }

    protected function handleResponse(ResponseInterface $response): void
    {
        try {
            $content = $response->getBody()->getContents();
            $result  = json_decode($content, true);

            if ($response->getStatusCode() === 200) {
                if ($result['state'] == 'failed') {
                    $this->handleError(ViDuException::getErrorMessage($result['err_code']) ?? '未知错误');
                }
                $this->success = true;
                $this->data    = $result;
            } else {
                $this->handleError(ViDuException::getErrorMessage($result['err_code']) ?? '未知错误');
            }
        } catch (\Exception $e) {
            $this->handleError('响应数据解析失败: '.$e->getMessage());
        }
    }

    protected function handleError(string $message): void
    {
        $this->success = false;
        $this->message = $message;
        $this->data    = [];
    }

    public static function error(string $message, array $params = []): self
    {
        return new static($message, $params);
    }

    public function isError(): bool
    {
        return ! $this->isSuccess();
    }

    public function isSuccess(): bool
    {
        return $this->success;
    }

    public function getMessage(): string
    {
        return $this->message;
    }

    public function getData(): array
    {
        return $this->data;
    }

    public function toArray(): array
    {
        return [
            'success' => $this->success,
            'message' => $this->message,
            'data'    => $this->data,
            'params'  => $this->params
        ];
    }

    public function __get(string $name): mixed
    {
        return $this->data[$name] ?? null;
    }

    public function __isset(string $name): bool
    {
        return isset($this->data[$name]);
    }
}