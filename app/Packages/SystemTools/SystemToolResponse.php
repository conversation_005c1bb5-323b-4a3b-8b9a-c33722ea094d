<?php

namespace App\Packages\SystemTools;

use Illuminate\Support\Collection;
use Psr\Http\Message\ResponseInterface;

class SystemToolResponse
{
    protected bool   $success;
    protected string $message;
    protected array  $data;

    /**
     * @param  \Psr\Http\Message\ResponseInterface|string  $response
     * @param  array  $params
     */
    public function __construct(
        ResponseInterface|string $response,
        protected array $params = []
    ) {
        if ($response instanceof ResponseInterface) {
            if ($response->getStatusCode() == 200) {
                $data          = json_decode($response->getBody()->getContents(),
                    true);
                $this->success = true;
                $this->data    = $data;
            } else {
                $this->success = false;
                $this->message = $response->getBody()->getContents();
            }
        } else {
            $this->success = false;
            $this->message = $response;
        }
    }

    /**
     * @param  string  $message
     * @param  array  $params
     * @return \App\Packages\SystemTools\SystemToolResponse
     */
    public static function error(string $message, array $params): SystemToolResponse
    {
        return new static($message, $params);
    }

    public function isError(): bool
    {
        return ! $this->isSuccess();
    }

    public function isSuccess(): bool
    {
        return $this->success;
    }

    public function getMessage(): string
    {
        return $this->message;
    }

    public function toArray(): array
    {
        return $this->data;
    }

    public function toCollect(): Collection
    {
        return collect($this->data);
    }

    public function __get(string $name): mixed
    {
        return $this->data[$name] ?? null;
    }
}