<?php

namespace App\Packages\BailianAssistant;

use App\Models\BailianAssistant;
use App\Models\BailianAssistantFile;
use App\Models\BailianAssistantLog;
use App\Models\BailianAssistantLogAction;
use App\Packages\AlibabaCloud\Alibaba;
use App\Packages\BailianAssistant\Traits\SendChatTrait;
use Exception;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use Psr\Http\Message\StreamInterface;

class Completion extends BaseClient
{
    use SendChatTrait;

    protected        $response;
    protected string $output        = '';
    protected string $reasoning     = '';
    protected string $lastAction    = '';
    protected array  $docReferences = [];
    protected string $tempArguments = '';
    protected        $currect       = [
        'id'          => '',
        'event'       => '',
        'code'        => '',
        'arguments'   => '',
        'observation' => '',
    ];
    protected array  $actions       = [];
    protected int    $step          = 3;
    protected string $tempFile      = '';

    public function __construct(ServiceConfig $serviceConfig)
    {
        parent::__construct($serviceConfig);
        $this->tempFile = "logs/temp.log";
    }

    public function chat(string $message, BailianAssistant $group)
    {
        try {
            $this->response = $this->sendChat(
                message: $message,
                group: $group,
            );
            if ($this->response->getStatusCode() == 200) {
                $log           = $group->logs()->create([
                    'user_id' => $group->user_id,
                    'prompt'  => $message,
                ]);
                $lastSessionId = '';
                foreach ($this->getIterator() as $data) {
                    $code = (int) $data['code'];

                    if ($code !== 200) {
                        $errorData = $data['data'];
                        switch ($errorData['code']) {
                            case 'DataInspectionFailed':
                                $errorMessage = Alibaba::aichat()->aiChat()->mt(
                                    text: $errorData['message'],
                                    domains: '这是一段错误提示',
                                );
                                $this->out(
                                    step: 0,
                                    type: 'msg',
                                    message: $errorMessage,
                                );
                                $this->setErrorLog($log, $errorMessage);

                                break;
                        }
                    }
                    $outPut        = $data['data']['output'];
                    $lastSessionId = $outPut['session_id'];
                    $requestId     = $data['data']['request_id'] ?? '';
                    $thoughts      = collect($outPut['thoughts'] ?? []);
                    if (! blank($thoughts)) {
                        $this->Fixed($thoughts, 'rag');
                        $this->Fixed($thoughts, 'memory');
                        $action = $thoughts->whereNotIn('action', ['memory', 'rag'])->last();
                        if ($action) {
                            $reason      = $action['response'] ?? '';
                            $arguments   = $action['arguments'] ?? '';
                            $observation = $action['observation'] ?? '';

                            if ($action['action'] != $this->lastAction) {
                                $step = count($this->actions) + 1;

                                $this->actions[] = [
                                    'step'        => $step,
                                    'action'      => $action['action'],
                                    'action_name' => $action['action_name'],
                                    'action_type' => $action['action_type'],
                                    'message'     => $reason,
                                    'arguments'   => $arguments,
                                    'observation' => $observation,
                                ];
                            } else {
                                $key                                = array_key_last($this->actions);
                                $step                               = $this->actions[$key]['step'];
                                $this->actions[$key]['message']     .= $reason;
                                $this->actions[$key]['arguments']   .= $arguments;
                                $this->actions[$key]['observation'] .= $observation;
                            }
                            if (! blank($reason) || ! blank($arguments) || ! blank($observation)) {
                                $this->out(
                                    step: $step,
                                    type: $action['action_type'],
                                    message: $reason ?: $arguments,
                                    data: Str::isJson($observation) ? json_decode($observation, true) : [],
                                    action: $action['action'],
                                    action_name: $action['action_name'],
                                    action_type: $action['action_type']
                                );
                            }
                            $this->lastAction = $action['action'];
                        }
                    }

                    $output = $outPut['text'] ?? '';

                    $docReferences = $outPut['doc_references'] ?? [];
                    if (! empty($docReferences)) {
                        $this->docReferences = $docReferences;
                    }

                    if (! blank($output)) {
                        $this->out(
                            step: 0,
                            type: 'msg',
                            message: $output,
                        );
                        $this->output .= $output;
                    }

                    $finish = $outPut['finish_reason'] ?? '';
                    if ($finish == 'stop') {
                        if (! empty($this->docReferences)) {
                            $this->out(
                                step: 0,
                                type: 'doc',
                                data: $this->docReferences,
                            );
                        }
                        $usage = $data['data']['usage']['models'] ?? [];
                        $log->update([
                            'output'     => $this->output,
                            'usage'      => $usage,
                            'doc'        => $this->docReferences,
                            'input'      => empty($this->imageUrls) ? null : $this->imageUrls,
                            'session_id' => $lastSessionId,
                            'request_id' => $requestId,
                        ]);
                        if (! empty($this->sessionFileIds)) {
                            BailianAssistantFile::whereIn('file_id', $this->sessionFileIds)
                                ->update([
                                    'log_id' => $log->id,
                                ]);
                        }
                        if (! empty($this->knowledgeIds)) {
                            $log->knowledges()->createMany(collect($this->knowledgeIds)->map(function ($knowledgeId) {
                                return [
                                    'knowledge_id' => $knowledgeId,
                                ];
                            })->toArray());
                        }
                        foreach ($this->actions as $action) {
                            BailianAssistantLogAction::updateOrCreate([
                                'group_id' => $group->id,
                                'log_id'   => $log->id,
                                'step'     => $action['step'],
                                'action'   => $action['action'],
                            ], [
                                'action_name' => $action['action_name'],
                                'action_type' => $action['action_type'],
                                'message'     => $action['message'] ?? '',
                                'arguments'   => Str::isJson($action['arguments']) ? json_decode($action['arguments'],
                                    true) : (is_array($action['arguments']) ? $action['arguments'] : [$action['arguments']]),
                                'observation' => Str::isJson($action['observation']) ? json_decode($action['observation'],
                                    true) : (is_array($action['observation']) ? $action['observation'] : [$action['observation']]),
                            ]);
                        }
                    }
                }
                $this->out(
                    step: 0,
                    type: 'log',
                    data: [
                        'id'         => $log->id,
                        'session_id' => $log->session_id,
                        'request_id' => $log->request_id,
                    ],
                );
                $group->update([
                    'session_id' => $lastSessionId
                ]);
                ob_end_clean();
            } else {
                throw new Exception($this->response->getBody()->getContents());
            }
        } catch (Exception $e) {
            Log::error($e->getMessage());
            return false;
        }
    }

    public function getIterator()
    {
        while (! $this->response->getBody()->eof()) {
            $line = $this->readLine($this->response->getBody());
            Log::channel('aiChat')->info($line);
            if (str_starts_with($line, 'id:')) {
                $this->currect['id'] = trim(substr($line, strlen('id:')));
                continue;
            }
            if (str_starts_with($line, 'event:')) {
                $this->currect['event'] = trim(substr($line, strlen('event:')));
                continue;
            }
            if (str_starts_with($line, ':HTTP_STATUS/')) {
                $this->currect['code'] = trim(substr($line, strlen(':HTTP_STATUS/')));
                continue;
            }
            if (str_starts_with($line, 'data:')) {
                $this->currect['data'] = json_decode(trim(substr($line, strlen('data:'))), true);
                yield $this->currect;
            }
        }
    }

    private function readLine(StreamInterface $stream): string
    {
        $buffer = '';

        while (! $stream->eof()) {
            if ('' === ($byte = $stream->read(1))) {
                return $buffer;
            }
            $buffer .= $byte;
            if ($byte === "\n") {
                break;
            }
        }

        return $buffer;
    }

    protected function setErrorLog(BailianAssistantLog $log, string $message)
    {
        $log->update([
            'output' => $message,
        ]);
    }

    private function Fixed(Collection $thoughts, string $action)
    {
        $action = $thoughts->where('action', $action)
            ->whereNotNull('observation')
            ->first();
        if ($action && ! blank($action['observation'])) {
            $step        = count($this->actions) + 1;
            $arguments   = json_decode($action['arguments'], true);
            $observation = json_decode($action['observation'], true);
            $this->out(step: $step,
                type: 'rag',
                data: ['arguments' => $arguments, 'observation' => $observation],
                action: $action['action'],
                action_name: $action['action_name'],
                action_type: $action['action_type']);
            $this->actions[]  = [
                'step'        => $step,
                'action'      => $action['action'],
                'action_name' => $action['action_name'],
                'action_type' => $action['action_type'],
                'message'     => '',
                'arguments'   => $arguments,
                'observation' => $observation,
            ];
            $this->lastAction = $action['action'];
        }
    }

}