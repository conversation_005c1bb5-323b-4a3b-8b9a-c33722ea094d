<?php

namespace App\Packages\BailianAssistant\Tools;

use Exception;
use GuzzleHttp\Client;

class Gaode
{
    protected Client $client;
    protected string $key = '6e697770e591e846dccd7a5f0fd06ca6';

    public function __construct()
    {
        $this->client = new Client([
            'base_uri' => 'https://restapi.amap.com/v3/',
            'verify'   => false,
        ]);
    }

    public function getAdCodeCity(string $city)
    {
        $response = $this->client->get('geocode/geo', [
            'query' => [
                'key'     => $this->key,
                'address' => $city,
                'city'    => $city,
            ],
        ]);
        $data     = json_decode($response->getBody()->getContents(), true);
        if ($data['status'] !== '1') {
            throw new Exception($data['info']);
        }
        $adCode = $data['geocodes'][0]['adcode'] ?? '';
        return $adCode;
    }

    public function getAddressForIp(string $ip)
    {
        $query  = [
            'key' => $this->key,
            'ip'  => $ip,
        ];
        $result = $this->client->get('/ip', [
            'query' => $query,
        ]);
        $data   = json_decode($result->getBody()->getContents(), true);
        if ($data['status'] !== '1') {
            throw new Exception($data['info']);
        }
        return $data['forecasts'];
    }

    public function getWeather(string $adCode)
    {
        $query  = [
            'key'        => $this->key,
            'city'       => $adCode,
            'extensions' => 'all'
        ];
        $result = $this->client->get('https://restapi.amap.com/v3/weather/weatherInfo', [
            'query' => $query,
        ]);
        $data   = json_decode($result->getBody()->getContents(), true);
        if ($data['status'] !== '1') {
            throw new Exception($data['info']);
        }
        return $data['forecasts'];
    }

    public function getAddress($lat, $lng)
    {
        $query  = [
            'key'      => $this->key,
            'location' => sprintf('%s,%s', $lng, $lat),
        ];
        $result = $this->client->get('geocode/regeo', [
            'query' => $query,
        ]);
        $data   = json_decode($result->getBody()->getContents(), true);

        if ($data['status'] !== '1') {
            throw new Exception($data['info']);
        }
        return $data;
    }

}