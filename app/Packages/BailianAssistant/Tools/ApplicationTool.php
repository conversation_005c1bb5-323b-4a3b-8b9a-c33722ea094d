<?php

namespace App\Packages\BailianAssistant\Tools;

use AlibabaCloud\SDK\Bailian\V20231229\Models\AddCategoryRequest;
use AlibabaCloud\SDK\Bailian\V20231229\Models\AddCategoryResponseBody;
use AlibabaCloud\SDK\Bailian\V20231229\Models\DeleteCategoryResponseBody;
use AlibabaCloud\SDK\Bailian\V20231229\Models\ListCategoryRequest;
use AlibabaCloud\SDK\Bailian\V20231229\Models\ListCategoryResponseBody;
use AlibabaCloud\SDK\Bailian\V20231229\Models\ListFileRequest;
use AlibabaCloud\SDK\Bailian\V20231229\Models\ListFileResponseBody;
use AlibabaCloud\Tea\Exception\TeaError;
use App\Packages\BailianAssistant\BaseClient;
use Exception;

class ApplicationTool extends BaseClient
{
    /**
     * @param  string  $categoryId
     * @param  string  $nextToken
     * @return \AlibabaCloud\SDK\Bailian\V20231229\Models\ListFileResponseBody
     */
    public function ListFile(string $categoryId, string $nextToken = ''): ListFileResponseBody
    {
        $request = new ListFileRequest([
            'categoryId' => $categoryId,
            'maxResults' => 50,
            'nextToken'  => $nextToken,
        ]);
        try {
            // 复制代码运行请自行打印 API 的返回值
            $response = $this->client->listFileWithOptions($this->serviceConfig->getWorkspaceId(), $request,
                $this->headers,
                $this->runtime);
            return $response->body;
        } catch (Exception $error) {
            if (! ($error instanceof TeaError)) {
                $error = new TeaError([], $error->getMessage(), $error->getCode(), $error);
            }
            throw $error;
        }
    }

    /**
     * @param  string  $name
     * @param  string  $parentId
     * @return \AlibabaCloud\SDK\Bailian\V20231229\Models\AddCategoryResponseBody
     */
    public function AddCategory(string $name, string $parentId = ''): AddCategoryResponseBody
    {
        $request = new AddCategoryRequest([
            'categoryName'     => $name,
            'parentCategoryId' => $parentId,
            'categoryType'     => 'UNSTRUCTURED',
        ]);
        try {
            // 复制代码运行请自行打印 API 的返回值
            $response = $this->client->addCategoryWithOptions($this->serviceConfig->getWorkspaceId(),
                $request,
                $this->headers,
                $this->runtime);
            return $response->body;
        } catch (Exception $error) {
            if (! ($error instanceof TeaError)) {
                $error = new TeaError([], $error->getMessage(), $error->getCode(), $error);
            }
            throw $error;
        }
    }

    /**
     * @param  string  $parentCategoryId
     * @param  string  $nextToken
     * @return \AlibabaCloud\SDK\Bailian\V20231229\Models\ListCategoryResponseBody
     */
    public function ListCategory(string $parentCategoryId = '', string $nextToken = ''): ListCategoryResponseBody
    {
        $request = new ListCategoryRequest([
            'categoryType'     => 'UNSTRUCTURED',
            'maxResults'       => 50,
            'nextToken'        => $nextToken,
            'parentCategoryId' => $parentCategoryId,
        ]);
        try {
            // 复制代码运行请自行打印 API 的返回值
            $response = $this->client->listCategoryWithOptions($this->serviceConfig->getWorkspaceId(),
                $request,
                $this->headers,
                $this->runtime);
            return $response->body;
        } catch (Exception $error) {
            if (! ($error instanceof TeaError)) {
                $error = new TeaError([], $error->getMessage(), $error->getCode(), $error);
            }
            throw $error;
        }
    }

    /**
     * @param  string  $categoryId
     * @return \AlibabaCloud\SDK\Bailian\V20231229\Models\DeleteCategoryResponseBody
     */
    public function DeleteCategory(string $categoryId): DeleteCategoryResponseBody
    {
        try {
            // 复制代码运行请自行打印 API 的返回值
            $response = $this->client->deleteCategoryWithOptions($categoryId, $this->serviceConfig->getWorkspaceId(),
                $this->headers,
                $this->runtime);
            return $response->body;
        } catch (Exception $error) {
            if (! ($error instanceof TeaError)) {
                $error = new TeaError([], $error->getMessage(), $error->getCode(), $error);
            }
            throw $error;
        }
    }

}