<?php

namespace App\Packages\BailianAssistant\Traits;

trait SendChatTrait
{

    protected function normalOut(string $type, string $message = '', array $data = [])
    {
        echo 'data: '.json_encode([
                'type' => $type,
                'msg'  => $message,
                'data' => $data ?: [],
            ], JSON_UNESCAPED_UNICODE)."\n\n";
        ob_flush();
        flush();
        usleep(2000);
    }

    protected function out(
        int $step,
        string $type,
        string $message = '',
        array $data = [],
        string $action = '',
        string $action_name = '',
        string $action_type = ''
    ) {
        echo 'data: '.json_encode([
                'step'        => $step,
                'type'        => $type,
                'action'      => $action,
                'action_name' => $action_name,
                'action_type' => $action_type,
                'msg'         => $message,
                'data'        => $data ?: '',
            ], JSON_UNESCAPED_UNICODE)."\n\n";
        ob_flush();
        flush();
        usleep(2000);
    }

}