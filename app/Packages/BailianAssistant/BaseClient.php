<?php

namespace App\Packages\BailianAssistant;

use AlibabaCloud\Credentials\Credential;
use AlibabaCloud\SDK\Bailian\V20231229\Bailian;
use AlibabaCloud\Tea\Utils\Utils\RuntimeOptions;
use App\Models\BailianAssistant;
use Darabonba\OpenApi\Models\Config;
use Exception;
use GuzzleHttp\Client;
use Illuminate\Support\Facades\Log;

class BaseClient
{
    public RuntimeOptions $runtime;
    public array          $headers = [];
    protected Bailian     $client;
    /**
     * 默认的模型ID
     * qwen-vl-plus 多模态
     *
     * @var string
     */
    protected string $modelId = 'qwen-turbo-latest';// qwen-turbo-latest
    protected        $logger;

    protected bool  $think          = true;
    protected array $knowledgeIds   = [];
    protected array $fileIds        = [];
    protected array $sessionFileIds = [];
    protected array $imageUrls      = [];
    protected bool  $webSearch      = true;
    protected array $other          = [];
    //行为模式：开启联网搜索和不开启联网搜索附加的提示词
    protected string $behavior_mode = '';

    public function __construct(public ServiceConfig $serviceConfig)
    {
        $credential       = new Credential([
            'type'              => 'access_key',
            'access_key_id'     => $this->serviceConfig->getAccessKeyId(),
            'access_key_secret' => $this->serviceConfig->getAccessKeySecret(),
        ]);
        $config           = new Config([
            'credential' => $credential,
        ]);
        $config->endpoint = "bailian.cn-beijing.aliyuncs.com";
        $this->client     = new Bailian($config);
        $this->runtime    = new RuntimeOptions([
            "ignoreSSL" => true
        ]);
        $this->logger     = Log::channel('bailian');
    }

    /**
     * 设置知识库ID
     *
     * @param  array|string  $knowledgeId
     * @return $this
     */
    public function setKnowledgeId(array|string $knowledgeId): self
    {
        if (is_string($knowledgeId)) {
            $this->knowledgeIds = explode(',', $knowledgeId);
        } elseif (is_array($knowledgeId)) {
            $this->knowledgeIds = $knowledgeId;
        }
        return $this;
    }

    public function setSessionFileIds(array|string $sessionFileIds): self
    {
        if (is_string($sessionFileIds)) {
            $this->sessionFileIds = explode(',', $sessionFileIds);
        } elseif (is_array($sessionFileIds)) {
            $this->sessionFileIds = $sessionFileIds;
        }
        return $this;
    }

    public function setImageUrl(array|string $sessionFileIds): self
    {
        if (is_string($sessionFileIds)) {
            $array = explode(',', $sessionFileIds);
        } elseif (is_array($sessionFileIds)) {
            $array = $sessionFileIds;
        }
        $array = array_filter($array);
        if (count($array) > 0) {
            if (count($array) > 9) {
                throw new Exception('上传的图片不可超过9个');
            }
            $this->modelId = 'qwen-vl-max-latest';
            foreach ($array as $key => $item) {
                $array[$key] = $item.'?x-oss-process=image/resize,m_mfit,h_512,w_512,limit_0';
            }
            $this->imageUrls = $array;
        }

        return $this;
    }

    public function setThink(bool $think): self
    {
        $this->think = $think;
        return $this;
    }

    public function setWebSearch(bool $webSearch): self
    {
        $this->webSearch = $webSearch;
        return $this;
    }

    public function setOther(array $other): self
    {
        $this->other = array_merge($this->other, $other);
        return $this;
    }

    public function sendKnowledgeChat(string $message, string $sessionId = '')
    {
        $client = new Client([
            'base_uri' => 'https://dashscope.aliyuncs.com/api/v1/apps/',
            'verify'   => false,
        ]);
        $data   = [
            'input'      => [
                'prompt'     => $message,
                'session_id' => $sessionId,
            ],
            'stream'     => true,
            'parameters' => [
                'max_tokens'         => 8192,
                'model_id'           => $this->modelId,
                'rag_options'        => [
                    'pipeline_ids'     => array_filter($this->knowledgeIds),
                    'file_ids'         => array_filter($this->fileIds),
                    'session_file_ids' => array_filter($this->sessionFileIds),
                ],
                'enable_thinking'    => $this->think,
                'enable_web_search'  => $this->webSearch,
                'has_thoughts'       => true, //是否思考
                'flow_stream_mode'   => 'full_thoughts',
                'incremental_output' => true,//流失增量输出
                'behavior_mode'      => $this->behavior_mode
            ],
        ];
        return $client->post($this->serviceConfig->getAppId().'/completion', [
            'headers' => [
                'Content-Type'          => 'application/json',
                'Authorization'         => 'Bearer '.$this->serviceConfig->getApiKey(),
                'X-DashScope-SSE'       => 'enable',
                'X-DashScope-WorkSpace' => $this->serviceConfig->getWorkspaceId(),
            ],
            'json'    => $data,
            'stream'  => true,
        ]);
    }

    public function setFileIds(array|string $fileIds): self
    {
        if (is_string($fileIds)) {
            $this->fileIds = explode(',', $fileIds);
        } elseif (is_array($fileIds)) {
            $this->fileIds = $fileIds;
        }
        return $this;
    }

    public function sendChat(
        string $message,
        BailianAssistant $group,
    ) {
        $client = new Client([
            'base_uri' => 'https://dashscope.aliyuncs.com/api/v1/apps/',
            'verify'   => false,
        ]);
        $invite = app('user.hashids')->encode($group->user_id);
        if (count($this->imageUrls) > 0) {
            $message .= "\n\nimage_list:".json_encode($this->imageUrls, JSON_UNESCAPED_UNICODE);
        }
        $data = [
            'input'      => [
                'prompt'     => $message,//提问内容
                'memory_id'  => $group->memory_id,//长记忆ID
                'image_list' => $this->imageUrls,//图片列表
                'biz_params' => [
                    'user_prompt_params' => [
                        'wateauth' => $invite,
                        'score'    => $group->user->account->score,
                        'lat'      => $this->other['lat'] ?? '',
                        'lng'      => $this->other['lng'] ?? '',
                        'ip'       => $this->other['ip'] ?? '',
                    ],
                ],
            ],
            'stream'     => true,
            'parameters' => [
                'max_tokens'         => 8192,//最大输出token
                'model_id'           => $this->modelId,//模型名称ID
                'dialog_round'       => 5,//上下问轮数 1-30
                'rag_options'        => [
                    'pipeline_ids'     => array_filter($this->knowledgeIds),//知识库ID
                    'file_ids'         => array_filter($this->fileIds),//知识库文件ID
                    'session_file_ids' => array_filter($this->sessionFileIds),//临时文件ID
                ],
                'enable_thinking'    => true,//是否开启思考
                'enable_web_search'  => true,//是否启用联网搜索
                'has_thoughts'       => true,//是否返回思考过程   $this->think
                'flow_stream_mode'   => 'full_thoughts',
                'incremental_output' => true//流失增量输出
            ],
        ];
        if (count($this->imageUrls) > 0) {
            $data['parameters']['enable_thinking'] = false;
        }
        if ($group->session_id) {
            $data['input']['session_id'] = $group->session_id;
        }
        return $client->post($this->serviceConfig->getAppId().'/completion', [
            'headers' => [
                'Content-Type'          => 'application/json',
                'Authorization'         => 'Bearer '.$this->serviceConfig->getApiKey(),
                'X-DashScope-SSE'       => 'enable',
                'X-DashScope-WorkSpace' => $this->serviceConfig->getWorkspaceId(),
            ],
            'json'    => $data,
            'stream'  => true,
        ]);
    }

}