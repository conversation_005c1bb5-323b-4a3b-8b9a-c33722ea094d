<?php

namespace App\Packages\BailianAssistant;

class KnowledgeConfig extends ServiceConfig
{
    public function __construct()
    {
        parent::__construct();
        $this->apiKey          = env('BAILIAN_API_KEY', '');
        $this->appId           = env('BAILIAN_KNOWLEDGE_APP_ID', '');
        $this->workspaceId     = env('BAILIAN_WORKSPACE_ID', '');
        $this->accessKeyId     = env('BAILIAN_ACCESS_KEY_ID', '');
        $this->accessKeySecret = env('BAILIAN_ACCESS_KEY_SECRET', '');
    }
}