<?php

namespace App\Packages\BailianAssistant;

use App\Packages\BailianAssistant\Tools\ApplicationTool;
use App\Packages\BailianAssistant\Tools\KnowledgeTool;
use App\Packages\BailianAssistant\Tools\MemoryTool;
use App\Packages\BailianAssistant\Tools\UnderstandingTool;

class BLAssistant
{
    protected ServiceConfig $serviceConfig;

    public function __construct()
    {
        $this->serviceConfig = new ServiceConfig();
    }

    public function completion(): Completion
    {
        return new Completion($this->serviceConfig);
    }

    public function knowledgeChat(): KnowledgeChat
    {
        $this->serviceConfig = new KnowledgeConfig();
        return new KnowledgeChat($this->serviceConfig);
    }

    public function application(): ApplicationTool
    {
        return new ApplicationTool($this->serviceConfig);
    }

    public function memory(): MemoryTool
    {
        return new MemoryTool($this->serviceConfig);
    }

    public function knowledge(): KnowledgeTool
    {
        return new KnowledgeTool($this->serviceConfig);
    }

    public function understanding(): UnderstandingTool
    {
        return new UnderstandingTool();
    }
}