<?php

namespace App\Packages\BailianAssistant;

use App\Models\BailianKnowledge;
use App\Models\BailianKnowledgeChat;
use App\Packages\BailianAssistant\Traits\SendChatTrait;
use Exception;
use Illuminate\Support\Collection;
use Psr\Http\Message\StreamInterface;

class KnowledgeChat extends BaseClient
{
    use SendChatTrait;

    protected                      $response;
    protected string               $output        = '';
    protected string               $reasoning     = '';
    protected string               $lastAction    = '';
    protected array                $docReferences = [];
    protected BailianKnowledgeChat $chat;
    protected array                $currect       = [
        'id'          => '',
        'event'       => '',
        'code'        => '',
        'arguments'   => '',
        'observation' => '',
    ];
    protected array                $actions       = [];
    protected int                  $step          = 3;

    public function __construct(KnowledgeConfig $serviceConfig)
    {
        parent::__construct($serviceConfig);
    }

    public function chat(string $message, $user, string $sessionId = '')
    {
        $this->chat    = BailianKnowledgeChat::create([
            'user_id'       => $user->id,
            'input_message' => $message,
            'inputs'        => [
                'is_think'         => $this->think,
                'session_file_ids' => $this->sessionFileIds,
                'is_web_search'    => $this->webSearch,
            ],
        ]);
        $behavior_mode = '当知识库和文档中都没有相关信息时，回复：【没有找到相关知识，我会努力学习的。】';
        if ($this->webSearch) {
            $behavior_mode = "当知识库和文档中都没有相关信息时，进行联网搜索获取相关信息。";
        }
        $this->behavior_mode = $behavior_mode;
        $this->response      = $this->sendKnowledgeChat(
            message: $message,
            sessionId: $sessionId,
        );
        if ($this->response->getStatusCode() == 200) {
            #TODO 创建记录
            $lastSessionId = '';
            foreach ($this->getIterator() as $data) {
                $outPut        = $data['data']['output'];
                $lastSessionId = $outPut['session_id'];
                $thoughts      = collect($outPut['thoughts'] ?? []);
                if (! blank($thoughts)) {
                    $this->Fixed($thoughts, 'rag');
                    $action = $thoughts->where('action', 'reasoning')->last();
                    if ($action) {
                        $reason = $action['response'] ?? '';
                        if (! blank($reason)) {
                            $this->reasoning .= $reason;
                            $this->normalOut(
                                type: 'reasoning',
                                message: $reason
                            );
                        }
                    }
                }
                $docReferences = $outPut['doc_references'] ?? [];
                if (! empty($docReferences)) {
                    $this->docReferences = $docReferences;
                }
                $output = $outPut['text'] ?? '';
                if (! blank($output)) {
                    $this->normalOut(
                        type: 'msg',
                        message: $output,
                    );
                    $this->output .= $output;
                }
                $finish = $outPut['finish_reason'] ?? '';
                if ($finish == 'stop') {
                    $this->normalOut(
                        type: 'doc', message: '', data: $this->docReferences,
                    );
                    $this->normalOut(
                        type: 'chat',
                        message: '',
                        data: ['chat_id' => $this->chat->id],
                    );
                    $usage = $data['data']['usage']['models'][0] ?? [];

                    $this->chat->update([
                        'usage'          => $usage,
                        'session_id'     => $lastSessionId,
                        'output_message' => $this->output,
                    ]);

                    if ($this->knowledgeIds) {
                        $knowledgeIds = BailianKnowledge::query()
                            ->whereIn('knowledge_id', $this->knowledgeIds)
                            ->pluck('id')
                            ->toArray();
                        $this->chat->knowledge()->sync($knowledgeIds);
                    }
                }
            }
            ob_end_clean();
        } else {
            throw new Exception($this->response->getBody()->getContents());
        }
    }

    public function getIterator()
    {
        while (! $this->response->getBody()->eof()) {
            $line = $this->readLine($this->response->getBody());
            if (str_starts_with($line, 'id:')) {
                $this->currect['id'] = trim(substr($line, strlen('id:')));
                continue;
            }
            if (str_starts_with($line, 'event:')) {
                $this->currect['event'] = trim(substr($line, strlen('event:')));
                continue;
            }
            if (str_starts_with($line, ':HTTP_STATUS/')) {
                $this->currect['code'] = trim(substr($line, strlen(':HTTP_STATUS/')));
                continue;
            }
            if (str_starts_with($line, 'data:')) {
                $this->currect['data'] = json_decode(trim(substr($line, strlen('data:'))), true);
                yield $this->currect;
            }
        }
    }

    private function readLine(StreamInterface $stream): string
    {
        $buffer = '';

        while (! $stream->eof()) {
            if ('' === ($byte = $stream->read(1))) {
                return $buffer;
            }
            $buffer .= $byte;
            if ($byte === "\n") {
                break;
            }
        }

        return $buffer;
    }

    private function Fixed(Collection $thoughts, string $action)
    {
        $action = $thoughts->where('action', $action)
            ->whereNotNull('observation')
            ->first();
        if ($action && ! blank($action['observation'])) {
            $arguments   = json_decode($action['arguments'], true);
            $observation = json_decode($action['observation'], true);
            $this->normalOut(
                type: 'rag',
                data: ['arguments' => $arguments, 'observation' => $observation],
            );
        }
    }
}