<?php

namespace App\Packages\JiMeng\Helper;

use App\Packages\JiMeng\BaseClient;
use App\Packages\JiMeng\JmResponse;
use Aws\Credentials\Credentials;
use Exception;
use GuzzleHttp\Client;
use GuzzleHttp\Psr7\Request;

/**
 * 上传文件助手类，用于处理与文件上传相关的操作
 */
class UploadFile extends BaseClient
{
    // 认证信息
    public string $type;
    // 文件内容
    protected string $auth = '';
    // 上传令牌结果对象
    protected $fileHandle = null;
    // 存储信息数组
    private JmResponse $tokenResult;
    // 上传主机
    private array $storeInfos = [];
    // 会话密钥
    private string $uploadHost = '';
    // AWS凭证对象
    private string $SessionKey = '';
    // 签名对象
    private Credentials $credentials;
    private Signature   $imagexSignature;
    // 配置信息数组
    private Signature $vodSignature;
    // 文件类型
    private array $config;

    /**
     * 构造函数，初始化上传文件所需的各项参数和对象
     *
     * @param  string  $fileUrl  文件URL
     */
    public function __construct(protected string $fileUrl = '')
    {
        parent::__construct();
        $this->fileHandle = fopen($this->fileUrl, 'r');
        if ($this->fileHandle === false) {
            throw new Exception("无法打开文件: {$this->fileUrl}");
        }
        // 计算文件大小
        $this->fileSize = $this->getRemoteFileSize($this->fileUrl);
        if ($this->fileSize <= 0) {
            throw new Exception("获取文件大小失败: {$this->fileUrl}");
        }
        // 根据文件扩展名判断文件类型
        $ext        = pathinfo($this->fileUrl, PATHINFO_EXTENSION);
        $this->type = match ($ext) {
            'mp4', 'avi', 'mov', 'mkv', 'flv', 'mp3' => 'video',
            default => 'image',
        };
        // 根据文件类型获取上传令牌
        $this->tokenResult = match ($this->type) {
            'image' => $this->withAid()->json('mweb/v1/get_upload_token', [
                'scene' => 2,
            ]),
            'video' => $this->withAid()->json('mweb/v1/get_upload_token', [
                'scene' => 1,
            ]),
        };

        // 创建AWS凭证对象
        $this->credentials = new Credentials($this->tokenResult->access_key_id,
            $this->tokenResult->secret_access_key,
            $this->tokenResult->session_token);
        // 创建签名对象
        $this->imagexSignature = new Signature('imagex', 'cn-north-1');
        $this->vodSignature    = new Signature('vod', 'cn-north-1');

        // 根据文件类型申请上传配置
        $this->config = match ($this->type) {
            'image' => $this->ApplyImageUpload(),
            'video' => $this->ApplyUploadInner(),
        };
        // 获取内部上传地址配置
        $innerUploadAddress = $this->config['Result']['InnerUploadAddress']['UploadNodes'][0];
        // 设置存储信息
        $this->storeInfos = $innerUploadAddress['StoreInfos'][0];
        // 设置上传主机
        $this->uploadHost = $innerUploadAddress['UploadHost'];

        // 设置会话密钥
        $this->SessionKey = $innerUploadAddress['SessionKey'];
    }

    private function getRemoteFileSize($url)
    {
        try {
            $client        = new Client([
                'verify' => false,
            ]);
            $ContentLength = $client->request('HEAD', $url)->getHeader('Content-Length');
            return $ContentLength[0];
        } catch (Exception $e) {
            return -1;
        }
    }

    /**
     * 申请图片上传
     *
     * @return array 上传配置信息
     */
    public function ApplyImageUpload(): array
    {
        // 获取空间名称作为服务ID
        $ServiceId = $this->tokenResult->space_name;

        // 构建请求查询数据数组
        $queryData = [
            'Action'    => 'ApplyImageUpload',
            'Version'   => '2018-08-01',
            'ServiceId' => $ServiceId,
            'FileSize'  => (int) $this->fileSize,
            's'         => '0p99oedxrbee'
        ];

        // 将查询数据数组转换为查询字符串
        $queryString = http_build_query($queryData);

        // 获取上传域名作为基础URI
        $baseUri = $this->tokenResult->upload_domain;

        // 创建GET请求对象
        $request = new Request('GET', sprintf("https://%s/?%s",
            $baseUri, $queryString));

        // 对请求进行签名
        $request = $this->imagexSignature->signRequest(
            $request,
            $this->credentials,
        );

        // 创建HTTP客户端对象，禁用SSL验证
        $client = new Client([
            'verify' => false,
        ]);

        // 尝试发送HTTP请求
        try {
            $response = $client->send($request);

            // 如果响应状态码为200，返回响应内容
            if ($response->getStatusCode() == 200) {
                $data = $response->getBody()->getContents();
                return json_decode($data, true);
            } else {
                // 如果响应状态码非200，返回状态码和错误信息
                return [
                    'code'    => $response->getStatusCode(),
                    'message' => $response->getBody()->getContents(),
                ];
            }
        } catch (Exception $exception) {
            // 捕获异常，返回异常代码和信息
            return [
                'code'    => $exception->getCode(),
                'message' => $exception->getMessage(),
            ];
        }
    }

    /**
     * 内部上传申请
     *
     * @return array 上传配置信息
     */
    public function ApplyUploadInner()
    {
        $ServiceId   = $this->tokenResult->space_name;
        $queryData   = [
            'Action'    => 'ApplyUploadInner',
            'Version'   => '2020-11-19',
            'SpaceName' => $ServiceId,
            'FileType'  => 'video',
            'IsInner'   => 1,
            'FileSize'  => (int) $this->fileSize,
            's'         => '07lwqnq6yo7n'
        ];
        $queryString = http_build_query($queryData);
        $baseUri     = $this->tokenResult->upload_domain;
        $request     = new Request('GET', sprintf("https://%s/?%s",
            $baseUri, $queryString));
        $request     = $this->vodSignature->signRequest(
            $request,
            $this->credentials,
        );
        $client      = new Client([
            'verify' => false,
        ]);
        try {
            $response = $client->send($request);
            if ($response->getStatusCode() == 200) {
                $data = $response->getBody()->getContents();
                return json_decode($data, true);
            } else {
                return [
                    'code'    => $response->getStatusCode(),
                    'message' => $response->getBody()->getContents(),
                ];
            }
        } catch (Exception $exception) {
            return [
                'code'    => $exception->getCode(),
                'message' => $exception->getMessage(),
            ];
        }
    }

    /**
     * 执行文件上传
     *
     * @return string 文件URI
     * @throws Exception
     */
    public function upload()
    {
        // 根据文件大小选择上传方式
        if ($this->fileSize > 5242880) {
            $this->storeMultipart();
        } else {
            $this->store();
        }
        $CommitData = match ($this->type) {
            'image' => $this->CommitImageUpload(),
            'video' => $this->CommitUploadInner(),
        };
        try {
            $commitResult = $CommitData['Result']['Results'][0];
            $uri          = match ($this->type) {
                'image' => $commitResult['Uri'],
                'video' => $commitResult['VideoMeta']['Uri'],
            };
            return [$uri, $commitResult, $CommitData];
        } catch (Exception $exception) {
            throw new Exception($exception->getMessage());
        }
    }

    /**
     * 分块上传
     */
    public function storeMultipart()
    {
        $client    = new Client([
            'verify'   => false,
            'base_uri' => sprintf("https://%s", $this->uploadHost)
        ]);
        $response  = $client->request(
            'POST',
            'upload/v1/'.$this->storeInfos['StoreUri'],
            [
                'headers' => [
                    'Content-Type'  => 'multipart/form-data',
                    'Authorization' => $this->storeInfos['Auth'],
                ],
                'query'   => [
                    'uploadmode' => 'part',
                    'phase'      => 'init',
                ],
            ]
        );
        $uploadid  = '';
        $part      = 1;
        $chunkSize = 5242880;
        if ($response->getStatusCode() != 200) {
            throw new Exception($response->getBody()->getContents());
        } else {
            $data     = json_decode($response->getBody()->getContents(), true);
            $uploadid = $data['data']['uploadid'];
        }
        $crcArray = [];

        while (! feof($this->fileHandle)) {
            $chunk = stream_get_contents($this->fileHandle, $chunkSize);
            if ($chunk !== false) {
                $query      = [
                    'uploadid'    => $uploadid,
                    'part_number' => $part,
                    'phase'       => 'transfer',
                ];
                $crc        = hash('crc32b', $chunk);
                $crcArray[] = $part.':'.$crc;
                $response   = $client->request(
                    'POST',
                    'upload/v1/'.$this->storeInfos['StoreUri'],
                    [
                        'headers' => [
                            'Content-Type'  => 'application/octet-stream',
                            'Content-CRC32' => $crc,
                            'Authorization' => $this->storeInfos['Auth'],
                        ],
                        'query'   => $query,
                        'body'    => $chunk,
                    ]
                );
                if ($response->getStatusCode() != 200) {
                    throw new Exception($response->getBody()->getContents());
                }
                $part++;
            }
        }
        $response = $client->request(
            'POST',
            'upload/v1/'.$this->storeInfos['StoreUri'],
            [
                'headers' => [
                    'Content-Type'  => 'text/plain;charset=UTF-8',
                    'Authorization' => $this->storeInfos['Auth'],
                ],
                'query'   => [
                    'uploadmode' => 'part',
                    'uploadid'   => $uploadid,
                    'phase'      => 'finish',
                ],
                'body'    => implode(',', $crcArray),
            ]
        );
        if ($response->getStatusCode() != 200) {
            throw new Exception($response->getBody()->getContents());
        } else {
            $data = json_decode($response->getBody()->getContents(), true);
            if ($data['code'] == 2000) {
            } else {
                throw new Exception($data['message']);
            }
        }
    }

    /**
     * 整体上传
     *
     * @return mixed 上传结果
     * @throws Exception
     */
    public function store()
    {
        $client   = new Client([
            'verify'   => false,
            'base_uri' => sprintf("https://%s",
                $this->uploadHost)
        ]);
        $crc      = hash_file('crc32b', $this->fileUrl);
        $response = $client->post('upload/v1/'.$this->storeInfos['StoreUri'], [
            'headers' => [
                'Content-Type'  => 'application/octet-stream',
                'Authorization' => $this->storeInfos['Auth'],
                'Content-CRC32' => $crc,
            ],
            'body'    => stream_get_contents($this->fileHandle, $this->fileSize),
        ]);
        if ($response->getStatusCode() == 200) {
            $data = json_decode($response->getBody()->getContents(), true);
            if ($data['code'] == 2000) {
                return $data['data'];
            } else {
                throw new Exception($data['message']);
            }
        } else {
            throw new Exception($response->getBody()->getContents());
        }
    }

    /**
     * 提交图片上传
     *
     * @return array 提交结果
     */
    public function CommitImageUpload()
    {
        $ServiceId   = $this->tokenResult->space_name;
        $queryData   = [
            'Action'    => 'CommitImageUpload',
            'Version'   => '2018-08-01',
            'ServiceId' => $ServiceId,
        ];
        $queryString = http_build_query($queryData);
        $baseUri     = $this->tokenResult->upload_domain;
        $params      = [
            'SessionKey' => $this->SessionKey,
        ];
        $request     = new Request('POST', sprintf("https://%s/?%s",
            $baseUri, $queryString), [
            'Content-Type'         => 'application/json',
            'x-amz-content-sha256' => hash('sha256', json_encode($params, JSON_UNESCAPED_UNICODE)),
        ], json_encode($params, JSON_UNESCAPED_UNICODE));
        $request     = $this->imagexSignature->signRequest(
            $request,
            $this->credentials,
        );
        $client      = new Client([
            'verify' => false,
        ]);
        try {
            $response = $client->send($request);
            if ($response->getStatusCode() == 200) {
                $data = $response->getBody()->getContents();
                return json_decode($data, true);
            } else {
                return [
                    'code'    => $response->getStatusCode(),
                    'message' => $response->getBody()->getContents(),
                ];
            }
        } catch (Exception $exception) {
            return [
                'code'    => $exception->getCode(),
                'message' => $exception->getMessage(),
            ];
        }
    }

    public function CommitUploadInner()
    {
        $ServiceId   = $this->tokenResult->space_name;
        $queryData   = [
            'Action'    => 'CommitUploadInner',
            'Version'   => '2020-11-19',
            'SpaceName' => $ServiceId,
        ];
        $queryString = http_build_query($queryData);
        $baseUri     = $this->tokenResult->upload_domain;
        $params      = [
            'SessionKey' => $this->SessionKey,
        ];
        $request     = new Request('POST', sprintf("https://%s/?%s",
            $baseUri, $queryString), [
            'Content-Type'         => 'application/json',
            'x-amz-content-sha256' => hash('sha256', json_encode($params, JSON_UNESCAPED_UNICODE)),
        ], json_encode($params, JSON_UNESCAPED_UNICODE));
        $request     = $this->vodSignature->signRequest(
            $request,
            $this->credentials,
        );
        $client      = new Client([
            'verify' => false,
        ]);
        try {
            $response = $client->send($request);
            if ($response->getStatusCode() == 200) {
                $data = $response->getBody()->getContents();
                return json_decode($data, true);
            } else {
                return [
                    'code'    => $response->getStatusCode(),
                    'message' => $response->getBody()->getContents(),
                ];
            }
        } catch (Exception $exception) {
            return [
                'code'    => $exception->getCode(),
                'message' => $exception->getMessage(),
            ];
        }
    }
}