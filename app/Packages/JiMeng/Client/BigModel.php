<?php

namespace App\Packages\JiMeng\Client;

use App\Models\PluginJmDraw;
use Exception;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;

class BigModel
{
    protected string $tokan = '106ec37796bd90c5790b1a1b0bb3522a.XjAlhxVhzi35sI2o';
    protected string $uri   = 'https://open.bigmodel.cn/api/paas/v4/';

    public function genImage(PluginJmDraw $task)
    {
        $params   = [
            'model'  => $task->type,
            'prompt' => $task->prompt,
            'size'   => $task->params['width'].'x'.$task->params['height'],
        ];
        $client   = new Client([
            'verify'   => false,
            'base_uri' => $this->uri,
        ]);
        $response = $client->post('images/generations', [
            'headers' => [
                'Content-Type'  => 'application/json',
                'Authorization' => 'Bearer '.$this->tokan,
            ],
            'json'    => $params,
        ]);
        if ($response->getStatusCode() == 200) {
            $data = json_decode($response->getBody()->getContents(), true);
            return $data;
        } else {
            throw new Exception($response->getBody()->getContents());
        }
    }

    public function genVideo(PluginJmDraw $task)
    {
        $params   = [
            'model'     => $task->type,
            'prompt'    => $task->prompt,
            'image_url' => $task->inputs[0]['url'] ?? '',
        ];
        $client   = new Client([
            'verify'   => false,
            'base_uri' => $this->uri,
        ]);
        $response = $client->post('videos/generations', [
            'headers' => [
                'Content-Type'  => 'application/json',
                'Authorization' => 'Bearer '.$this->tokan,
            ],
            'json'    => $params,
        ]);
        if ($response->getStatusCode() == 200) {
            $data = json_decode($response->getBody()->getContents(), true);
            return $data;
        } else {
            throw new Exception($response->getBody()->getContents());
        }
    }

    public function videoBigQuery(PluginJmDraw $task)
    {
        $url    = 'async-result/'.$task->task_id;
        $client = new Client([
            'verify'   => false,
            'base_uri' => $this->uri,
        ]);
        try {
            $response = $client->get($url, [
                'headers' => [
                    'Content-Type'  => 'application/json',
                    'Authorization' => 'Bearer '.$this->tokan,
                ],
            ]);
            if ($response->getStatusCode() == 200) {
                $data = json_decode($response->getBody()->getContents(), true);
                return $data;
            } else {
                throw new Exception($response->getBody()->getContents());
            }
        } catch (RequestException $exception) {
            $data = json_decode($exception->getResponse()->getBody()->getContents(), true);
            throw new Exception($data['error']['message']);
        }
    }
}