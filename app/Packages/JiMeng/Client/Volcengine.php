<?php

namespace App\Packages\JiMeng\Client;

use App\Models\PluginJmDraw;
use App\Packages\Volcengine\Volcengine as Engine;
use Exception;
use GuzzleHttp\Client;

class Volcengine
{
    protected string $authKey  = 'd7559878-baec-4a6f-8d76-8d7d04a5582e';
    protected string $videoUrl = 'https://ark.cn-beijing.volces.com/api/v3/contents/generations/tasks';

    public function textImage(PluginJmDraw $task)
    {
        $reqKey = match ($task->type) {
            PluginJmDraw::TYPE_TEXT_IMAGE => 'high_aes_general_v21_L',
            PluginJmDraw::TYPE_TEXT_ZT2I => 'high_aes_general_v30l_zt2i',
        };
        $params = [
            'req_key'     => $reqKey,
            'prompt'      => $task->prompt,
            'use_pre_llm' => $task->params['optimize'] ? true : false,
            'scale'       => (float) $task->params['sample_strength'],
            'width'       => (int) bcdiv($task->params['width'], 2, 0),
            'height'      => (int) bcdiv($task->params['height'], 2, 0),
            'return_url'  => true,
        ];

        if ($task->type == PluginJmDraw::TYPE_TEXT_IMAGE) {
            $params['use_sr']        = true;
            $params['model_version'] = 'general_v2.1_L';
            $result                  = Engine::vision()->HighAesGeneralV21L('2024-06-06', $params);
        }
        if ($task->type == PluginJmDraw::TYPE_TEXT_ZT2I) {
            $params['width']  = $task->params['width'];
            $params['height'] = $task->params['height'];
            $result           = Engine::vision()->HighAesGeneralV30LZT2I('2024-06-06', $params);
        }
        if ($result->isSuccess()) {
            $baseResp = $result->algorithm_base_resp;
            if ($baseResp['status_code'] === 0) {
                return $result->toArray();
            } else {
                throw new Exception($baseResp['status_message']);
            }
        } else {
            throw new Exception($result->getMessage());
        }
    }

    public function imageImage(PluginJmDraw $task)
    {
        $params = [
            'req_key'    => 'img2img_ai_doodle_dreamina',
            'prompt'     => $task->prompt,
            'image_urls' => [$task->inputs[0]['url']],
            'width'      => $task->params['width'],
            'height'     => $task->params['height'],
            'seed'       => mt_rand(100000000, 999999999),
            'strength'   => (float) $task->params['sample_strength'],
            'steps'      => 4,
            'return_url' => true,
        ];
        $result = Engine::vision()->Img2imgAIDoodleDreamina('2024-06-06', $params);
        if ($result->isSuccess()) {
            $baseResp = $result->algorithm_base_resp;
            if ($baseResp['status_code'] === 0) {
                return $result->toArray();
            } else {
                throw new Exception($baseResp['status_message']);
            }
        } else {
            throw new Exception($result->getMessage());
        }
    }

    public function videoQuery(PluginJmDraw $task)
    {
        $url    = $this->videoUrl.'/'.$task->task_id;
        $client = new Client([
            'verify' => false,
        ]);
        try {
            $response = $client->request('GET', $url, [
                'headers' => [
                    'Authorization' => 'Bearer '.$this->authKey,
                ],
            ]);
            if ($response->getStatusCode() === 200) {
                $data = json_decode($response->getBody()->getContents(), true);
                return $data;
            } else {
                throw new Exception($response->getBody()->getContents());
            }
        } catch (Exception $exception) {
            throw new Exception($exception->getMessage());
        }
    }

    public function drawVideo(PluginJmDraw $task)
    {
        $model  = match ($task->type) {
            PluginJmDraw::TYPE_TEXT_SEEDANCE => 'ep-20250508105020-dvcrm',
            PluginJmDraw::TYPE_IMAGE_SEEDANCE => 'ep-20250508105048-b8dh7',
            default => 'ep-20250227140939-qmx6k',
        };
        $params = [
            'model'   => $model,
            'content' => match ($task->type) {
                PluginJmDraw::TYPE_TEXT_VIDEO, PluginJmDraw::TYPE_TEXT_SEEDANCE => [
                    [
                        'type' => 'text',
                        'text' => $this->getVideoPrompt($task),
                    ]
                ],
                PluginJmDraw::TYPE_IMAGE_VIDEO, PluginJmDraw::TYPE_IMAGE_SEEDANCE => [
                    [
                        'type' => 'text',
                        'text' => $this->getVideoPrompt($task),
                    ],
                    [
                        'type'      => 'image_url',
                        'image_url' => [
                            'url' => getOssPrivateUrl($task->inputs[0]['url'])
                        ],
                    ]
                ],
            },
        ];
        try {
            $client   = new Client([
                'verify' => false,
            ]);
            $response = $client->request('POST', $this->videoUrl, [
                'headers' => [
                    'Authorization' => 'Bearer '.$this->authKey,
                ],
                'json'    => $params,
            ]);
            if ($response->getStatusCode() === 200) {
                $data = json_decode($response->getBody()->getContents(), true);
                return $data;
            } else {
                throw new Exception($response->getBody()->getContents());
            }
        } catch (Exception $exception) {
            throw new Exception($exception->getMessage());
        }
    }

    protected function getVideoPrompt(PluginJmDraw $task): string
    {
        return match ($task->type) {
            PluginJmDraw::TYPE_TEXT_VIDEO, PluginJmDraw::TYPE_TEXT_SEEDANCE => sprintf('%s --ratio %s --fps 24  --duration %s',
                $task->prompt,
                $task->params['aspect_ratio'],
                $task->params['duration'],
            ),
            PluginJmDraw::TYPE_IMAGE_VIDEO, PluginJmDraw::TYPE_IMAGE_SEEDANCE => sprintf('%s --fps 24  --duration %s',
                $task->prompt,
                $task->params['duration'],
            ),
        };
    }
}