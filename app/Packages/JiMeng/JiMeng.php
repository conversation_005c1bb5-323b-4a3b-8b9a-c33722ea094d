<?php

namespace App\Packages\JiMeng;

use App\Packages\JiMeng\Client\BigModel;
use App\Packages\JiMeng\Client\Task;
use App\Packages\JiMeng\Client\Tools;
use App\Packages\JiMeng\Client\Volcengine;

class JiMeng
{
    /**
     * 创建并返回一个新的Task对象
     *
     * 该方法用于实例化一个Task对象并将其返回它主要用于需要动态创建Task实例的场景
     *
     * @return Task 返回一个新的Task对象
     */
    public static function task(): Task
    {
        // 创建一个新的Task对象并返回
        return new Task();
    }

    public static function volcengine(): Volcengine
    {
        return new Volcengine();
    }

    public static function bigmodel(): BigModel
    {
        return new BigModel();
    }

    /**
     * 创建并返回一个新的Tools对象实例
     *
     * 此方法允许在需要的地方轻松获取Tools对象的实例
     * 它没有参数，因此在调用时不需要提供任何额外的信息
     *
     * @return Tools 新创建的Tools对象实例
     */
    public static function tools(): Tools
    {
        // 创建一个新的Task对象并返回
        return new Tools();
    }
}