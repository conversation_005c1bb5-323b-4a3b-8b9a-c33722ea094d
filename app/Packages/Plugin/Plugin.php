<?php

namespace App\Packages\Plugin;

use App\Packages\Plugin\Client\ChatCompletions;
use App\Packages\Plugin\Client\Keling;
use App\Packages\Plugin\Helper\KeLingAi;

class Plugin extends BaseClient
{
    public static function ChatCompletions(): ChatCompletions
    {
        return new ChatCompletions();
    }

    public static function Keling(): Keling
    {
        return new Keling();
    }

    public static function klHelper(): KeLingAi
    {
        return new KeLingAi();
    }
}
