<?php

namespace App\Packages\Plugin\Client;

use App\Exceptions\ValidatorException;
use App\Models\PluginKeLing;
use App\Models\SystemConfig;
use App\Packages\Plugin\BaseClient;
use App\Packages\Plugin\Response\KelingResponse;
use Exception;

class Keling extends BaseClient
{
    public function __construct()
    {
        $this->setResponseClass(KelingResponse::class);
        $this->setBaseUri('https://klingai.kuaishou.com/api/');
        $this->setHeaders([
            "Accept"             => "application/json, text/plain, */*",
            "Accept-Encoding"    => "gzip, deflate, br, zstd",
            "Accept-Language"    => "zh",
            "Connection"         => "keep-alive",
            "Content-Type"       => "application/json;charset=UTF-8",
            "Host"               => "klingai.kuaishou.com",
            "Origin"             => "https://klingai.kuaishou.com",
            "Sec-Fetch-Dest"     => "empty",
            "Sec-Fetch-Mode"     => "cors",
            "Sec-Fetch-Site"     => "same-origin",
            "User-Agent"         => "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36",
            "sec-ch-ua-mobile"   => "?0",
            "sec-ch-ua-platform" => "\"Windows\"",
        ]);
    }

    public function imageToVideo(PluginKeLing $task)
    {
        $this->setCookie();
        return $this->setParams($task->params)
            ->post('task/submit');
    }

    protected function setCookie(): self
    {
        $cookie = SystemConfig::getValue('OneKeyVideoCookie');
        return $this->setHeaders([
            "Cookie" => $cookie,
        ]);
    }

    public function taskQuery(PluginKeLing $task)
    {
        $this->setCookie();
        return $this->setParams([
            'taskId' => $task->task_id
        ])
            ->get('task/status');
    }

    public function dsx_tts($query)
    {
        $url = "lip/sync/ttsList";
        try {
            return $this->setParams($query)
                ->get($url);
        } catch (Exception $exception) {
            throw new ValidatorException($exception->getMessage());
        }
    }

    public function specialEffects()
    {
        $url = "config/composited_special_effects";
        try {
            return $this->get($url);
        } catch (Exception $exception) {
            throw new ValidatorException($exception->getMessage());
        }
    }

    public function tts($query)
    {
        $url = "lip/sync/tts";
        try {
            return $this->setParams($query)
                ->post($url);
        } catch (Exception $exception) {
            throw new ValidatorException($exception->getMessage());
        }
    }

    public function libraries(int $count, string $ver = '1.6')
    {
        $this->setCookie();
        return $this->setParams([
            'count' => $count,
        ])->get(sprintf('libraries/text-to-video/%s/recommended', $ver));
    }

    public function recommendAudio($query)
    {
        $url = "lip/sync/recommendAudio";
        try {
            return $this->setParams($query)
                ->get($url);
        } catch (Exception $exception) {
            throw new ValidatorException($exception->getMessage());
        }
    }

    public function firstRecommend(int $count)
    {
        $url = "libraries/image-to-video/1.6/recommended";
        try {
            return $this->setParams([
                'count' => $count,
            ])
                ->get($url);
        } catch (Exception $exception) {
            throw new ValidatorException($exception->getMessage());
        }
    }

    public function multRecommend()
    {
        $url = "config/multi_id_r2v_1.6";
        try {
            return $this->get($url);
        } catch (Exception $exception) {
            throw new ValidatorException($exception->getMessage());
        }
    }

    public function recommend_videos()
    {
        $url = "config/lip_sync_recommend_videos";
        try {
            return $this->get($url);
        } catch (Exception $exception) {
            throw new ValidatorException($exception->getMessage());
        }
    }

    public function delete(PluginKeLing $task)
    {
        $this->setCookie();
        $url = 'task/del';
        try {
            return $this->setParams([
                'taskIds' => [$task->task_id]
            ])
                ->post($url);
        } catch (Exception $exception) {
            throw new ValidatorException($exception->getMessage());
        }
    }

    public function imageOptions()
    {
        try {
            return $this->get('tryon/models/options');
        } catch (Exception $exception) {
            throw new ValidatorException($exception->getMessage());
        }
    }

    public function recoModel()
    {
        try {
            return $this->get('tryon/recoModel');
        } catch (Exception $exception) {
            throw new ValidatorException($exception->getMessage());
        }
    }

    public function recoClothes()
    {
        try {
            return $this->get('tryon/recoClothes');
        } catch (Exception $exception) {
            throw new ValidatorException($exception->getMessage());
        }
    }

    public function uploadModel(string $url)
    {
        $this->setCookie();
        try {
            return $this->setParams([
                'imageUrl' => $url,
            ])
                ->post('tryon/uploadModel');
        } catch (Exception $exception) {
            throw new ValidatorException($exception->getMessage());
        }
    }

    public function uploadClothes(string $url)
    {
        $this->setCookie();
        try {
            return $this->setParams([
                'imageUrl' => $url,
            ])
                ->post('tryon/uploadClothes');
        } catch (Exception $exception) {
            throw new ValidatorException($exception->getMessage());
        }
    }

}
