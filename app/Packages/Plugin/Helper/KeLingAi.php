<?php

namespace App\Packages\Plugin\Helper;

use App\Models\SystemConfig;
use GuzzleHttp\Client;
use Illuminate\Support\Str;

class KeLingAi
{
    protected string $cookie = '';
    protected string $klApi  = 'https://klingai.kuaishou.com/api';
    protected string $upApi  = 'https://upload.kuaishouzt.com/api';
    protected Client $client;

    public function __construct()
    {
        $this->cookie = SystemConfig::getValue('OneKeyVideoCookie');
        $this->client = new Client([
            'verify' => false,
        ]);
    }

    public function updateFile(string $fileUrl, string $type = 'image')
    {
        $fileUrl    = getOssPrivateUrl($fileUrl);
        $fileHandle = @fopen($fileUrl, 'r');
        if (! $fileHandle) {
            return [
                'status'  => 400,
                'message' => '远程文件打开失败',
            ];
        }
        $token         = $this->getToken($fileUrl);
        $fragmentCount = $this->chunkSpent($fileHandle, $token);
        if ($fragmentCount <= 0) {
            return [
                'status'  => 400,
                'message' => '文件分割失败',
            ];
        }
        $this->isComplate($fragmentCount, $token);
        list($res, $fileName) = match ($type) {
            'image' => $this->image($token),
            'video' => $this->video($token),
            'audio' => $this->audio($token),
            default => [false, '类型不存在'],
        };
        if ($res) {
            return [
                'status'  => 200,
                'message' => '',
                'data'    => [
                    'url' => $fileName
                ],
            ];
        } else {
            return [
                'status'  => 400,
                'message' => $fileName,
            ];
        }
    }

    protected function getToken(string $fileUrl)
    {
        $fileName    = Str::afterLast(Str::beforeLast($fileUrl, '?'), '/');
        $getToken    = $this->client->request('GET',
            $this->klApi.'/upload/issue/token',
            [
                'headers' => [
                    'Cookie' => $this->cookie,
                ],
                'query'   => [
                    'filename' => $fileName,
                ]
            ]);
        $tokenString = $getToken->getBody()->getContents();
        $token       = json_decode($tokenString, true);
        $token       = $token['data']['token'];
        return $token;
    }

    protected function chunkSpent($fileHandle, $token)
    {
        $fragmentCount = 0;
        $chunkSize     = 1024 * 1024;
        while (! feof($fileHandle)) {
            $chunk = stream_get_contents($fileHandle, $chunkSize);
            if ($chunk !== false) {
                $this->client->request('POST',
                    $this->upApi.'/upload/fragment', [
                        'body'  => $chunk,
                        'query' => [
                            'upload_token' => $token,
                            'fragment_id'  => $fragmentCount,
                        ],
                    ]);
                $fragmentCount++;
            }
        }
        return $fragmentCount;
    }

    public function isComplate(int $fragmentCount, string $token)
    {
        return $this->client->request('POST',
            $this->upApi.'/upload/complete', [
                'query' => [
                    'upload_token'   => $token,
                    'fragment_count' => $fragmentCount,
                ]
            ]);
    }

    protected function image(string $token)
    {
        $res  = $this->client->request('GET',
            $this->klApi.'/upload/verify/token',
            [
                'headers' => [
                    'Cookie' => $this->cookie,
                ],
                'query'   => [
                    'token' => $token,
                ]
            ]);
        $data = json_decode($res->getBody()->getContents(), true);
        if ($data['status'] != 200) {
            return [false, $data['message']];
        }
        return [true, $data['data']['url']];
    }

    protected function video(string $token)
    {
        $res  = $this->client->request('POST',
            $this->klApi.'/lip/sync/identify/video',
            [
                'headers' => [
                    'Cookie' => $this->cookie,
                ],
                'json'    => [
                    'token' => $token,
                ]
            ]
        );
        $data = json_decode($res->getBody()->getContents(), true);
        if ($data['status'] != 200) {
            return [false, $data['message']];
        }
        return [true, $data['data']['resourceUrl']];
    }

    protected function audio(string $token)
    {
        $res  = $this->client->request('GET',
            $this->klApi.'/upload/verify/audio',
            [
                'headers' => [
                    'Cookie' => $this->cookie,
                ],
                'query'   => [
                    'token' => $token,
                ]
            ]
        );
        $data = json_decode($res->getBody()->getContents(), true);
        if ($data['status'] != 200) {
            return [false, $data['message']];
        }
        return [true, $data['data']['resourceUrl']];
    }

}