<?php

namespace App\Packages\Plugin;

use App\Models\SystemConfig;
use Exception;
use GuzzleHttp\Client;

class BaseClient
{
    protected string $baseUri;
    protected array  $headers = [
        "Content-Type" => "application/json;charset=UTF-8",
    ];
    protected array  $params  = [];
    protected bool   $hasFile = false;//是否上传文件
    protected        $file;
    protected bool   $verify  = false;
    protected string $responseClass;//解析response

    protected function setResponseClass(string $responseClass): void
    {
        $this->responseClass = $responseClass;
    }

    protected function setBaseUri(string $baseUri): self
    {
        $this->baseUri = $baseUri;
        return $this;
    }

    protected function setHeaders(array $header): self
    {
        $this->headers = array_merge($this->headers, $header);
        return $this;
    }

    /**
     * @return $this
     */
    protected function setToken(): self
    {
        $this->headers['Token'] = SystemConfig::getValue('OneKeyVideoToken', 'cbbea6c1a49645d998ae8c4a2978dfe3');
        return $this;
    }

    protected function setParams(array $params)
    {
        $this->params = $params;
        return $this;
    }

    protected function setFile($file)
    {
        $this->file    = $file;
        $this->hasFile = true;
        return $this;
    }

    protected function get(string $path): PluginResponse
    {
        return $this->request($path, 'GET', 'query');
    }

    /**
     * 数据包发送
     *
     * @param  string  $url
     * @param  string  $method
     * @param  string  $paramsName
     * @return \App\Packages\Plugin\PluginResponse
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    private function request(
        string $url,
        string $method,
        string $paramsName,
    ): PluginResponse {
        try {
            $client   = new Client([
                'verify' => $this->verify,
            ]);
            $response = $client->request($method, $this->baseUri.$url, [
                'headers'   => $this->headers,
                $paramsName => $this->params,
            ]);
            return new $this->responseClass($response, $this->params);
        } catch (Exception $exception) {
            $statusCode = $exception->getResponse()?->getStatusCode();
            if ($statusCode == 401) {
                throw new Exception("接口服务器访问异常【{$statusCode}】");
            }
            throw new Exception($exception->getMessage());
        }
    }

    protected function post(string $path): PluginResponse
    {
        return $this->request($path, 'POST', 'json');
    }
}
