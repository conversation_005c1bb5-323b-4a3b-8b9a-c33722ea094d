<?php

namespace App\Enums\Bailian;

use App\Traits\EnumMethods;

enum BailianArticleTypeEnum: string
{
    use EnumMethods;

    case ARTICLE = 'article';
    case RECORD  = 'record';
    case MEETING = 'meeting';
    case LINK    = 'link';
    case IMAGE   = 'image';

    const STATUS_MAP = [
        self::ARTICLE->value => '文字',
        self::RECORD->value  => '录音',
        self::MEETING->value => '会议录音',
        self::LINK->value    => '链接',
        self::IMAGE->value   => '图片',

    ];

    const LABEL_MAP = [
        self::ARTICLE->value => 'default',
        self::RECORD->value  => 'primary',
        self::MEETING->value => 'info',
        self::LINK->value    => 'success',
        self::IMAGE->value   => 'warning',
    ];

    public function toString(): string
    {
        return self::STATUS_MAP[$this->value];
    }
}