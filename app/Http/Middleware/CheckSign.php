<?php

namespace App\Http\Middleware;

use App\Packages\KernelCommand;
use Closure;
use Illuminate\Http\Request;
use Symfony\Component\HttpFoundation\Response;

class CheckSign
{
    public function handle(Request $request, Closure $next): Response
    {
        $kernel = new KernelCommand($request);
        if ($request->get('debug', '') == 'yes') {
            $kernel->debug();
        }
        $request->merge([
            'kernel' => $kernel
        ]);
        return $next($request);
    }
}