<?php

use App\Http\Controllers\Api\Center\IndexController;
use App\Http\Controllers\Api\Center\OptionController;
use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Route;

Route::group([
    'namespace'  => 'App\\Http\\Controllers',
    'middleware' => ['api'],
    'prefix'     => 'center'
], function (Router $router) {
    $router->post('user/index', [IndexController::class, 'index'])
        ->name('center.User.Index');
    $router->post('user/assets', [IndexController::class, 'assets'])
        ->name('center.User.Assets');
});

Route::group([
    'namespace'  => 'App\\Http\\Controllers',
    'middleware' => ['api', 'wateAuth'],
    'prefix'     => 'center'
], function (Router $router) {
    $router->post('user/setNicknameAs', [OptionController::class, 'setNicknameAs'])
        ->name('center.Option.setNicknameAs');
    $router->post('user/setDontShow', [OptionController::class, 'setDontShow'])
        ->name('center.Option.setDontShow');
    $router->post('user/setBlack', [OptionController::class, 'setBlack'])
        ->name('center.Option.setBlack');
});