<?php

use App\Http\Controllers\Api\Support\ArticleController;
use App\Http\Controllers\Api\Support\CategoryController;
use App\Http\Controllers\Api\Support\FeedbackController;
use App\Http\Controllers\Api\Support\IndexController;
use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Route;

Route::group([
    'namespace'  => 'App\\Http\\Controllers',
    'middleware' => ['api'],
    'prefix'     => 'support'
], function (Router $router) {
    $router->post('index', [IndexController::class, 'index'])->name('support.index');
    $router->post('feedback', [FeedbackController::class, 'index'])->name('support.feedback.index');
    $router->post('store', [FeedbackController::class, 'store'])->name('support.feedback.add');
    $router->post('category', [CategoryController::class, 'index'])->name('support.category.index');
    $router->post('article', [ArticleController::class, 'index'])->name('support.article.index');
});
