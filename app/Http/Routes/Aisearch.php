<?php

use App\Http\Controllers\Api\AiSearch\ImageController;
use App\Http\Controllers\Api\AiSearch\IndexController;
use App\Http\Controllers\Api\AiSearch\IndexV2Controller;
use App\Http\Controllers\Api\AiSearch\NewSearchController;
use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Route;

Route::group([
    'namespace'  => 'App\\Http\\Controllers',
    'middleware' => ['api'],
], function (Router $router) {
    $router->post('aisearch/gethot', [IndexController::class, 'getHot'])->name('aisearch.gethot');
    $router->post('aisearch/getAppHot', [IndexController::class, 'appSearch'])->name('aisearch.getAppHot');
    $router->post('aisearch/index', [IndexController::class, 'index'])->name('Aisearch.index');
    $router->post('aisearch/AiSearchImage', [ImageController::class, 'AiSearchImage'])->name('tool.aisearch_image');
    $router->post('aisearch/new/index', [NewSearchController::class, 'index'])->name('Aisearch.New.index');

    $router->post('aisearch/indexV2', [IndexV2Controller::class, 'appSearch'])->name('Aisearch.v2index');
    $router->post('aisearch/getAppHotV2', [IndexV2Controller::class, 'getHost'])->name('aisearch.V2getAppHot');
});
Route::group([
    'namespace'  => 'App\\Http\\Controllers',
    'middleware' => ['api', 'wateAuth'],
], function (Router $router) {
    $router->post('aisearch/getLog', [IndexController::class, 'getLog'])->name('aisearch.getLog');
});



