<?php

use App\Http\Controllers\Api\Card\CardController;
use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Route;

Route::group([
    'namespace'  => 'App\\Http\\Controllers',
    'middleware' => ['api', 'wateAuth'],
    'prefix'     => 'card'
], function (Router $router) {
    $router->post('index', [CardController::class, 'index'])->name('cardcase.myCardcase');
    $router->post('change', [CardController::class, 'change'])->name('cardcase.changeBusiness');
    $router->post('rank', [CardController::class, 'rank'])->name('cardcase.rank');
});
