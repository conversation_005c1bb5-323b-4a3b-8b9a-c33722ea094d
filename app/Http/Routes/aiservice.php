<?php

use App\Http\Controllers\Api\AiChat\ServiceController;
use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Route;

Route::group([
    'namespace'  => 'App\\Http\\Controllers',
    'middleware' => ['api'],
    'prefix'     => 'ai'
], function (Router $router) {
    $router->post('volcengineAudioToken', [ServiceController::class, 'volcengineAudioToken'])
        ->name('volcengine.Audio.Token');
    $router->post('volcengineStandalone', [ServiceController::class, 'volcengineStandalone'])
        ->name('volcengine.Standalone');
});
