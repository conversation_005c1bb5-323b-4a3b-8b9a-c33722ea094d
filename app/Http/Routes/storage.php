<?php

use App\Http\Controllers\Api\Storage\UploadController;
use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Route;

Route::group([
    'namespace'  => 'App\\Http\\Controllers',
    'middleware' => ['api'],
], function (Router $router) {
    $router->post('common/uploadImage', [UploadController::class, 'upload'])->name('storage.upload');
    $router->post('common/uploadFile', [UploadController::class, 'uploadFile'])->name('storage.uploadFile');
    $router->post('common/uploadFromWechat', [UploadController::class, 'uploadFromWechat'])
        ->name('storage.uploadFromWechat');
});
