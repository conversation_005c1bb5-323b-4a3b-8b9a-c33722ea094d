<?php

use App\Http\Controllers\App\InteractionController;
use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Route;

Route::group([
    'namespace'  => 'App\\Http\\Controllers',
    'middleware' => ['api', 'wateAuth'],
], function (Router $router) {
    $router->post('app/interaction/follow', [InteractionController::class, 'follow'])
        ->name('app.Interaction.follow');
    $router->post('app/interaction/followList', [InteractionController::class, 'followList'])
        ->name('app.Interaction.followList');
    $router->post('app/interaction/fans', [InteractionController::class, 'fans'])
        ->name('app.Interaction.fans');
    $router->post('app/interaction/like', [InteractionController::class, 'like'])
        ->name('app.Interaction.like');
    $router->post('app/interaction/favorite', [InteractionController::class, 'favorite'])
        ->name('app.Interaction.favorite');
    $router->post('app/interaction/comment', [InteractionController::class, 'comment'])
        ->name('app.Interaction.comment');
    $router->post('app/interaction/commentDelete', [InteractionController::class, 'commentDelete'])
        ->name('app.Interaction.commentDelete');
    $router->post('app/interaction/commentLike', [InteractionController::class, 'commentLike'])
        ->name('app.Interaction.commentLike');
    $router->post('app/interaction/commentStep', [InteractionController::class, 'commentStep'])
        ->name('app.Interaction.commentStep');
    $router->post('app/interaction/subscribe', [InteractionController::class, 'subscribe'])
        ->name('app.Interaction.subscribe');
});

Route::group([
    'namespace'  => 'App\\Http\\Controllers',
    'middleware' => ['api'],
], function (Router $router) {
    $router->post('app/interaction/commentList', [InteractionController::class, 'commentList'])
        ->name('app.Interaction.commentList');
    $router->post('app/interaction/likedUsersTop', [InteractionController::class, 'likedUsersTop'])
        ->name('app.Interaction.likedUsersTop');
});