<?php

use App\Http\Controllers\Api\Common\SystemConfigController;
use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Route;

Route::group([
    'namespace'  => 'App\\Http\\Controllers',
    'middleware' => ['api', 'wateAuth'],
], function (Router $router) {
    $router->post('config/companyUserExcel', [SystemConfigController::class, 'companyUserExcel'])
        ->name('systemConfig.companyUserExcel');
    $router->post('config/pushSettings', [SystemConfigController::class, 'pushSettings'])
        ->name('systemConfig.push.settings');
});