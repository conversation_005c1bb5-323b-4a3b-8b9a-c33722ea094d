<?php

use App\Http\Controllers\Api\Notification\IndexController;
use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Route;

Route::group([
    'namespace'  => 'App\\Http\\Controllers',
    'middleware' => ['api'],
    'prefix'     => 'notification'
], function (Router $router) {
    $router->post('index', [IndexController::class, 'index'])->name('notification.index');
    $router->post('read', [IndexController::class, 'markAsRead'])
        ->name('notification.read');
    $router->post('read', [IndexController::class, 'markAsRead'])
        ->name('notification.read');
    $router->post('read_all', [IndexController::class, 'maskAllAsRead'])->name('notification.read_all');
    $router->post('read_by_ids', [IndexController::class, 'maskAsReadById'])->name('notification.read_by_ids');

    $router->post('count', [IndexController::class, 'count'])->name('notification.count');
});
