<?php

use App\Http\Controllers\Api\AiChat\AppGroupController;
use App\Http\Controllers\Api\AiChat\AssistantController;
use App\Http\Controllers\Api\AiChat\AudioController;
use App\Http\Controllers\Api\AiChat\AudioSunoController;
use App\Http\Controllers\Api\AiChat\GroupController;
use App\Http\Controllers\Api\AiChat\ImageController;
use App\Http\Controllers\Api\AiChat\NewIndexController;
use App\Http\Controllers\Api\AiChat\OfflineTaskController;
use App\Http\Controllers\Api\AiChat\SunoToolController;
use App\Http\Controllers\Api\AiChat\ToolsController;
use App\Http\Controllers\Api\AiChat\VideoController;
use App\Http\Controllers\Api\AiChat\VisionController;
use App\Http\Controllers\Api\AiChat\VolcengineRtcController;
use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Route;

Route::group([
    'namespace'  => 'App\\Http\\Controllers',
    'middleware' => ['api'],
], function (Router $router) {
    $router->post('image/draw/getSize', [ImageController::class, 'getSize'])
        ->name('draw.getSize');
    $router->post('image/draw/getStyle', [ImageController::class, 'getStyle'])
        ->name('draw.getStyle');
    $router->post('image/allimage', [ImageController::class, 'getAllImage'])
        ->name('img.getAllImage');
    $router->post('image/videoInit', [VideoController::class, 'init'])
        ->name('draw.videoInit');
    $router->post('audio/aiSinger', [SunoToolController::class, 'aiSinger'])
        ->name('audio.aiSinger');
    $router->post('audio/createdSinger',
        [SunoToolController::class, 'createdSinger'])
        ->name('audio.createdSinger');

    $router->post('audio/audioClass', [SunoToolController::class, 'audioClass'])
        ->name('audio.audioClass');
    $router->post('audio/audioLists', [SunoToolController::class, 'audioLists'])
        ->name('audio.audioLists');
    $router->post('aichat/share/getChat', [NewIndexController::class, 'getShareChat'])
        ->name('chat.Share.getChat');
});
Route::group([
    'namespace'  => 'App\\Http\\Controllers',
    'middleware' => ['api', 'wateAuth'],
], function (Router $router) {
    $router->post('Assistant/Index', [AssistantController::class, 'bailian'])
        ->name('Assistant.Chat');
    $router->post('Assistant/Index/Logs', [AssistantController::class, 'bailianLogs'])
        ->name('Assistant.Chat.Logs');
    $router->post('Assistant/Index/lastAsset', [AssistantController::class, 'lastAsset'])
        ->name('Assistant.Chat.lastAsset');

    $router->post('Assistant/getNodes', [AssistantController::class, 'getNodes'])
        ->name('Assistant.getNodes');//获取记忆片段
    $router->post('Assistant/createNode', [AssistantController::class, 'createNode'])
        ->name('Assistant.createNode');//修改记忆片段内容
    $router->post('Assistant/updateNode', [AssistantController::class, 'updateNode'])
        ->name('Assistant.updateNode');//修改记忆片段内容
    $router->post('Assistant/deleteNode', [AssistantController::class, 'deleteNode'])
        ->name('Assistant.deleteNode');//删除记忆片段
    $router->post('Assistant/cleanNode', [AssistantController::class, 'cleanNode'])
        ->name('Assistant.cleanNode');//清空记忆片段

    $router->post('Assistant/refreshNodes', [AssistantController::class, 'refreshNodes'])
        ->name('Assistant.refreshNodes');//更新同步记忆片段

    $router->post('Assistant/Disposal', [AssistantController::class, 'disposal'])
        ->name('Assistant.Disposal');//清除上下文

    $router->post('Assistant/setSetting', [AssistantController::class, 'setSetting'])
        ->name('Assistant.setSetting');
    $router->post('Assistant/getSetting', [AssistantController::class, 'getSetting'])
        ->name('Assistant.getSetting');

    $router->post('aichat/sendnew', [NewIndexController::class, 'chatV2'])
        ->name('chat.sendtext');
    $router->post('aichat/log/delete', [NewIndexController::class, 'delete'])
        ->name('chat.Group.Log.Delete');

    $router->post('aichat/share/getNo', [NewIndexController::class, 'getShareNo'])
        ->name('chat.Share.getNo');

    $router->post('aichat/getHistoryMsg',
        [NewIndexController::class, 'getHistoryMsg'])->name('chat.getHistoryMsg');
    $router->post('aichat/pcHistoryMsg',
        [NewIndexController::class, 'pcHistoryMsg'])->name('chat.pcHistoryMsg');

    $router->post('aichat/chatGpt', [NewIndexController::class, 'chatGpt'])
        ->name('chat.chatGpt');

    //群组
    $router->post('group/list', [GroupController::class, 'index'])
        ->name('group.getGroupList');

    $router->post('group/create', [GroupController::class, 'create'])
        ->name('Group.saveGroup');
    $router->post('group/info', [GroupController::class, 'info'])
        ->name('group.getGroup');
    $router->post('group/delete', [GroupController::class, 'delete'])
        ->name('group.delGroup');
    $router->post('group/clear', [GroupController::class, 'clear'])
        ->name('group.clearGroup');

    $router->post('app-group/create', [AppGroupController::class, 'create'])
        ->name('Aisession.saveGroup');
    $router->post('app-group/delete', [AppGroupController::class, 'delete'])
        ->name('Aisession.delGroup');
    $router->post('app-group/list', [AppGroupController::class, 'index'])
        ->name('Aisession.getGroupList');
});

Route::group([
    'namespace'  => 'App\\Http\\Controllers',
    'middleware' => ['api', 'wateAuth'],
], function (Router $router) {
    $router->post('image/drawGen', [ImageController::class, 'draw'])
        ->name('draw.gen');
    $router->post('image/history', [ImageController::class, 'history'])
        ->name('img.history');
    $router->post('image/result', [ImageController::class, 'result'])
        ->name('img.result');
    $router->post('image/delete', [ImageController::class, 'delete'])
        ->name('img.delete');
    $router->post('video/drawGen', [VideoController::class, 'draw'])
        ->name('cogVideo.index');
    $router->post('video/history', [VideoController::class, 'history'])
        ->name('CogVideo.getVideoList');
    $router->post('video/result', [VideoController::class, 'result'])
        ->name('CogVideo.result');
    $router->post('video/delete', [VideoController::class, 'delete'])
        ->name('CogVideo.delete');
});

Route::group([
    'namespace'  => 'App\\Http\\Controllers',
    'middleware' => ['api', 'wateAuth'],
], function (Router $router) {
    //生成音频Suno
    $router->post('audio/gen', [AudioController::class, 'gen'])
        ->name('audio.gen');
    $router->post('audio/genCustom', [AudioController::class, 'genCustom'])
        ->name('audio.genCustom');
    $router->post('audio/captcha', [AudioController::class, 'captcha'])
        ->name('audio.captcha');
    $router->post('audio/lists', [AudioController::class, 'lists'])
        ->name('audio.lists');
    $router->post('audio/getTags', [AudioController::class, 'getTags'])
        ->name('audio.getTags');
    $router->post('audio/isIng', [AudioController::class, 'isIng'])
        ->name('audio.isIng');
    $router->post('audio/result', [AudioController::class, 'result'])
        ->name('audio.result');

    $router->post('audio/Suno/detail', [AudioSunoController::class, 'detail'])
        ->name('audio.Suno.Detail');
    $router->post('audio/Suno/Platform', [AudioSunoController::class, 'Platform'])
        ->name('audio.Suno.Platform');
    $router->post('audio/Suno/PlatformTo', [AudioSunoController::class, 'PlatformTo'])
        ->name('audio.Suno.PlatformTo');
    $router->post('audio/Suno/PlatformDown', [AudioSunoController::class, 'PlatformDown'])
        ->name('audio.Suno.PlatformDown');
    $router->post('audio/Suno/MyDelete', [AudioSunoController::class, 'MyDelete'])
        ->name('audio.Suno.MyDelete');
    $router->post('audio/Suno/MyUpdateName', [AudioSunoController::class, 'MyUpdateName'])
        ->name('audio.Suno.MyUpdateName');

    $router->post('audio/result', [AudioController::class, 'result'])
        ->name('audio.result');
    $router->post('audio/delete', [AudioController::class, 'delete'])
        ->name('audio.delete');
    $router->post('audio/createLyrics',
        [AudioController::class, 'createLyrics'])
        ->name('audio.createLyrics');

    $router->post('vision/index',
        [VisionController::class, 'index'])
        ->name('vision.index');

    $router->post('vision/ocr',
        [VisionController::class, 'ocr'])
        ->name('vision.ocr');
    //实时语音视频通话

    $router->post('audio/realtimeToken',
        [AudioController::class, 'realtimeToken'])
        ->name('audio.realtimeToken');
    $router->post('audio/videoResource',
        [AudioController::class, 'videoResource'])
        ->name('audio.videoResource');

    $router->post('audio/getRole', [AudioController::class, 'getRole'])
        ->name('audio.getRole');
    $router->post('audio/getDescription',
        [AudioController::class, 'getDescription'])
        ->name('audio.getDescription');
    //    $router->post('audio/createRoom', [AudioController::class, 'createRoom'])->name('audio.createRoom');
    $router->post('audio/createRoom',
        [AudioController::class, 'createRoom'])
        ->name('audio.createRoom');

    $router->post('audio/aliAiRoom',
        [AudioController::class, 'generateAIAgentCall'])
        ->name('audio.aliAiRoom');

    $router->post('audio/closeRoom',
        [AudioController::class, 'closeRoom'])
        ->name('audio.closeRoom');

    $router->post('voice/Volcengine/getConfig',
        [VolcengineRtcController::class, 'getConfig'])
        ->name('voice.Volcengine.getConfig');//设定角色

    $router->post('voice/Volcengine/StartVoiceChat',
        [VolcengineRtcController::class, 'StartVoiceChat'])
        ->name('voice.Volcengine.StartVoiceChat');//创建语音聊天室

    $router->post('voice/Volcengine/setVoiceRole',
        [VolcengineRtcController::class, 'setVoiceRole'])
        ->name('voice.Volcengine.setVoiceRole');//设定角色

    $router->post('voice/Volcengine/setCamera',
        [VolcengineRtcController::class, 'setCamera'])
        ->name('voice.Volcengine.camera');//摄像头开关

    $router->post('voice/Volcengine/setVoiceTts',
        [VolcengineRtcController::class, 'setVoiceTts'])
        ->name('voice.Volcengine.setVoiceTts');//设定角色

    $router->post('voice/Volcengine/setImageModel',
        [VolcengineRtcController::class, 'setImageModel'])
        ->name('voice.Volcengine.setImageModel');//设定角色

    $router->post('voice/Volcengine/StopVoiceChat',
        [VolcengineRtcController::class, 'StopVoiceChat'])
        ->name('voice.Volcengine.setRole');//停止

    $router->post('voice/huoshan/getVoiceList', [VolcengineRtcController::class, 'getVoiceList'])
        ->name('voice.huoshan.getVoiceList');

    $router->post('voice/huoshan/getRoleList', [VolcengineRtcController::class, 'getRoleList'])
        ->name('voice.huoshan.getRoleList');

    $router->post('voice/huoshan/getRoleListSocket', [VolcengineRtcController::class, 'getRoleListSocket'])
        ->name('voice.huoshan.getRoleListSocket');
    $router->post('voice/huoshan/getCompanionConfig', [VolcengineRtcController::class, 'getCompanionConfig'])
        ->name('voice.huoshan.getCompanionConfig');

    $router->post('voice/huoshan/saveRole', [VolcengineRtcController::class, 'saveRole'])
        ->name('voice.huoshan.saveRole');
    $router->post('voice/huoshan/deleteRole', [VolcengineRtcController::class, 'deleteRole'])
        ->name('voice.huoshan.deleteRole');
    $router->post('voice/huoshan/getRole', [VolcengineRtcController::class, 'getRole'])
        ->name('voice.huoshan.getRole');
});

Route::group([
    'namespace'  => 'App\\Http\\Controllers',
    'middleware' => ['api', 'wateAuth'],
    'prefix'     => 'ai/tools'
], function (Router $router) {
    $router->post('aliBailianAuth',
        [ToolsController::class, 'aliBailianAuth'])
        ->name('ai.Tools.aliBailianAuth');
});

Route::group([
    'namespace'  => 'App\\Http\\Controllers',
    'middleware' => ['api', 'wateAuth'],
    'prefix'     => 'offline/tasks'
], function (Router $router) {
    $router->post('lists', [OfflineTaskController::class, 'lists'])
        ->name('offline.tasks.lists');

    $router->post('stop', [OfflineTaskController::class, 'stop'])
        ->name('offline.tasks.stop');
    $router->post('continue', [OfflineTaskController::class, 'continue'])
        ->name('offline.tasks.continue');
    $router->post('delete', [OfflineTaskController::class, 'delete'])
        ->name('offline.tasks.delete');
});

