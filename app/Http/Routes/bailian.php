<?php

use App\Http\Controllers\Api\Bailian\ArticleController;
use App\Http\Controllers\Api\Bailian\AssistantFileController;
use App\Http\Controllers\Api\Bailian\DirectoryController;
use App\Http\Controllers\Api\Bailian\DocumentController;
use App\Http\Controllers\Api\Bailian\IndexController;
use App\Http\Controllers\Api\Bailian\KnowledgeChatController;
use App\Http\Controllers\Api\Bailian\KnowledgeController;
use App\Http\Controllers\Api\Bailian\KnowledgeItemController;
use App\Http\Controllers\Api\Bailian\MemberController;
use App\Http\Controllers\Api\Bailian\TagController;
use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Route;

Route::group([
    'prefix'     => 'bailian',
    'namespace'  => 'App\\Http\\Controllers',
    'middleware' => ['api'],
], function (Router $router) {
    $router->get('knowledge/{knowledge}', [KnowledgeController::class, 'detailNoAuth'])
        ->name('bailian.knowledge.detail_no_auth');

    $router->get('knowledge/item/{knowledge}', [KnowledgeItemController::class, 'indexNoAuth'])
        ->name('bailian.knowledge.item.list_no_auth');
});

Route::group([
    'prefix'     => 'bailian',
    'namespace'  => 'App\\Http\\Controllers',
    'middleware' => ['api', 'wateAuth'],
], function (Router $router) {
    //首页
    $router->post('bailian/index', [IndexController::class, 'index'])->name('bailian.index');
    $router->post('bailian/audio_to_text', [IndexController::class, 'audioToText'])->name('bailian.audio_to_text');
    $router->post('bailian/image_to_text', [IndexController::class, 'imageToText'])->name('bailian.image_to_text');
    $router->post('bailian/url_to_text', [IndexController::class, 'urlToText'])->name('bailian.url_to_text');
    $router->post('bailian/polish_text', [IndexController::class, 'polishText'])->name('bailian.polish_text');//润色
    $router->post('bailian/correct_text', [IndexController::class, 'correctText'])->name('bailian.correct_text');

    // 知识库相关接口
    $router->post('knowledge/before', [KnowledgeController::class, 'before'])->name('bailian.knowledge.before');
    $router->post('knowledge/list', [KnowledgeController::class, 'list'])->name('bailian.knowledge.list');
    $router->post('knowledge/create', [KnowledgeController::class, 'create'])->name('bailian.knowledge.create');
    $router->post('knowledge/detail', [KnowledgeController::class, 'detail'])->name('bailian.knowledge.detail');
    $router->post('knowledge/update', [KnowledgeController::class, 'update'])->name('bailian.knowledge.update');
    $router->post('knowledge/delete', [KnowledgeController::class, 'delete'])->name('bailian.knowledge.delete');
    $router->post('knowledge/level', [KnowledgeController::class, 'level'])->name('bailian.knowledge.level');
    //笔记
    $router->post('article/types', [ArticleController::class, 'types'])->name('bailian.article.types');
    $router->post('article/list', [ArticleController::class, 'list'])->name('bailian.article.list');
    $router->post('article/list_by_tag', [ArticleController::class, 'listByTag'])
        ->name('bailian.article.list_by_tag');
    $router->post('article/create', [ArticleController::class, 'create'])->name('bailian.article.create');
    $router->post('article/meeting_create', [ArticleController::class, 'meeting'])
        ->name('bailian.article.meeting_create');
    $router->post('article/detail', [ArticleController::class, 'detail'])->name('bailian.article.detail');
    $router->post('article/update', [ArticleController::class, 'update'])->name('bailian.article.update');
    $router->post('article/delete', [ArticleController::class, 'delete'])->name('bailian.article.delete');
    $router->post('article/join_knowledge', [ArticleController::class, 'joinKnowledge'])
        ->name('bailian.article.join.knowledge');
    $router->post('article/batch_join_knowledge', [ArticleController::class, 'batchJoinKnowledge'])
        ->name('bailian.article.batch_join.knowledge');
    $router->post('article/set_tag', [ArticleController::class, 'setTag'])->name('bailian.article.set_tag');
    $router->post('article/delete_tag', [ArticleController::class, 'deleteTag'])->name('bailian.article.delete_tag');

    // 文档上传和管理相关接口
    $router->post('document/list', [DocumentController::class, 'index'])->name('bailian.document.list');
    $router->post('document/create', [DocumentController::class, 'store'])->name('bailian.document.create');
    $router->post('document/update', [DocumentController::class, 'update'])->name('bailian.document.update');
    $router->post('document/delete', [DocumentController::class, 'delete'])->name('bailian.document.delete');
    $router->post('document/add-to-knowledge-base', [DocumentController::class, 'joinKnowledge'])
        ->name('bailian.document.join.knowledge');
    $router->post('document/download_url', [DocumentController::class, 'getDownloadUrl'])
        ->name('bailian.document.download_url');

    // 知识库目录相关接口
    $router->post('knowledge/directory/create', [DirectoryController::class, 'create'])
        ->name('bailian.directory.create');
    $router->post('knowledge/directory/update', [DirectoryController::class, 'update'])
        ->name('bailian.directory.update');
    $router->post('knowledge/directory/delete', [DirectoryController::class, 'delete'])
        ->name('bailian.directory.delete');

    // 知识库项目相关接口
    $router->post('knowledge/item/index', [KnowledgeItemController::class, 'index'])
        ->name('bailian.knowledge.item.list');
    $router->post('knowledge/item/directories', [KnowledgeItemController::class, 'directoryList'])
        ->name('bailian.knowledge.item.directory_list');
    $router->post('knowledge/item/delete', [KnowledgeItemController::class, 'delete'])
        ->name('bailian.knowledge.item.delete');
    $router->post('knowledge/item/itemable_delete', [KnowledgeItemController::class, 'itemalbleDelete'])
        ->name('bailian.knowledge.item.remove');
    $router->post('knowledge/item/move', [KnowledgeItemController::class, 'parent'])
        ->name('bailian.knowledge.item.move');
    $router->post('knowledge/item/list', [KnowledgeItemController::class, 'list'])
        ->name('bailian.knowledge.item.directories');

    //成员相关接口
    $router->post('member/list', [MemberController::class, 'index'])->name('bailian.member.list');
    $router->post('member/store', [MemberController::class, 'store'])->name('bailian.member.store');
    $router->post('member/update', [MemberController::class, 'update'])->name('bailian.member.update');
    $router->post('member/destroy', [MemberController::class, 'destroy'])->name('bailian.member.destroy');

    //智能体添加文件
    $router->post('assistant/index', [AssistantFileController::class, 'index'])->name('bailian.assistant.file.index');
    $router->post('assistant/add-file', [AssistantFileController::class, 'store'])->name('bailian.assistant.file.add');
    $router->post('assistant/status', [AssistantFileController::class, 'status'])
        ->name('bailian.assistant.file.status');
    //聊天
    $router->post('chat/index', [KnowledgeChatController::class, 'index'])->name('bailian.knowledge.chat.index');
    $router->post('chat/chat', [KnowledgeChatController::class, 'chat'])->name('bailian.knowledge.chat');
    $router->post('chat/like', [KnowledgeChatController::class, 'like'])->name('bailian.knowledge.chat.like');
    $router->post('chat/clear', [KnowledgeChatController::class, 'clear'])->name('bailian.knowledge.chat.clear');
    $router->post('chat/delete', [KnowledgeChatController::class, 'delete'])->name('bailian.knowledge.chat.delete');

    $router->post('chat/knowledge', [KnowledgeChatController::class, 'knowledge'])
        ->name('bailian.knowledge.chat.knowledge');
    $router->post('chat/knowledge_list', [KnowledgeChatController::class, 'knowledgeList'])
        ->name('bailian.knowledge.chat.knowledge_list');
    $router->post('chat/knowledge_list_by_type', [KnowledgeChatController::class, 'knowledgeListByType'])
        ->name('bailian.knowledge.chat.knowledge_list_by_type');

    //标签相关接口
    $router->post('tag/list', [TagController::class, 'index'])->name('bailian.tag.list');
    $router->post('tag/create', [TagController::class, 'store'])->name('bailian.tag.create');
    $router->post('tag/delete', [TagController::class, 'delete'])->name('bailian.tag.delete');
    $router->post('tag/create-by-article', [TagController::class, 'createByArticle'])
        ->name('bailian.tag.create.by.article');
});
