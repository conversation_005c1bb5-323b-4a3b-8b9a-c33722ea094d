<?php

use App\Http\Controllers\Api\Payment\CardController;
use App\Http\Controllers\Api\Payment\IndexController;
use App\Http\Controllers\Api\Payment\NotifyController;
use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Route;

Route::group([
    'namespace'  => 'App\\Http\\Controllers',
    'middleware' => ['api', 'wateAuth'],
], function (Router $router) {
    $router->post('payment/package', [IndexController::class, 'package'])
        ->name('pay.package');
    $router->post('payment/newPackage', [IndexController::class, 'newPackage'])
        ->name('pay.newPackage');
    $router->post('payment/check', [IndexController::class, 'checkStatus'])
        ->name('pay.checkOrder');
    $router->post('payment/card/info', [CardController::class, 'index'])
        ->name('pay.getCardInfo');
    $router->post('payment/card/exchange', [CardController::class, 'exchange'])
        ->name('pay.cardExchange');
});

Route::group([
    'namespace'  => 'App\\Http\\Controllers',
    'middleware' => ['api'],
], function (Router $router) {
    Route::any('payment/notify/{gateway}', [NotifyController::class, 'index'])
        ->name('api.payment.notify');
});


