<?php

use App\Http\Controllers\Api\Common\AiSettingController;
use App\Http\Controllers\Api\Common\ImageController;
use App\Http\Controllers\Api\Common\StrController;
use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Route;

Route::group([
    'namespace'  => 'App\\Http\\Controllers',
    'middleware' => ['api'],
], function (Router $router) {
    $router->post('common/system-resource', [ImageController::class, 'backImages'])->name('system.imageBack');
    $router->post('common/ai-list', [AiSettingController::class, 'aiLists'])->name('Aisetting.lists');
    $router->post('common/str/random', [StrController::class, 'random'])->name('str.random');
});
