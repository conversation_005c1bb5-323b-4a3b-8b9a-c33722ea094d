<?php

namespace App\Http\Controllers\App;

use App\Http\Controllers\Controller;
use App\Http\Resources\App\AiUnifyAssetCollection;
use App\Models\AiUnifyAsset;
use Illuminate\Http\Request;

class IndexController extends Controller
{
    public function index(Request $request)
    {
        $pageSize = $request->pagesize ?: 10;
        $videos   = AiUnifyAsset::where('status', AiUnifyAsset::STATUS_SUCCESS)
            ->noShow()
            ->where('platform', 1)
            ->whereIn('type', AiUnifyAsset::TYPE_VIDEO)
            ->orderByDesc('created_at')
            ->paginate($pageSize);
        return $request->kernel->success(new AiUnifyAssetCollection($videos));
    }
}