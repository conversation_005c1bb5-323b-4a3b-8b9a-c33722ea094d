<?php

namespace App\Http\Controllers\App\Asset;

use App\Http\Controllers\Controller;
use App\Http\Resources\App\ActivityCollection;
use App\Http\Resources\App\ActivityResource;
use App\Models\AiUnifyActivity;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;

class ActivityController extends Controller
{
    public function init(Request $request)
    {
        $activity = AiUnifyActivity::latest('start_at')
            ->where('status', AiUnifyActivity::STATUS_ON)
            ->select('title', 'id')
            ->get();
        return $request->kernel->success($activity);
    }

    public function index(Request $request)
    {
        $keyword  = $request->keyword;
        $status   = $request->status;
        $activity = AiUnifyActivity::query()
            ->when($keyword, function (Builder $builder, $keyword) {
                $builder->where('title', 'like', "%{$keyword}%");
            })
            ->when(is_numeric($status), function (Builder $builder, $status) {
                $builder->where('status', $status);
            })
            ->latest('start_at')
            ->paginate($request->pagesize ?: 10);
        return $request->kernel->success(new ActivityCollection($activity));
    }

    public function info(Request $request)
    {
        $request->kernel->validate([
            'id' => 'required',
        ]);
        $activity = AiUnifyActivity::find($request->id);
        if (! $activity) {
            return $request->kernel->error('活动不存在');
        }
        return $request->kernel->success(new ActivityResource($activity));
    }
}