<?php

namespace App\Http\Controllers\App\Asset;

use App\Http\Controllers\Controller;
use App\Models\DrawAudio;
use App\Models\PluginJmDraw;
use App\Models\PluginKeLing;
use App\Models\PluginMjDraw;
use Illuminate\Http\Request;

class ScoreController extends Controller
{
    public function mj(Request $request)
    {
        return $request->kernel->success(PluginMjDraw::Score);
    }

    public function keling(Request $request)
    {
        return $request->kernel->success(PluginKeLing::SCORE);
    }

    public function jm(Request $request)
    {
        return $request->kernel->success([
            'image' => PluginJmDraw::SCORE_IMAGE,
            'video' => PluginJmDraw::SCORE_ViDEO,
            'lip'   => PluginJmDraw::SCORE_LIP,
        ]);
    }

    public function suno(Request $request)
    {
        return $request->kernel->success(DrawAudio::SCORE);
    }
}