<?php

namespace App\Http\Controllers\App\Asset;

use App\Http\Controllers\Controller;
use App\Models\AiUnifyAsset;
use Illuminate\Http\Request;

class IndexController extends Controller
{
    public function detail(Request $request)
    {
        $request->kernel->validate([
            'id' => 'required|integer|exists:ai_unify_assets,id',
        ], [
            'id.required' => '资源ID不能为空',
            'id.integer'  => '资源ID必须是整数',
            'id.exists'   => '资源不存在',
        ]);
        $asset = AiUnifyAsset::find($request->id);
        return $request->kernel->success($asset->assetable->getAssetResource());
    }
}