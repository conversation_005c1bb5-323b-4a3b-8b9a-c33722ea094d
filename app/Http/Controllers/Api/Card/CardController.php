<?php

namespace App\Http\Controllers\Api\Card;

use App\Exceptions\ValidatorException;
use App\Http\Controllers\ApiController;
use App\Models\Business;
use App\Models\Company;
use App\Models\CompanyStaff;
use App\Models\Contact;
use App\Traits\CardTrait;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class CardController extends ApiController
{
    use CardTrait;

    public function index(Request $request)
    {
        $request->validate([
            'company_id'  => ['nullable', 'required_if:category_id,4'],
            'category_id' => ['nullable', 'integer'],
        ], [
            'company_id.required_if' => 'category_id为4时必添',
            'category_id.integer'    => 'category_id只能是数字'
        ]);

        $userId     = $request->kernel->id();
        $page       = $request->page ?? 1;
        $pageSize   = $request->pagesize ?? 10;
        $keyword    = $request->keyword ?? '';
        $categoryId = $request->category_id ?? 0;
        $companyId  = $request->company_id ?? 0;

        $ids        = [];
        $companyIds = [];
        $idss       = [];
        switch ($categoryId) {
            case 1:
                //个人人脉
                //我的朋友
                $ids  = $this->findFriend($userId);
                $idss = $ids;
                break;
            case 2:
                //企业人脉
                //企业认证的名片
                //我以及企业的朋友
                //todo 检测个人下面有没有企业认证的名片
                $company_ids = Business::where('uid', $userId)
                    ->where('is_company', 1)
                    ->pluck('company_id')
                    ->unique();

                $companyStaffIds = [];
                foreach ($company_ids as $company_id) {
                    $staff = CompanyStaff::where('uid', $userId)
                        ->where('company_id', $company_id)
                        ->where('is_work', 0)
                        ->where('is_open', 0)
                        ->first();

                    if (! empty($staff)) {
                        $companyStaffIds[] = $company_id;
                    }
                }
                if (! empty($companyStaffIds)) {
                    //todo 获取所有企业下的用户
                    $user_ids = Business::where('is_default', 0)
                        ->where('is_private', 1)
                        ->whereIn('company_id', $company_ids)
                        ->pluck('uid')
                        ->unique();

                    foreach ($user_ids as $user_id) {
                        $ids        = array_unique(array_merge($ids, $this->findFriend($user_id)));
                        $companyIds = $ids;
                    }
                }
                break;
            case 4:
                //当前企业的人脉
                if (empty($companyId)) {
                    throw new ValidatorException("企业ID不能为空");
                }
                $company = Company::find($companyId);
                if (! $company) {
                    throw new ValidatorException("企业不存在");
                }

                $user_ids = Business::where('company_id', $companyId)
                    ->where('is_company', 1)
                    ->pluck('uid')
                    ->unique();

                $ids = Contact::query()
                    ->whereIn('dstid', $user_ids)
                    ->where('uid', '<>', $company->uid)
                    ->where('status', 1)
                    ->pluck('from_bid')
                    ->unique()
                    ->toArray();

//                if ($user_ids->isNotEmpty()) {
//                    foreach ($user_ids as $user_id) {
//                        $ids = array_unique(array_merge($ids, $this->findFriend($user_id)));
//                    }
//                }
//
//                $staff = CompanyStaff::where('uid', $userId)
//                    ->where('company_id', $companyId)
//                    ->where('is_work', 0)
//                    ->where('is_open', 0)
//                    ->first();
//
//                if (! empty($staff)) {
//                    $user_ids = Business::where('company_id', $companyId)
//                        ->where('is_company', 1)
//                        ->where('is_private', 1)
//                        ->where('is_default')
//                        ->pluck('uid')
//                        ->unique();
//
//                    foreach ($user_ids as $user_id) {
//                        $ids = array_unique(array_merge($ids, $this->findFriend($user_id)));
//                    }
//                }

                break;
            default:
                //混合
                $ids         = $this->findFriend($userId);
                $idss        = $ids;
                $company_ids = Business::where('uid', $userId)
                    ->where('is_company', 1)
                    ->where('is_default', 0)
                    ->pluck('company_id')
                    ->unique();

                if ($company_ids->isNotEmpty()) {
                    //todo 获取所有企业下的用户
                    $user_ids = Business::where('is_default', 0)
                        ->where('is_private', 1)
                        ->whereIn('company_id', $company_ids)
                        ->pluck('uid')
                        ->unique();
                    //$ids = [];
                    foreach ($user_ids as $user_id) {
                        $companyIds = $this->findFriend($user_id);
                        $ids        = array_unique(array_merge($ids, $companyIds));
                    }
                }
                break;
        }

        $businessQuery = Business::whereIn('id', $ids)
            ->when($keyword, function ($query) use ($keyword) {
                $query->where(function ($q) use ($keyword) {
                    $q->where('nickname', 'like', "%{$keyword}%")
                        ->orWhere('company_name', 'like', "%{$keyword}%")
                        ->orWhere('phone', 'like', "%{$keyword}%")
                        ->orWhere('position', 'like', "%{$keyword}%");
                });
            })
            ->latest();

        $businesses = $businessQuery->paginate($pageSize, ['*'], 'page', $page);

        $fieldList = [];
        foreach ($businesses as $k => $info) {
            if (! empty($idss) || ! empty($companyIds)) {
                if (! empty($idss) && ! empty($companyIds)) {
                    if (in_array($info->id, $idss)) {
                        $fieldList[$k]['from'] = "私人";
                    }
                    if (in_array($info->id, $companyIds)) {
                        $fieldList[$k]['from'] = "企业";
                    }
                }
                if (! empty($idss) && empty($companyIds)) {
                    $fieldList[$k]['from'] = "私人";
                }
                if (empty($idss) && ! empty($companyIds)) {
                    $fieldList[$k]['from'] = "企业";
                }
            }
            $fieldList[$k]['id']           = $info->id;
            $fieldList[$k]['nickname']     = $info->nickname;
            $fieldList[$k]['company_name'] = $info->company_name;
            $fieldList[$k]['phone']        = $info->phone;
            $fieldList[$k]['avatar']       = $info->avatar;
            $fieldList[$k]['position']     = $info->position;
            $fieldList[$k]['wechat']       = $info->wechat;
            $fieldList[$k]['email']        = $info->email;
            $fieldList[$k]['address']      = $info->address;
            $fieldList[$k]['industry']     = $info->industry;
            $fieldList[$k]['is_work']      = $info->is_work;
            $fieldList[$k]['is_company']   = $info->is_company;
            $fieldList[$k]['annex']        = $info->annex ?? '';
            $fieldList[$k]['annex_name']   = $info->annex_name ?? '';
            $contacts                      = Contact::where('dstid', $info->uid)
                ->where('to_bid', $info->id)
                ->first();
            $fieldList[$k]['join_time']    = (string) $contacts->created_at;
            $fieldList[$k]['is_private']   = 0;
            if ($contacts->uid == $userId) {
                $fieldList[$k]['is_private'] = 1;
            }
        }
        $data['total']        = $businesses->total();
        $data['per_page']     = $pageSize;
        $data['current_page'] = $page;
        $data['last_page']    = ceil($data['total'] / $pageSize);
        $data['data']         = $fieldList;

        return $request->kernel->success($data);
    }

    public function change(Request $request)
    {
        $keyword      = $request->keyword ?? '';
        $page         = $request->page ?? 1;
        $pageSize     = $request->pagesize ?? 10;
        $userId       = $request->kernel->id();
        $contactQuery = Contact::where('is_change', 0)
            ->with(['business'])
            ->where('dstid', $userId)
            ->where('status', 0)
            ->when($keyword, function ($query) use ($keyword) {
                $query->whereHas('fromBusiness', function ($query) use ($keyword) {
                    $query->where('nickname', 'like', '%'.$keyword.'%')
                        ->whereOr('company_name', 'like', '%'.$keyword.'%')
                        ->whereOr('position', 'like', '%'.$keyword.'%');
                });
            });

        $contacts = $contactQuery->latest()->paginate($pageSize, ['*'], 'page', $page);

        $fieldList = [];
        foreach ($contacts as $k => $contact) {
            $fieldList[] = [
                'id'                => $contact->id,
                'to_bid'            => $contact->to_bid,
                'to_company_name'   => $contact->business->company_name ?? '',
                'to_position'       => $contact->business->position ?? '',
                'from_bid'          => $contact->from_bid,
                'from_avatar'       => $contact->fromBusiness->avatar ?? '',
                'from_nickname'     => $contact->fromBusiness->nickname ?? '',
                'from_is_work'      => $contact->fromBusiness->is_work ?? '',
                'from_is_company'   => $contact->fromBusiness->is_company ?? '',
                'from_company_name' => $contact->fromBusiness->company_name ?? '',
                'from_position'     => $contact->fromBusiness->position ?? '',
                'change_time'       => (string) $contact->created_at,
            ];
        }

        $data['total']        = $contacts->total();
        $data['per_page']     = $contacts->perPage();
        $data['current_page'] = $contacts->currentPage();
        $data['last_page']    = $contacts->lastPage();
        $data['data']         = $fieldList;

        return $request->kernel->success($data);
    }

    public function rank(Request $request)
    {
        $page        = $request->page ?? 1;
        $pageSize    = $request->pagesize ?? 10;
        $keyword     = $request->keyword ?? '';
        $province    = $request->province ?? '';
        $city        = $request->city ?? '';
        $area        = $request->area ?? '';
        $industry    = $request->industry ?? '';
        $is_position = $request->is_position ?? '';
        $is_phone    = $request->is_phone ?? '';
        $is_wechat   = $request->is_wechat ?? '';
        $is_email    = $request->is_email ?? '';
        $is_company  = $request->is_company ?? '';
        $is_business = $request->is_business ?? '';

        $userId = $request->kernel->id();

        $position = "";
        if ($is_position == 1) {
            $position = Business::where('uid', $userId)
                ->where('is_default', 1)
                ->value('position');
        }

        $businessQuery = DB::table('business as b')
            ->leftJoin('users as u', 'b.uid', '=', 'u.id')
            ->leftJoin('interaction_likes as l', 'b.id', '=', 'l.likeable_id')
            ->leftJoin('browse_records as r', 'b.id', '=', 'r.bid')
            ->when($is_email, fn($query) => $query->where('b.email', '<>', ''))
            ->when($is_phone, fn($query) => $query->where('b.phone', '<>', ''))
            ->when($is_wechat, fn($query) => $query->where('b.wechat', '<>', ''))
            ->when($is_company, fn($query) => $query->where('b.company_name', '<>', ''))
            ->when($is_business, fn($query) => $query->where('b.business_name', '<>', ''))
            ->when($position, fn($query) => $query->orWhere('b.position', 'like', "%$position%"))
            ->when($province, fn($query) => $query->where('b.province', $province))
            ->when($city, fn($query) => $query->where('b.city', $city))
            ->when($area, fn($query) => $query->where('b.area', $area))
            ->when($industry, fn($query) => $query->where('b.industry', $industry))
            ->where(fn($query) => $keyword ?
                $query->orWhere('b.nickname', 'like', "%$keyword%")
                    ->orWhere('b.company_name', 'like', "%$keyword%")
                    ->orWhere('b.phone', 'like', "%$keyword%")
                    ->orWhere('b.position', 'like', "%$keyword%") : null
            )
            ->whereNull('b.deleted_at')
            ->selectRaw("b.id, b.uid, b.position, b.company_name, b.avatar, b.nickname, 
                 (IFNULL(SUM(r.count) * 1, 0) + IFNULL(COUNT(l.id) * 5, 0)) as total_order_count")
            ->groupBy('b.id')
            ->orderByDesc('total_order_count');

        $businesses = $businessQuery->take(100)->get();
        $count      = $businesses->count();

        $default  = Business::where('uid', $userId)->where('is_default', 1)->first();
        $nickname = $default->nickname ?? '';
        $avatar   = $default->avatar ?? '';

        $fieldList = [];
        $rank      = 0;
        $ke        = 0;
        foreach ($businesses as $k => $business) {
            $fieldList[$k]['num'] = "";
            if ($k < 100) {
                if ($page == 1) {
                    $ke = $k + 1;
                } else {
                    if ($k == 0) {
                        $ke = (($page - 1) * $pageSize) + 1;
                    } else {
                        $ke++;
                    }
                }
                $fieldList[$k]['num'] = $ke;
                if ($business->uid == $userId) {
                    if ($rank == 0) {
                        $rank     = $fieldList[$k]['num'];
                        $nickname = $business->nickname;
                        $avatar   = $business->avatar;
                    } else {
                        if ($fieldList[$k]['num'] < $rank) {
                            $rank = $fieldList[$k]['num'];
                        }
                    }
                }
            }
            $fieldList[$k]['bid']               = $business->id;
            $fieldList[$k]['nickname']          = $business->nickname;
            $fieldList[$k]['avatar']            = $business->avatar;
            $fieldList[$k]['position']          = $business->position;
            $fieldList[$k]['company_name']      = $business->company_name;
            $fieldList[$k]['total_order_count'] = $business->total_order_count;
        }

        $data = [
            'rank'         => $rank,
            'nickname'     => $nickname,
            'avatar'       => $avatar,
            'total'        => $count,
            'per_page'     => $pageSize,
            'current_page' => $page,
            'last_page'    => ceil($count / $pageSize),
            'data'         => $fieldList,
        ];

        return $request->kernel->success($data);
    }

}
