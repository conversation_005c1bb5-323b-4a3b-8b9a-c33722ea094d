<?php

namespace App\Http\Controllers\Api\Advert;

use App\Http\Controllers\Controller;
use App\Http\Resources\Advert\AdvertResource;
use App\Models\Advert;
use Illuminate\Http\Request;

class IndexController extends Controller
{
    public function index(Request $request)
    {
        $position = $request->position ?? '';
        $adverts  = Advert::query()
            ->where('status', 1)
            ->when($position, function ($query) use ($position) {
                $query->where('position', $position);
            })
            ->orderBy('order', 'asc')
            ->get();
        return $request->kernel->success(AdvertResource::collection($adverts));
    }
}
