<?php

namespace App\Http\Controllers\Api\User;

use App\Http\Controllers\Controller;
use App\Http\Resources\Account\LogsCollection;
use App\Http\Resources\Account\OrderCollection;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;
use Modules\Payment\Enums\PaymentStatus;
use Modules\Payment\Models\AccountRule;
use Modules\Payment\Models\Payment;

class AccountController extends Controller
{
    protected array $rules = [6, 7, 8, 9, 11, 12, 14];

    public function orders(Request $request)
    {
        $user     = $request->kernel->user();
        $pagesize = $request->pagesize ?: 10;
        $orders   = Payment::ofUser($user)
            ->whereIn('status', [
                PaymentStatus::PAID,
                PaymentStatus::REFUND,
                PaymentStatus::REFUND_PART,
            ])
            ->orderByDesc('paid_at')
            ->paginate($pagesize);
        return $request->kernel->success(new OrderCollection($orders));
    }

    public function logsInit(Request $request)
    {
        $ruleType = AccountRule::whereIn('id', $this->rules)
            ->pluck('name', 'id');
        $data     = [
            'form' => [
                [
                    'key'   => 'system',
                    'title' => env('APP_NAME')
                ]
            ],
            'mode' => [
                [
                    'key'   => 'create_work',
                    'title' => '资源生成'
                ],
            ],
            'type' => array_values($ruleType->map(function ($name, $key) {
                return [
                    'key'   => $key,
                    'title' => $name
                ];
            })->toArray()),
        ];
        return $request->kernel->success($data);
    }

    public function logs(Request $request)
    {
        $user      = $request->kernel->user();
        $pagesize  = $request->pagesize ?: 10;
        $startTime = $request->start_time;
        $endTime   = $request->end_time;

        $logs = $user->account->logs()
            ->when($startTime, function (Builder $query, $startTime) {
                $query->where('created_at', '>=', $startTime.' 00:00:00');
            })
            ->when($endTime, function (Builder $query, $endTime) {
                $query->where('created_at', '<=', $endTime.' 23:59:59');
            })
            ->orderByDesc('created_at')
            ->paginate($pagesize);

        return $request->kernel->success(new LogsCollection($logs));
    }

    public function scoreBalance(Request $request)
    {
        $user = $request->kernel->user();
        return $request->kernel->success([
            'balance' => $user->account->balance,
            'score'   => $user->account->score,
        ]);
    }
}