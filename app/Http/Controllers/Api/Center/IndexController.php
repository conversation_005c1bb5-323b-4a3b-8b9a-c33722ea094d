<?php

namespace App\Http\Controllers\Api\Center;

use App\Http\Controllers\Controller;
use App\Http\Resources\App\Publish\PublishCollection;
use App\Models\AiUnifyAsset;
use App\Models\User;
use App\Models\UserNicknameAs;
use Illuminate\Http\Request;
use Modules\Interaction\Models\Follow;
use Modules\Interaction\Models\Like;

class IndexController extends Controller
{
    public function index(Request $request)
    {
        $request->kernel->validate([
            'user_id' => 'required|integer|exists:users,id',
        ], [
            'user_id.required' => '用户不能为空',
            'user_id.integer'  => '用户参数异常',
            'user_id.exists'   => '用户不存在',
        ]);
        $targetUser = User::withCount([
            'fans',
            'follow',
        ])->find($request->user_id);

        $userAsset = AiUnifyAsset::ofUser($targetUser->id)
            ->pluck('id')->toArray() ?: [];
        $likes     = Like::where('likeable_type', app(AiUnifyAsset::class)->getMorphClass())
            ->whereIn('likeable_id', $userAsset)
            ->count('id');
        $userId    = $request->kernel->id() ?: 0;
        $option    = UserNicknameAs::where('user_id', $userId)
            ->where('target_id', $request->user_id)
            ->first();
        return $request->kernel->success([
            'user_id'     => $targetUser->id,
            'nickname'    => $targetUser->info->nickname,
            'origin_name' => $targetUser->info->origin_name,
            'avatar'      => $targetUser->info->avatar_url,
            'description' => $targetUser->info->description,
            'is_self'     => $targetUser->id == $userId,
            'dont_show'   => (bool) ($option?->dont_show ?: 0),
            'black'       => (bool) ($option?->black ?: 0),
            'web_url'     => 'https://watestar.com/user/'.$request->user_id,
            'interaction' => [
                'interaction_type' => $targetUser->getMorphClass(),
                'interaction_id'   => $targetUser->getKey(),
                'followed'         => Follow::ofUser($userId)
                    ->where('followable_type', $targetUser->getMorphClass())
                    ->where('followable_id', $targetUser->getKey())
                    ->exists(),
                'fans'             => $targetUser->fans_count,
                'follow'           => $targetUser->follow_count,
                'used'             => 0,
                'like'             => $likes,
            ],
            'im_user_id'  => $targetUser->imUser?->im_user_id,
        ]);
    }

    public function assets(Request $request)
    {
        $request->kernel->validate([
            'user_id' => 'required|integer|exists:users,id',
        ], [
            'user_id.required' => '用户不能为空',
            'user_id.integer'  => '用户参数异常',
            'user_id.exists'   => '用户不存在',
        ]);
        $assets = AiUnifyAsset::ofUser($request->user_id)
            ->has('assetable')
            ->where('status', AiUnifyAsset::STATUS_SUCCESS)
            ->where('platform', 1)
            ->orderByDesc('created_at')
            ->paginate($request->pagesize ?: 10);
        return $request->kernel->success(new PublishCollection($assets));
    }
} 