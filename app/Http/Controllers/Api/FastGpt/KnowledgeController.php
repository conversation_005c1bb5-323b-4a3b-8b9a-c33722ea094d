<?php

namespace App\Http\Controllers\Api\FastGpt;

use App\Exceptions\ValidatorException;
use App\Http\Controllers\ApiController;
use App\Http\Requests\FastGpt\Knowledge\AddKnowledgeRequest;
use App\Http\Resources\FastGpt\KnowledgeDirectoryResource;
use App\Http\Resources\FastGpt\KnowledgeResource;
use App\Models\Enums\FastgptKnowledgeLevelEnum;
use App\Models\FastgptApp;
use App\Models\FastgptKnowledge;
use App\Models\FastgptKnowledgeTrainingOrder;
use App\Packages\FastGpt\FastGpt;
use App\Traits\FastGptTrait;
use Illuminate\Http\Request;

class KnowledgeController extends ApiController
{
    use FastGptTrait;

    public function search(Request $request)
    {
        $request->kernel->validate([
            'knowledge_id' => ['required', 'exists:fastgpt_knowledge,id'],
            'content'      => 'required',
        ], [
            'knowledge_id.required' => '知识库ID不能为空',
            'knowledge_id.exists'   => '知识库不存在',
            'content.required'      => '搜索内容不能为空',
        ]);

        $knowledge_id = $request->knowledge_id;
        $content      = $request->content;

        $knowledge = FastgptKnowledge::find($knowledge_id);
        if (! $knowledge) {
            throw new ValidatorException('知识库不存在');
        }

        $result = FastGpt::knowledge()->search($knowledge->dataset_id, $content);
        return $request->kernel->success($result);
    }

    /**
     * Notes: 知识库列表
     *
     * @Author: 玄尘
     * @Date: 2024/12/2 13:02
     * @param  \Illuminate\Http\Request  $request
     * @return mixed
     */
    public function list(Request $request)
    {
        $userId = $request->kernel->id();
        $level  = $request->level ?? '';
        $title  = $request->title ?? '';

        $knowledge = FastgptKnowledge::query()
            ->withCount('knowledgeSets')
            ->when($title, function ($query) use ($title) {
                $query->where('name', 'like', "%{$title}%");
            })
            ->when($level, function ($query) use ($level) {
                $query->where('level', $level);
            })
            ->where('user_id', $userId)
            ->OfEnabled()
            ->get();

        return $request->kernel->success(KnowledgeResource::collection($knowledge));
    }

    public function create(Request $request)
    {
        $addRequest = new AddKnowledgeRequest();

        $request->kernel->validate($addRequest->rules(), $addRequest->messages());

        $department_id = $request->department_id;
        $name          = $request->name;
        $type          = $request->type ?? 'dataset';
        $description   = $request->intro ?? '';
        $level         = $request->level;
        $company_id    = $request->company_id;
        $user          = $request->kernel->user();

        $this->canCreateKnowledge($user);//检查是否可以创建知识库

        $result = FastGpt::knowledge()->create($name, $type, $description);
        $detail = FastGpt::knowledge()->knowledgeDetail($result['data']);

        $knowledge = FastgptKnowledge::create([
            'user_id'       => $user->id,
            'dataset_id'    => $result['data'],
            'company_id'    => $user->company->id ?? 0,
            'department_id' => $department_id ?? 0,
            'name'          => $name,
            'type'          => $type,
            'level'         => $level,
            'source'        => $detail['data'],
            'description'   => $description,
        ]);

        $knowledge->fastgptCollaborators()->create([
            'user_id'    => $user->id,
            'is_manager' => 1
        ]);

        return $request->kernel->success(new KnowledgeResource($knowledge));
    }

    /**
     * Notes: 编辑
     *
     * @Author: 玄尘
     * @Date: 2024/12/20 10:19
     * @param  \Illuminate\Http\Request  $request
     * @return mixed
     * @throws \App\Exceptions\ValidatorException
     */
    public function update(Request $request)
    {
        try {
            $request->kernel->validate([
                'knowledge_id' => ['required', 'exists:fastgpt_knowledge,id'],
                'name'         => 'required',
            ], [
                'knowledge_id.required' => '知识库id不存在',
                'knowledge_id.exists'   => '知识库不存在',
                'name.required'         => '知识库名称必填',
                'level.required'        => '知识库权限必填',
            ]);

            $user         = $request->kernel->user();
            $name         = $request->name;
            $knowledge_id = $request->knowledge_id;
            $level        = $request->level;
            $intro        = $request->intro ?? '';

            $knowledge = FastgptKnowledge::find($knowledge_id);
            if (! $knowledge) {
                throw new ValidatorException('知识库不存在');
            }

            if (! $knowledge->canEdit($user)) {
                throw new ValidatorException('您没有权限');
            }

            $res = FastGpt::knowledge()->update($knowledge->dataset_id, $name, $intro);
            if ($res['code'] !== 200) {
                throw new ValidatorException($res['message']);
            }

            $data = [
                'name'        => $name,
                'description' => $intro,
                'level'       => $level,
            ];
            $knowledge->update(array_filter($data));
            return $request->kernel->success('修改成功');
        } catch (\Exception $e) {
            return $request->kernel->error($e->getMessage());
        }
    }

    /**
     * Notes: 知识库详情
     *
     * @Author: 玄尘
     * @Date: 2024/12/16 15:35
     * @param  \Illuminate\Http\Request  $request
     * @return mixed
     * @throws \App\Exceptions\ValidatorException
     */
    public function detail(Request $request)
    {
        $request->kernel->validate([
            'knowledge_id' => ['required', 'exists:fastgpt_knowledge,id'],
        ], [
            'knowledge_id.required' => '缺少知识库id',
        ]);

        $user         = $request->kernel->user();
        $knowledge_id = $request->knowledge_id;

        $knowledge = FastgptKnowledge::find($knowledge_id);
        if (! $knowledge) {
            throw new ValidatorException('知识库不存在');
        }
        if (! $knowledge->canView($user)) {
            throw new ValidatorException('您没有权限');
        }

        return $request->kernel->success(new KnowledgeResource($knowledge));
    }

    public function delete(Request $request)
    {
        try {
            \DB::beginTransaction();
            $request->kernel->validate([
                'knowledge_id' => ['required', 'exists:fastgpt_knowledge,id'],
            ], [
                'knowledge_id.required' => '缺少知识库id',
            ]);

            $user = $request->kernel->user();

            $knowledge_id = $request->knowledge_id;

            $knowledge = FastgptKnowledge::find($knowledge_id);
            if (! $knowledge) {
                throw new ValidatorException('知识库不存在');
            }

            if (! $knowledge->canDelete($user)) {
                throw new ValidatorException('您没有权限');
            }

            $result = FastGpt::knowledge()->deleteKnowledge($knowledge->dataset_id);
            //删除协作者
            $knowledge->fastgptCollaborators()->delete();
            //删除集合
            $knowledge->knowledgeSets()->delete();
            //删除应用
            $apps = FastgptApp::where('dataset_ids', 'like', "%{$knowledge->dataset_id}%")->get();
            if ($apps->isNotEmpty()) {
                $apps->each(function ($app) use ($knowledge) {
                    $res = FastGpt::app()->appDelete($app->app_id);
                    $app->chats()->delete();
                    $app->delete();
                });
            }
            $knowledge->delete();
            \DB::commit();
            return $request->kernel->success('删除成功');
        } catch (\Exception $exception) {
            \DB::rollBack();
            return $request->kernel->error($exception->getMessage());
        }
    }

    public function status(Request $request)
    {
        $request->kernel->validate([
            'knowledge_id' => 'required',
        ], [
            'knowledge_id.required' => '缺少知识库id',
        ]);

        $user = $request->kernel->user();

        $knowledge_id = $request->knowledge_id;
        $knowledge    = FastgptKnowledge::find($knowledge_id);
        if (! $knowledge) {
            throw new ValidatorException('知识库不存在');
        }

        if (! $knowledge->canStatus($user)) {
            throw new ValidatorException('您没有权限');
        }

        $status            = $knowledge->status;
        $knowledge->status = $status ? 0 : 1;
        $knowledge->save();

        return $request->kernel->success('操作成功');
    }

    /**
     * Notes: 训练订单
     *
     * @Author: 玄尘
     * @Date: 2024/12/16 16:10
     * @param  \Illuminate\Http\Request  $request
     * @return mixed
     * @throws \App\Exceptions\ValidatorException
     */
    public function trainingOrder(Request $request)
    {
        $request->kernel->validate([
            'knowledge_id' => ['required', 'exists:fastgpt_knowledge,id'],
            //            'name'       => 'required',
        ], [
            'knowledge_id.required' => '缺少知识库id',
            'name.required'         => '订单名称',
        ]);

        $knowledge_id = $request->knowledge_id;
        $name         = $request->input('name', '');

        $knowledge = FastgptKnowledge::find($knowledge_id);
        if (! $knowledge) {
            throw new ValidatorException('知识库不存在');
        }

        $result        = FastGpt::knowledge()->trainingOrder($knowledge->dataset_id);
        $trainingOrder = FastgptKnowledgeTrainingOrder::create([
            'knowledge_id' => $knowledge->id,
            'name'         => $name,
            'bill_id'      => $result['data'],
        ]);

        return $request->kernel->success('创建成功');
    }

    /**
     * Notes: 随机一个知识库
     *
     * @Author: 玄尘
     * @Date: 2025/2/28 08:58
     * @param  \Illuminate\Http\Request  $request
     * @return mixed
     */
    public function random(Request $request)
    {
        $knowledge = FastgptKnowledge::query()
            ->OfLevel(FastgptKnowledgeLevelEnum::ALL->value)
            ->OfEnabled()
            ->inRandomOrder()
            ->first();
        return $request->kernel->success(new KnowledgeResource($knowledge));
    }

    /**
     * Notes: 目录
     *
     * @Author: 玄尘
     * @Date: 2025/3/6 14:44
     * @param  \Illuminate\Http\Request  $request
     * @return mixed
     */
    public function directory(Request $request)
    {
        $request->kernel->validate([
            'knowledge_id' => ['required', 'exists:fastgpt_knowledge,id'],
        ], [
            'knowledge_id.required' => '缺少知识库id',
        ]);
        $knowledge = FastgptKnowledge::find($request->knowledge_id);
        if (! $knowledge) {
            throw new ValidatorException('知识库不存在');
        }
        $sets = $knowledge->knowledgeSets()
            ->whereNull('parent_id')
            ->with([
                'children' => function ($query) {
                    $query->orderBy('order');
                }
            ])
            ->orderBy('order')
            ->latest('created_at')
            ->get();

        return $request->kernel->success(KnowledgeDirectoryResource::collection($sets));
    }
}
