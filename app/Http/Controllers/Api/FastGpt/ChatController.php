<?php

namespace App\Http\Controllers\Api\FastGpt;

use App\Http\Controllers\ApiController;
use App\Http\Resources\FastGpt\KnowledgeChatResource;
use App\Models\FastgptChat;
use App\Packages\FastGpt\BaseClient;
use App\Packages\FastGpt\FastGpt;
use App\Traits\FastGptTrait;
use DB;
use Exception;
use GuzzleHttp\Client;
use Illuminate\Http\Request;
use Psr\Http\Message\StreamInterface;

class ChatController extends ApiController
{
    use FastGptTrait;

    public function testChat(Request $request)
    {
        $request->kernel->validate([
            'app_id'  => 'required',
            'message' => 'required',
            'chat_id' => 'required',
        ], [
            'app_id.required'  => '缺少应用id',
            'message.required' => '缺少内容',
            'chat_id.required' => '缺少chat_id',
        ]);

        $app_id       = $request->input('app_id');
        $message      = $request->input('message');
        $chatId       = $request->input('chat_id');
        $user_data_id = $request->input('user_data_id') ?? $this->getChatId(18);
        $ai_data_id   = $request->input('ai_data_id') ?? $this->getChatId(18);

        if (! $chatId) {
            $chatId = $this->getChatId(12);
        }

        $user = $request->kernel->user();
        $app  = $this->getAppById($app_id, true);
        if (! $app->api_token) {
            throw  new Exception('应用缺少token');
        }
        //        if ($app->canStatus($user)) {
        //            throw new \Exception('无权操作');
        //        }

        $chatMes = [
            "messages" => [
                [
                    'dataId'  => $user_data_id,
                    "role"    => "user",
                    "content" => $message
                ],
                [
                    'dataId'  => $ai_data_id,
                    "role"    => "assistant",
                    "content" => ''
                ],
            ],
            "appId"    => $app_id,
            "detail"   => true,
            "chatId"   => $chatId,
            "stream"   => true
        ];
        $chat    = $app->chats()->where('chat_id', $chatId)->first();
        if ($chat) {
            $chat->update([
                'title'   => $message,
                'message' => $message,
            ]);
        } else {
            $chat = FastgptChat::create([
                'user_id'        => $user->id ?? null,
                'fastgpt_app_id' => $app->id,
                'chat_id'        => $chatId,
                'title'          => $message,
                'message'        => $message,
                'user_data_id'   => $user_data_id,
                'ai_data_id'     => $ai_data_id,
            ]);
        }

        ignore_user_abort(true);
        set_time_limit(300);
        session_write_close();
        header('Content-Type: text/event-stream');
        header('Cache-Control: no-cache');
        header('Connection: keep-alive');
        header('X-Accel-Buffering: no');
        ob_implicit_flush(1);

        $client = new Client([
            'base_uri' => 'http://36.133.86.95:3000/api/',
            'verify'   => false,
        ]);

        $this->response = $client->request('POST', 'v1/chat/completions', [
            'headers' => [
                'Authorization' => 'Bearer '.$app->api_token,
            ],
            'json'    => $chatMes,
            'stream'  => true,
        ]);
        foreach ($this->getIterator() as $response) {
            if (! blank($response)) {
                echo $response."\n";
            }
            ob_flush();
            flush();
            usleep(20000);
        }
    }

    public function getIterator()
    {
        while (! $this->response->getBody()->eof()) {
            $line = $this->readLine($this->response->getBody());

            yield $line;
        }
    }

    private function readLine(StreamInterface $stream): string
    {
        $buffer = '';

        while (! $stream->eof()) {
            if ('' === ($byte = $stream->read(1))) {
                return $buffer;
            }
            $buffer .= $byte;
            if ($byte === "\n") {
                break;
            }
        }

        return $buffer;
    }

    public function history(Request $request)
    {
        $request->kernel->validate([
            'app_id' => 'required',
        ], [
            'app_id.required' => '缺少应用id',
        ]);

        $app_id   = $request->input('app_id');
        $pageSize = $request->input('pageSize') ?? 10;
        $user     = $request->kernel->user();

        $app = $this->getAppById($app_id, true);
        //
        $chats = FastgptChat::with(['app'])
            ->where('user_id', $user->id)
            ->where('fastgpt_app_id', $app->id)
            ->orderByDesc('is_top')
            ->orderByDesc('created_at')
            ->paginate(100);
        return $request->kernel->success(KnowledgeChatResource::collection($chats));
        //        $res = FastGpt::chat()->chatList($app);
        //        return $request->kernel->success($res['data']);
    }

    public function chat(Request $request)
    {
        $request->kernel->validate([
            'app_id'  => 'required',
            'message' => 'required',
            'chat_id' => 'required',
        ], [
            'app_id.required'  => '缺少应用id',
            'message.required' => '缺少内容',
            'chat_id.required' => '缺少chat_id',
        ]);

        $app_id       = $request->input('app_id');
        $message      = $request->input('message');
        $chatId       = $request->input('chat_id');
        $user_data_id = $request->input('user_data_id') ?? $this->getChatId(18);
        $ai_data_id   = $request->input('ai_data_id') ?? $this->getChatId(18);

        if (! $chatId) {
            $chatId = $this->getChatId(12);
        }

        $user = $request->kernel->user();
        $app  = $this->getAppById($app_id, true);
        //        if ($app->canStatus($user)) {
        //            throw new \Exception('无权操作');
        //        }
        $chat = $app->chats()->where('chat_id', $chatId)->first();
        if ($chat) {
            $chat->update([
                'title'   => $message,
                'message' => $message,
            ]);
        } else {
            $chat = FastgptChat::create([
                'user_id'        => $user->id ?? null,
                'fastgpt_app_id' => $app->id,
                'chat_id'        => $chatId,
                'title'          => $message,
                'message'        => $message,
                'user_data_id'   => $user_data_id,
                'ai_data_id'     => $ai_data_id,
            ]);
        }
        if (BaseClient::isNew()) {
            $chatMes = [
                "messages" => [
                    [
                        'dataId'  => $user_data_id,
                        "role"    => "user",
                        "content" => $message
                    ],
                ],
                "appId"    => $app_id,
                "detail"   => true,
                "chatId"   => $chatId,
                "stream"   => true
            ];
        } else {
            $chatMes = [
                "messages" => [
                    [
                        'dataId'  => $user_data_id,
                        "role"    => "user",
                        "content" => $message
                    ],
                    [
                        'dataId'  => $ai_data_id,
                        "role"    => "assistant",
                        "content" => ''
                    ],
                ],
                "appId"    => $app_id,
                "detail"   => true,
                "chatId"   => $chatId,
                "stream"   => true
            ];
        }

        FastGpt::chat()->chat($chatMes, $app, false);
        FastGpt::chat()->updateChatTitle($app_id, $chatId, $message);

        die;
    }

    public function detail(Request $request)
    {
        $request->kernel->validate([
            'app_id' => 'required',
            //            'chat_id' => 'required',
        ], [
            'app_id.required'  => '缺少应用id',
            'chat_id.required' => '缺少消息id',
        ]);
        $app_id = $request->input('app_id');
        $chatId = $request->input('chat_id');
        $app    = $this->getAppById($app_id);
        $chat   = FastgptChat::where('chat_id', $chatId)->first();

        if (BaseClient::isNew()) {
            $data    = [
                'appId'    => $app_id,
                'title'    => $chat ? $chat->getTitle() : '新对话',
                'chatId'   => $chatId ?? $this->getChatId(12),
                'prologue' => $app->prologue,
            ];
            $res     = FastGpt::chat()->chatDetail($app_id, $chatId);
            $history = collect($res['data']['list']);
        } else {
            $res     = FastGpt::chat()->chatDetail($app_id, $chatId);
            $data    = [
                'appId'    => $res['data']['appId'],
                'title'    => $res['data']['title'],
                'chatId'   => $res['data']['chatId'] ?? $this->getChatId(12),
                'prologue' => $app->prologue,
            ];
            $history = collect($res['data']['history']);
        }

        if ($history->isNotEmpty()) {
            $data['history'] = $history->map(function ($item) {
                return array_filter([
                    '_id'              => $item['_id'],
                    'dataId'           => $item['dataId'],
                    'obj'              => $item['obj'],
                    'value'            => $item['value'],
                    'userGoodFeedback' => $item['userGoodFeedback'] ?? '',
                    'userBadFeedback'  => $item['userBadFeedback'] ?? '',
                ]);
            });
        }

        return $request->kernel->success($data);
    }

    /**
     * Notes: 点赞
     *
     * @Author: 玄尘
     * @Date: 2024/12/18 09:10
     * @param  \Illuminate\Http\Request  $request
     * @return mixed
     * @throws \App\Exceptions\ValidatorException
     */
    public function updateUserFeedback(Request $request)
    {
        $request->kernel->validate([
            'app_id'     => 'required',
            'chat_id'    => 'required',
            'chatItemId' => 'required',
        ], [
            'app_id.required'     => '缺少应用id',
            'chat_id.required'    => '缺少消息id',
            'chatItemId.required' => '缺少数据id',
        ]);

        $app_id           = $request->input('app_id');
        $chat_id          = $request->input('chat_id');
        $chatItemId       = $request->input('chatItemId');
        $userGoodFeedback = $request->input('userGoodFeedback', '');
        $userBadFeedback  = $request->input('userBadFeedback', '');

        $user = $request->kernel->user();

        $app = $this->getAppById($app_id, true);
//        if ($app->canStatus($user)) {
//            throw new Exception('无权操作');
//        }

        $res = FastGpt::chat()
            ->updateUserFeedback($app_id, $chat_id, $chatItemId, $userGoodFeedback, $userBadFeedback);

        return $request->kernel->success(true);
    }

    public function updateTitle(Request $request)
    {
        $request->kernel->validate([
            'app_id'  => 'required',
            'chat_id' => 'required',
            'title'   => 'required',
        ], [
            'app_id.required'  => '缺少应用id',
            'chat_id.required' => '缺少消息id',
            'title.required'   => '缺少标题',
        ]);

        $app_id = $request->input('app_id');
        $chatId = $request->input('chat_id');
        $title  = $request->input('title');

        $user = $request->kernel->user();
        $app  = $this->getAppById($app_id, true);
        $chat = FastgptChat::where('chat_id', $chatId)->first();
        if (! $chat) {
            return $request->kernel->error('数据不存在');
        }
        if ($chat->user_id != $user->id) {
            return $request->kernel->error('无权操作');
        }
        $res = FastGpt::chat()->updateChatTitle($app_id, $chatId, $title);
        $chat->update([
            'custom_title' => $title
        ]);
        return $request->kernel->success('修改成功');
    }

    public function top(Request $request)
    {
        $request->kernel->validate([
            'app_id'  => 'required',
            'chat_id' => 'required',
            'top'     => 'required',
        ], [
            'app_id.required'  => '缺少应用id',
            'chat_id.required' => '缺少消息id',
            'top.required'     => '缺少操作',
        ]);
        $app_id  = $request->input('app_id');
        $chat_id = $request->input('chat_id');
        $top     = $request->input('top');
        $app     = $this->getAppById($app_id, true);
        $user    = $request->kernel->user();

        $chat = $app->chats()->where('chat_id', $chat_id)->first();
        if (! $chat) {
            throw new Exception('对话不存在');
        }
        if ($chat->user_id != $user->id) {
            throw new Exception('无权操作');
        }

        $chat?->update([
            'is_top' => $top == true ? 1 : 0
        ]);

        $res = FastGpt::chat()->chatTop($app_id, $chat_id, $top);
        return $request->kernel->success(true);
    }

    public function delete(Request $request)
    {
        $request->kernel->validate([
            'app_id'  => 'required',
            'chat_id' => 'required',
        ], [
            'app_id.required'  => '缺少应用id',
            'chat_id.required' => '缺少消息id',
        ]);
        $app_id  = $request->input('app_id');
        $chat_id = $request->input('chat_id');

        $app  = $this->getAppById($app_id, true);
        $chat = $app->chats()->where('chat_id', $chat_id)->first();
        if (! $chat) {
            throw new Exception('对话不存在');
        }
        $user = $request->kernel->user();
        if ($chat->user_id != $user->id) {
            throw new Exception('无权操作');
        }

        try {
            DB::beginTransaction();

            $res = FastGpt::chat()->chatDelete($app_id, $chat_id);
            FastgptChat::where('chat_id', $chat_id)->delete();

            DB::commit();
            return $request->kernel->success(true);
        } catch (Exception $e) {
            DB::rollback();
            \Log::error('删除对话失败: '.$e->getMessage());
            throw new Exception('删除失败,请重试');
        }
    }

    public function clear(Request $request)
    {
        $request->kernel->validate([
            'app_id' => 'required',
        ], [
            'app_id.required' => '缺少应用id',
        ]);
        $app_id = $request->input('app_id');
        $app    = $this->getAppById($app_id, true);
        $user   = $request->kernel->user();
        if (! $app->canStatus($user)) {
            throw new Exception('无权操作');
        }

        $res = FastGpt::chat()->chatClear($app);
        FastgptChat::where('fastgpt_app_id', $app->id)->delete();

        return $request->kernel->success(true);
    }

    /**
     * Notes: 删除某个用户对话记录
     *
     * @Author: 玄尘
     * @Date: 2025/3/31 11:00
     */
    public function clearUser(Request $request)
    {
        $request->kernel->validate([
            'app_id' => 'required',
        ], [
            'app_id.required' => '缺少应用id',
        ]);
        $app_id = $request->input('app_id');
        $app    = $this->getAppById($app_id, true);
        $user   = $request->kernel->user();

        if (! $app->canStatus($user)) {
            throw new Exception('无权操作');
        }

        try {
            $chats = $app->chats()->where('user_id', $user->id)->get();
            foreach ($chats as $chat) {
                DB::beginTransaction();

                // 删除 FastGpt 聊天记录
                $res = FastGpt::chat()->chatDelete($app_id, $chat->chat_id);
                // 删除本地数据库记录
                FastgptChat::where('chat_id', $chat->chat_id)->delete();
                DB::commit();
            }

            return $request->kernel->success('删除成功');
        } catch (\Exception $e) {
            DB::rollback(); // 回滚事务
            // 记录日志，方便调试
            \Log::error('清除用户聊天记录失败: '.$e->getMessage(), [
                'app_id'  => $app_id,
                'user_id' => $user->id,
                'trace'   => $e->getTraceAsString(),
            ]);
            throw new Exception('删除聊天记录失败，请稍后重试'); // 提供更友好的错误信息
        }
    }

    /**
     * Notes: 获取对话记录列表
     *
     * @Author: 玄尘
     * @Date: 2025/2/27 13:50
     * @param  \Illuminate\Http\Request  $request
     * @return mixed
     */
    public function records(Request $request)
    {
        $request->kernel->validate([
            'app_id'  => 'required',
            'chat_id' => 'required',
        ], [
            'app_id.required'  => '缺少应用id',
            'chat_id.required' => '缺少消息id',
        ]);
        $app_id = $request->input('app_id');
        $chatId = $request->input('chat_id');

        $res = FastGpt::chat()->records($app_id, $chatId);
        return $request->kernel->success($res);
    }

}
