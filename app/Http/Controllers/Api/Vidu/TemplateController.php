<?php

namespace App\Http\Controllers\Api\Vidu;

use App\Http\Controllers\Controller;
use App\Http\Resources\Vidu\CategoryResource;
use App\Http\Resources\Vidu\TemplateCollection;
use App\Http\Resources\Vidu\TemplateResource;
use App\Models\PluginViduTemplate;
use App\Models\PluginViduTemplateCategory;
use Illuminate\Http\Request;

class TemplateController extends Controller
{
    public function categories(Request $request)
    {
        $categories = PluginViduTemplateCategory::query()->where('status', 1)->get();
        return $request->kernel->success(CategoryResource::collection($categories));
    }

    public function index(Request $request)
    {
        $category_id = $request->category_id ?? '';
        $name        = $request->name ?? '';
        $templates   = PluginViduTemplate::query()
            ->with(['category'])
            ->where('status', 1)
            ->when($category_id, function ($query) use ($category_id) {
                $query->where('vidu_template_category_id', $category_id);
            })
            ->when($name, function ($query) use ($name) {
                $query->where('name', 'like', "%{$name}%");
            })
            ->paginate(15);
        return $request->kernel->success(new TemplateCollection($templates));
    }

    public function show(Request $request)
    {
        $request->kernel->validate([
            'template_id' => 'required',
        ], [
            'template_id.required' => '缺少模版id',
        ]);
        $template_id = $request->template_id ?? '';
        $template    = PluginViduTemplate::find($template_id);

        return $request->kernel->success(new TemplateResource($template));
    }
}