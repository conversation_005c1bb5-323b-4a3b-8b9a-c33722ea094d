<?php

namespace App\Http\Controllers\Api\Common;

use App\Http\Controllers\ApiController;
use App\Http\Resources\Resource\SystemResourceResource;
use App\Models\SystemResource;
use Illuminate\Http\Request;

class ImageController extends ApiController
{
    public function backImages(Request $request)
    {
        SystemResource::query()
            ->oldest('key')
            ->oldest('order')
            ->oldest('id')
            ->get()
            ->map(function ($item) use (&$result) {
                $result[$item->key][] = [
                    'name'  => $item->name,
                    'type'  => $item->type,
                    'key'   => $item->key,
                    'url'   => $item->cover_url,
                    'order' => $item->order,
                ];
            });
        return $request->kernel->success(array_values($result));
//        return [
//            'code' => 0,
//            'data' => array_values($result),
//        ];
//        $type = $request->type ?? '';
//        $key  = $request->key ?? '';
//
//        $resources = SystemResource::query()
//            ->when($type, function ($query, $type) {
//                return $query->where('type', $type);
//            })
//            ->when($key, function ($query, $key) {
//                return $query->where('key', $key);
//            })
//            ->oldest('key')
//            ->oldest('type')
//            ->oldest('id')
//            ->get();
//        return $request->kernel->success(SystemResourceResource::collection($resources));
    }
}
