<?php

namespace App\Http\Controllers\Api\Common;

use App\Http\Controllers\ApiController;
use App\Models\SystemConfig;
use Illuminate\Http\Request;

class SystemConfigController extends ApiController
{
    public function companyUserExcel(Request $request)
    {
        $info = SystemConfig::getValue('company_user_template');
        return $request->kernel->success([
            'url' => $info
        ]);
    }

    public function pushSettings(Request $request)
    {
        $data = [
            'identifier'         => SystemConfig::getValue('im_push_identifier'),
            'sdk_appid'          => SystemConfig::getValue('im_push_sdkappid'),
            'client_secret_key'  => SystemConfig::getValue('im_push_client_secretkey'),
            'ios_certificate_id' => SystemConfig::getValue('im_push_ios_certificate_id'),
        ];
        return $request->kernel->success($data);
    }
}
