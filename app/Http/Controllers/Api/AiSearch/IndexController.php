<?php

namespace App\Http\Controllers\Api\AiSearch;

use App\Http\Controllers\Controller;
use App\Models\AppAiSearch;
use App\Models\AppAiSearchGroup;
use App\Models\TokenLog;
use Exception;
use GuzzleHttp\Client;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;

class IndexController extends Controller
{
    private array $defaultData  = [];
    private array $tool         = [];
    private       $appKey       = 'b4897ac2816c45f3f270fd489ea150f4.lPR4mPgPcPTZuuEz';
    private       $url          = "https://open.bigmodel.cn/api/paas/v4/assistant";
    private       $assistant_id = "659e54b1b8006379b4b2abd6";
    private array $errorMsg     = [
        "id"              => "20241112165617067b39a9e4ba4512",
        "created"         => 1731401784125,
        "model"           => "glm-4-assistant",
        "choices"         => [
            [
                "index" => 0,
                "delta" => [
                    "role"     => "assistant",
                    "content"  => "4",
                    "metadata" => []
                ]
            ]
        ],
        "status"          => "in_process",
        "assistant_id"    => "659e54b1b8006379b4b2abd6",
        "conversation_id" => "6733183108437dc2f30c1d10"
    ];

    public function index(Request $request)
    {
        $request->kernel->validate([
            'message'         => 'required',
            'conversation_id' => 'required|integer',
        ], [
            'message.required'         => '请输入要搜索的内容',
            'conversation_id.required' => '请输入会话ID',
            'conversation_id.integer'  => '请输入会话ID',
        ]);
        $this->defaultData = [
            'title'           => $request->message,
            'user_id'         => $request->kernel->id(),
            'conversation_id' => $request->conversation_id,
            'tokens'          => 0,
            'text'            => [],
            'content'         => '',
            'modal'           => 'glm-4-assistant',
            'company_modal'   => 'zhipu'
        ];
        $put               = [
            "assistant_id" => $this->assistant_id,
            "stream"       => true,
            "model"        => "glm-4-assistant",
            "messages"     => [
                [
                    "role"    => "user",
                    "content" => $request->message
                ]
            ]
        ];
        $isChat            = $request->is_chat ? true : false;
        $this->curlStreamRequest($this->url, $put, $request->message, $isChat);
        AppAiSearchGroup::where('user_id', $request->kernel->id())
            ->where('id', $request->conversation_id)
            ->update([
                'updated_at' => now()
            ]);
        die;
    }

    function curlStreamRequest(string $url, $postData, string $messageT, bool $isChat = false)
    {
        ignore_user_abort(true);
        set_time_limit(300);
        session_write_close();
        header('Content-Type: text/event-stream');
        header('Cache-Control: no-cache');
        header('Connection: keep-alive');
        header('X-Accel-Buffering: no');
        ob_implicit_flush(1);

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, false);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, false);
        curl_setopt($ch, CURLOPT_TIMEOUT, 0); // 无限超时
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($postData));
        curl_setopt($ch, CURLOPT_HTTPHEADER,
            ['Content-Type: application/json', 'Accept: text/event-stream', 'Authorization: Bearer '.$this->appKey]);
        curl_setopt($ch, CURLOPT_WRITEFUNCTION, function ($ch, $data) use ($isChat) {
            if ($isChat) {
                $json = substr($data, 5, strlen($data));
                $json = json_decode($json, true);
                if (! empty($json['choices'][0]['finish_reason'])) {
                    $this->defaultData['tokens'] = $json['usage']['total_tokens'] ?? 0;
                } else {
                    if (! empty($json['choices'][0]['delta'])) {
                        $json = $json['choices'][0]['delta'];
                        if ($json['role'] == 'assistant') {
                            echo 'data:'.json_encode([
                                    'type' => 'msg',
                                    'msg'  => $json['content'],
                                    'data' => [],
                                ], JSON_UNESCAPED_UNICODE)."\n";
                        }
                    } else {
                        echo $data."\n";
                    }
                }
            } else {
                echo $data;
            }
            ob_flush();
            flush();
            usleep(20000);
            if (strpos($data, '[DONE]')) {
                $this->defaultData['text'] = $this->tool;
                $item                      = AppAiSearch::create($this->defaultData);
                $map['type']               = empty($data['type']) ?? '';
                $map['uid']                = $this->defaultData['user_id'];
                $map['froms']              = "数字名片";
                $map['source']             = "AI搜索";
                $map['tokens']             = 0;
                $map['mid']                = $item->id;
                $map['app_id']             = $this->defaultData['conversation_id'];
                $map['kb_id']              = 0;
                $map['content']            = json_encode([
                    'question' => $this->defaultData['title'],
                    'answer'   => $this->defaultData['content']
                ]);
                TokenLog::create($map);
            } else {
                $this->add($data);
            }
            return strlen($data);
        });
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 300); // 增加连接超时时间
        curl_setopt($ch, CURLOPT_BUFFERSIZE, 0);       // 调整缓冲区大小
        $response = curl_exec($ch);
        if ($response === false) {
            die('Curl error: '.curl_error($ch));
        }
        curl_close($ch);
    }

    private function add($json)
    {
        $json = substr($json, 5, strlen($json));
        $json = json_decode($json, true);
        if (! empty($json['choices'][0]['finish_reason'])) {
            $this->defaultData['tokens'] = $json['usage']['total_tokens'] ?? 0;
        } else {
            if (! empty($json['choices'][0]['delta'])) {
                $json = $json['choices'][0]['delta'];
                if ($json['role'] == 'tool') {
                    $this->tool[] = $json['tool_calls'][0];
                }
                if ($json['role'] == 'assistant') {
                    $this->defaultData['content'] .= $json['content'];
                }
            }
        }
    }

    public function getHot(Request $request)
    {
        $result = $this->getHotData();

        if ($result['data'] ?? '') {
            return $request->kernel->success([
                'list' => $result
            ]);
        } else {
            return $request->kernel->success([], '没有内容');
        }
    }

    public function getHotData()
    {
        if (Cache::has('ai_hot_search')) {
            return Cache::get('ai_hot_search');
        }
        try {
            $url      = "https://www.doubao.com/samantha/skill/recommend";
            $client   = new Client();
            $response = $client->request('POST', $url, [
                'form_params' => [
                    "omit_image"   => true,
                    "omit_sidebar" => true
                ],
                'verify'      => false,
                'query'       => [
                    "omit_image"          => true,
                    "omit_sidebar"        => true,
                    'version_code'        => 20710,
                    'language'            => 'zh',
                    'device_platform'     => 'web',
                    'aid'                 => 497858,
                    'real_aid'            => 497858,
                    'pkg_type'            => 'release_version',
                    'device_id'           => '',
                    'use-olympus-account' => 1,
                    'region'              => 'CN',
                    'sys_region'          => 'CN',
                    'samantha_web'        => 1,
                    'msToken'             => 'JmpPFXHQ02FFHGtLjC7sZ0KGvV7i_pSlcInth2iGW78zaTQzy0c_rL-glzgnL-8-8BF5s0kXdfjtHyV209t7xMeRjIxXzp9Njq67cIiDGTyZLCLTcrJpx8PSH1OhQDTPfg==',
                    'a_bogus'             => 'EvsOkcg0Msm1xxBPZwkT9rm/4jm0YW-SgZEz1nW1x0q-',
                ],
            ]);
            if ($response->getStatusCode() == 200) {
                $data = json_decode($response->getBody()->getContents(), true);
                Cache::put('ai_hot_search', $data, 60 * 30);
                return $data;
            } else {
                throw new Exception('获取失败');
            }
        } catch (Exception $e) {
            throw new Exception('获取热门搜索失败');
        }
    }

    public function appSearch(Request $request)
    {
        $result = $this->getHotData();
        if ($result['data'] ?? '') {
            $aiSearch = collect(collect($result['data']['onboarding_skills'])->where('skill_type', 4)
                                    ->first()['ai_search']['item_list']);
            $aiSearch->splice(6);
            return $request->kernel->success([
                'list' => $aiSearch
            ]);
        } else {
            return $request->kernel->success([], '没有内容');
        }
    }

    public function getLog(Request $request)
    {
        $user     = $request->kernel->user();
        $pageSize = $request->pageSize ?: 10;

        $lists                = AppAiSearch::where(['user_id' => $user->id])
            ->latest()
            ->paginate($pageSize);
        $data['total']        = $lists->total();
        $data['per_page']     = $lists->perPage();
        $data['current_page'] = $lists->currentPage();
        $data['last_page']    = $lists->lastPage();
        $data['data']         = $lists->map(function ($item) use ($user) {
            return array_merge($item->toArray(), [
                'nickname' => $user->info->nickname,
            ]);
        });

        return $request->kernel->success($data);
    }

    private function returnError(string $message)
    {
        ignore_user_abort(true);
        set_time_limit(300);
        session_write_close();
        header('Content-Type: text/event-stream');
        header('Cache-Control: no-cache');
        header('Connection: keep-alive');
        header('X-Accel-Buffering: no');
        ob_implicit_flush(1);
        $this->errorMsg['choices'][0]['delta']['content'] = $message;
        echo 'data: '.json_encode($this->errorMsg, JSON_UNESCAPED_UNICODE).PHP_EOL.PHP_EOL;
        echo 'data: [DONE]'.PHP_EOL.PHP_EOL;
        ob_flush();
        flush();
        usleep(20000);
        die;
    }

}
