<?php

namespace App\Http\Controllers\Api\Knowledge;

use App\Exceptions\ValidatorException;
use App\Http\Controllers\ApiController;
use App\Http\Requests\Knowledge\AssistantRequest;
use App\Models\Assistant;
use App\Models\Knowledge;
use App\Packages\Knowledge\Knowledge as KnowledgeClient;
use App\Traits\CheckKnowledge;
use Illuminate\Http\Request;

class AssistantController extends ApiController
{
    use CheckKnowledge;

    public function create(Request $request)
    {
        $validate = new AssistantRequest();
        $request->kernel->validate($validate->rules(), $validate->messages());

        $userId = $request->kernel->id();

        $datasetId   = $request->datasetId;
        $description = $request->description;
        $nickname    = $request->nickname;
        $personality = $request->personality;
        $identity    = $request->identity;
        $prologue    = $request->prologue;
        $image       = $request->image;

        $knowledge = [];
        if ($datasetId) {
            $knowledge = Knowledge::where('kb_id', $datasetId)->first();

            $this->checkKnowledge($userId, $knowledge);
        }

        $is_have  = false;
        $assitant = Assistant::query()
            ->where('uid', $userId)
            ->where('is_deleted', 0)
            ->first();

        if ($assitant) {
            $is_have = true;
            $app_id  = $assitant->app_id;
        }

        if (! $is_have) {
            $query  = [
                'description'     => $description,
                'icon'            => '🤖',
                'icon_background' => '#FFEAD5',
                'icon_type'       => 'emoji',
                'mode'            => "chat",
                'name'            => $nickname,
            ];
            $result = KnowledgeClient::client()->apps($query);
            $app_id = $result['id'];
        }

//        $app_id  = "8dc4096f-24dd-4495-8b9c-29d25948845f";
//        $is_have = false;

        $chatQuery                                                                = config('chat');
        $chatQuery['dataset_configs']['datasets']['datasets'][0]['dataset']['id'] = $datasetId ?? '';
        $chatQuery['pre_prompt']                                                  = $prologue;

        $result = KnowledgeClient::client()->modelConfig($app_id, $chatQuery);
        if ($result['result'] != "success") {
            throw new ValidatorException("未知错误");
        }

        if ($is_have) {
            Assistant::where('uid', $userId)
                ->update([
                    'kb_id'       => $datasetId ?? '',
                    'image'       => $image,
                    'company_id'  => ! empty($knowledge) ? $knowledge->company_id : 0,
                    'nickname'    => $nickname,
                    'description' => $description,
                    'personality' => $personality ?? '',
                    'identity'    => $identity ?? '',
                    'prologue'    => $prologue,
                ]);
        } else {
            $assitant = Assistant::create([
                'uid'         => $userId,
                'company_id'  => ! empty($knowledge) ? $knowledge->company_id : 0,
                'image'       => $image,
                'nickname'    => $nickname,
                'description' => $description,
                'personality' => $personality ?? '',
                'identity'    => $identity ?? '',
                'prologue'    => $prologue,
                'kb_id'       => ! empty($datasetId) ? $datasetId : '',
                'app_id'      => $app_id,
                'model_id'    => "qwen-max",
            ]);
        }

        if (! $assitant) {
            throw new ValidatorException("操作失败");
        }

        if (! $assitant->api_key) {
            $result = KnowledgeClient::client()->getApiKey($app_id);
            if (isset($result['token'])) {
                $assitant->api_key = $result['token'];
                $assitant->save();
            }
        }

        return $request->kernel->success([
            'app_id' => $app_id,
        ]);
    }

    public function myAssistant(Request $request)
    {
        $userId = $request->kernel->id();
        $uid    = $request->uid;

        $assistant = Assistant::query()
            ->when($uid && $uid != 0, function ($query) use ($uid) {
                $query->where('uid', $uid)->where('is_open', 0);
            }, function ($query) use ($userId) {
                $query->where('uid', $userId);
            })
            ->where('is_deleted', 0)
            ->first();

        if (! $assistant) {
            return $request->kernel->success([]);
        }

        return $request->kernel->success($assistant->toArray());
    }

    /**
     * Notes: 开关私人助理
     *
     * @Author: 玄尘
     * @Date: 2024/12/3 09:40
     * @param  \Illuminate\Http\Request  $request
     * @return mixed
     * @throws \App\Exceptions\ValidatorException
     */
    public function openAssistant(Request $request)
    {
        $userId    = $request->kernel->id();
        $assistant = Assistant::query()
            ->where('uid', $userId)
            ->where('is_deleted', 0)
            ->first();

        if (! $assistant) {
            throw new ValidatorException("请先创建智能助理");
        }

        $is_open = $assistant['is_open'] ? 0 : 1;

        $assistant->is_open = $is_open;
        $assistant->save();

        return $request->kernel->success([
            'is_open' => $is_open,
        ]);
    }

}
