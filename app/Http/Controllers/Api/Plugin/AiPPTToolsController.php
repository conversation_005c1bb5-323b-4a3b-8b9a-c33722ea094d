<?php

namespace App\Http\Controllers\Api\Plugin;

use App\Http\Controllers\Api\Plugin\Traits\AiPPTConfigTrait;
use App\Http\Controllers\Controller;
use Exception;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;
use Illuminate\Http\Request;

class AiPPTToolsController extends Controller
{
    use AiPPTConfigTrait;

    public function audience(Request $request)
    {
        return $request->kernel->success($this->getConfig('audience'));
    }

    public function scene(Request $request)
    {
        return $request->kernel->success($this->getConfig('scene'));
    }

    public function tone(Request $request)
    {
        return $request->kernel->success($this->getConfig('tone'));
    }

    public function language(Request $request)
    {
        return $request->kernel->success($this->getConfig('language'));
    }

    public function templates(Request $request)
    {
        $client = new Client([
            'verify' => false,
        ]);
        try {
            $response = $client->get('https://res.thisonegpt.com/api/template/list', [
                'query' => [
                    'page'  => $request->page ?: 1,
                    'size'  => $request->pagesize ?: 10,
                    'scene' => $request->scene ?: '',
                ]
            ]);
            if ($response->getStatusCode() == 200) {
                $data = json_decode($response->getBody()->getContents(), true);
                return $request->kernel->success($data['data']);
            } else {
                return $request->kernel->error($response->getReasonPhrase());
            }
        } catch (RequestException $exception) {
            $content = $exception->getResponse()->getBody()->getContents();
            $data    = json_decode($content, true);
            return $request->kernel->error($data['msg']);
        } catch (Exception $e) {
            return $request->kernel->error($e->getMessage());
        }
    }

}