<?php

namespace App\Http\Controllers\Api\Plugin;

use App\Exceptions\ValidatorException;
use App\Http\Controllers\Controller;
use App\Http\Resources\Plugin\Keling\ListCollection;
use App\Models\AiUnifyAsset;
use App\Models\PluginKeLing;
use App\Packages\Plugin\Plugin;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Str;

class KelingController extends Controller
{
    public function recommend_videos(Request $request)
    {
        $result = Plugin::Keling()->recommend_videos();
        return $request->kernel->success($result->toArray(), 'success');
    }

    public function recommendAudio(Request $request)
    {
        $isfirst    = (bool) $request->first;
        $query      = [
            'first'    => $isfirst,
            'duration' => 0
        ];
        $result     = Plugin::Keling()->recommendAudio($query);
        $resultData = [];
        foreach ($result->toArray()['audioList'] as $vo) {
            if (! Str::contains($vo['name'], ['可灵', 'K ling'])) {
                $resultData[] = $vo;
            }
        }
        return $request->kernel->success($resultData, 'success');
    }

    public function firstRecommend(Request $request)
    {
        $count  = $request->count ?: 5;
        $result = Plugin::Keling()->firstRecommend($count);
        if ($result->isError()) {
            return $request->kernel->error($result->getMessage());
        }
        return $request->kernel->success($result->toArray(), 'success');
    }

    public function multRecommend(Request $request)
    {
        $result = Plugin::Keling()->multRecommend();
        if ($result->isError()) {
            return $request->kernel->error($result->getMessage());
        }
        return $request->kernel->success($result->toArray(), 'success');
    }

    public function createKlLibraries(Request $request)
    {
        $result = Plugin::Keling()->libraries($request->count ?: 3);
        return $request->kernel->success($result->toArray(), 'success');
    }

    public function createKlMessageVideos(Request $request)
    {
        $request->kernel->validate([
            'prompt'          => 'required',
            'type'            => 'required',
            'version'         => 'required',
            'negative_prompt' => 'nullable',
            'cfg'             => 'required|numeric',
            'duration'        => 'required|integer',
            'aspect_ratio'    => 'required',
        ], [
            'prompt.required'       => '请输入描述词',
            'version.required'      => '请选择模型版本',
            'type.required'         => '请选择品质',
            'cfg.required'          => '请选择创意想象力/相关性',
            'duration.required'     => '请选择时长',
            'aspect_ratio.required' => '请选择视频比例',
        ]);

        $params = [
            'type'      => $request->type,
            'inputs'    => [],
            'arguments' => [
                ['name' => 'prompt', 'value' => $request->prompt],
                ['name' => 'negative_prompt', 'value' => $request->negative_prompt ?: ''],
                ['name' => 'cfg', 'value' => $request->cfg],
                ['name' => 'duration', 'value' => $request->duration],
                ['name' => 'aspect_ratio', 'value' => $request->aspect_ratio],
                ['name' => 'imageCount', 'value' => '1'],
                ['name' => 'kling_version', 'value' => $request->version],
                [
                    'name'  => 'camera_json',
                    'value' => '{"type":"empty","horizontal":0,"vertical":0,"zoom":0,"tilt":0,"pan":0,"roll":0}'
                ],
                ['name' => 'camera_control_enabled', 'value' => 'false'],
                ['name' => 'biz', 'value' => 'klingai'],
            ]
        ];
        $user   = $request->kernel->user();
        $task   = PluginKeLing::create([
            'channel'  => PluginKeLing::VIDEO,
            'user_id'  => $user->id,
            'prompt'   => $request->prompt,
            'type'     => $request->type,
            'status'   => AiUnifyAsset::STATUS_INIT,
            'params'   => $params,
            'is_asset' => $request->is_asset ? true : false
        ]);
        if ($task) {
            return $request->kernel->success($task->getAssetResource(), 'success');
        } else {
            return $request->kernel->error('创建失败');
        }
    }

    public function create(Request $request)
    {
        $request->kernel->validate([
            'prompt'          => 'required',
            'first_url'       => 'required|url',
            'last_url'        => 'nullable|url',
            'type'            => 'required',
            'version'         => 'required',
            'negative_prompt' => 'nullable',
            'cfg'             => 'required|numeric',
            'duration'        => 'required|integer',
        ], [
            'prompt.required'    => '请输入描述词',
            'first_url.required' => '请上传首帧图片',
            'first_url.url'      => '首帧格式错误',
            'last_url.url'       => '尾帧格式错误',
            'version.required'   => '请选择模型版本',
            'type.required'      => '请选择品质',
            'cfg.required'       => '请选择创意想象力/相关性',
            'duration.required'  => '请选择时长',
        ]);
        $inputs = [
            [
                "inputType" => "URL",
                "url"       => $request->first_url,
                "name"      => "input"
            ]
        ];
        if (! blank($request->last_url)) {
            $inputs[] = [
                "inputType" => "URL",
                "url"       => $request->first_url,
                "name"      => "tail_image"
            ];
        }
        $params = [
            'type'      => $request->type,
            'inputs'    => $inputs,
            'arguments' => [
                ['name' => 'prompt', 'value' => $request->prompt],
                ['name' => 'negative_prompt', 'value' => $request->negative_prompt ?: ''],
                ['name' => 'cfg', 'value' => $request->cfg],
                ['name' => 'duration', 'value' => $request->duration],
                ['name' => 'imageCount', 'value' => '1'],
                ['name' => 'tail_image_enabled', 'value' => blank($request->last_url) ? 'false' : 'true'],
                ['name' => 'kling_version', 'value' => $request->version],
                [
                    'name'  => 'camera_json',
                    'value' => '{"type":"empty","horizontal":0,"vertical":0,"zoom":0,"tilt":0,"pan":0,"roll":0}'
                ],
                ['name' => 'camera_control_enabled', 'value' => 'false'],
                ['name' => 'biz', 'value' => 'klingai'],
            ]
        ];
        $user   = $request->kernel->user();
        $task   = PluginKeLing::create([
            'channel'  => PluginKeLing::VIDEO,
            'user_id'  => $user->id,
            'prompt'   => $request->prompt,
            'type'     => $request->type,
            'params'   => $params,
            'is_asset' => $request->is_asset ? true : false
        ]);
        if ($task) {
            $task['is_asset'] = $request->is_asset ? true : false;

            return $request->kernel->success($task->getAssetResource(), 'success');
        } else {
            return $request->kernel->error('创建失败');
        }
    }

    public function createKLMultVideos(Request $request)
    {
        $request->kernel->validate([
            'prompt'          => 'required',
            'images'          => 'required',
            'type'            => 'required',
            'version'         => 'required',
            'negative_prompt' => 'nullable',
            'duration'        => 'required|integer',
            'aspect_ratio'    => 'required',
        ], [
            'prompt.required'       => '请输入描述词',
            'images.required'       => '请上传最少一张图片',
            'version.required'      => '请选择模型版本',
            'type.required'         => '请选择品质',
            'duration.required'     => '请选择时长',
            'aspect_ratio.required' => '请选择视频比例',
        ]);
        $inputs = [];
        $i      = 0;
        foreach (explode(',', $request->images) as $image) {
            $inputs[] = [
                "inputType" => "URL",
                "url"       => $image,
                "name"      => "ref_img_".$i
            ];
            $inputs[] = [
                "inputType" => "URL",
                "url"       => $image,
                "name"      => "raw_ref_img_".$i
            ];
            $i++;
        }
        $params = [
            'type'      => $request->type,
            'inputs'    => $inputs,
            'arguments' => [
                ['name' => 'prompt', 'value' => $request->prompt],
                ['name' => 'negative_prompt', 'value' => $request->negative_prompt ?: ''],
                ['name' => 'duration', 'value' => $request->duration],
                ['name' => 'imageCount', 'value' => '1'],
                ['name' => 'aspect_ratio', 'value' => $request->aspect_ratio],
                ['name' => 'kling_version', 'value' => $request->version],
                [
                    "name"  => "imageList",
                    "value" => json_encode([
                        ['top' => 0, 'left' => 0, 'width' => 1, 'height' => 1],
                        ['top' => 0, 'left' => 0, 'width' => 1, 'height' => 1],
                        ['top' => 0, 'left' => 0, 'width' => 1, 'height' => 1],
                        ['top' => 0, 'left' => 0, 'width' => 1, 'height' => 1],
                    ]),
                ],
                ['name' => 'biz', 'value' => 'klingai'],
            ]
        ];
        $user   = $request->kernel->user();
        $task   = PluginKeLing::create([
            'channel'  => PluginKeLing::VIDEO,
            'user_id'  => $user->id,
            'prompt'   => $request->prompt,
            'type'     => $request->type,
            'params'   => $params,
            'is_asset' => $request->is_asset ? true : false
        ]);
        if ($task) {
            $task['is_asset'] = $request->is_asset ? true : false;

            return $request->kernel->success($task->getAssetResource(), 'success');
        } else {
            return $request->kernel->error('创建失败');
        }
    }

    public function createKLSpecial(Request $request)
    {
        $request->kernel->validate([
            'image'   => 'required|url',
            'type'    => 'required',
            'version' => 'required',
        ], [
            'image.required'   => '请上传最少一张图片',
            'image.url'        => '图片格式错误',
            'version.required' => '请选择模型版本',
            'type.required'    => '请选择品质',
        ]);
        $params = [
            'type'      => $request->type,
            'inputs'    => [
                [
                    "inputType" => "URL",
                    "url"       => $request->image,
                    "name"      => "input"
                ]
            ],
            'arguments' => [
                ['name' => 'special_effect', 'value' => $request->name],
                ['name' => 'kling_version', 'value' => $request->version],
                ['name' => 'biz', 'value' => 'klingai'],
            ]
        ];
        $user   = $request->kernel->user();
        $task   = PluginKeLing::create([
            'channel'  => PluginKeLing::VIDEO,
            'user_id'  => $user->id,
            'prompt'   => '特效',
            'type'     => $request->type,
            'params'   => $params,
            'is_asset' => $request->is_asset ? true : false
        ]);
        if ($task) {
            $task['is_asset'] = $request->is_asset ? true : false;

            return $request->kernel->success($task->getAssetResource(), 'success');
        } else {
            return $request->kernel->error('创建失败');
        }
    }

    public function createKLShape(Request $request)
    {
        $request->kernel->validate([
            'video' => 'required|url',
            'audio' => 'required|url',
            'type'  => 'required',
        ], [
            'video.required' => '请上传视频',
            'video.url'      => '视频地址格式不正确',
            'audio.required' => '请上传音频',
            'audio.url'      => '音频地址不正确',
            'type.required'  => '请选择品质',
        ]);
        $params = [
            'type'      => $request->type,
            'inputs'    => [
                [
                    "inputType"  => "URL",
                    "url"        => $request->video,
                    "name"       => "video",
                    'fromWorkId' => 0
                ],
                [
                    "inputType"  => "URL",
                    "url"        => $request->audio,
                    "name"       => "audio",
                    'fromWorkId' => 0
                ]
            ],
            'arguments' => [
                ['name' => '__ttsText', 'value' => $request->tts_text ?: ''],
                ['name' => '__ttsTimbre', 'value' => $request->tts_timbre ?: ''],
                ['name' => '__ttsSpeed', 'value' => $request->tts_speed ?: 1],
                ['name' => '__ttsEmotion', 'value' => $request->tts_emotion ?: ''],
                ['name' => 'biz', 'value' => 'klingai'],
            ]
        ];
        $user   = $request->kernel->user();
        $task   = PluginKeLing::create([
            'user_id'  => $user->id,
            'prompt'   => '口型',
            'type'     => $request->type,
            'params'   => $params,
            'is_asset' => $request->is_asset ? true : false
        ]);
        if ($task) {
            return $request->kernel->success($task->getAssetResource(), 'success');
        } else {
            return $request->kernel->error('创建失败');
        }
    }

    public function KlQuery(Request $request)
    {
        $task = PluginKeLing::where('no', $request->no)->first();
        if (! $task) {
            return $request->kernel->error('任务不存在');
        }
        return $request->kernel->success($task->getAssetResource(), 'success');
    }

    public function lists(Request $request)
    {
        $user  = $request->kernel->user();
        $lists = PluginKeLing::ofUser($user)
            ->where('channel', PluginKeLing::VIDEO)
            ->orderByDesc('created_at')
            ->paginate($request->pageSize ?? 10);

        return $request->kernel->success(new ListCollection($lists), 'success');
    }

    public function KlSpecialEffects(Request $request)
    {
        $result = Plugin::Keling()->specialEffects();
        if ($result->isSuccess()) {
            $result = collect($result->toArray())->map(function ($item) {
                if (blank($item['compositedSchema']['preprocess'] ?? '')) {
                    $item['compositedSchema']['preprocess'] = (object) [];
                }
                if (blank($item['compositedSchema']['process'] ?? '')) {
                    $item['compositedSchema']['process'] = (object) [];
                }
                return $item;
            })->values()->toArray();
            return $request->kernel->success($result);
        } else {
            return $request->kernel->error($result->getMessage());
        }
    }

    public function tts(Request $request)
    {
        $request->kernel->validate([
            'text'       => 'required',
            'speaker_id' => 'required',
            'speed'      => 'required'
        ], [
            'text.required'       => '请输入要说的话',
            'speaker_id.required' => '请选择使用的语音',
            'speed.required'      => '请选择语速',
        ]);
        $query = [
            "text"      => $request->text,
            "speakerId" => $request->speaker_id,
            "speed"     => $request->speed,
            "emotion"   => $request->emotion ?? ''
        ];
        if ((float) $request->speed > 2.0 || (float) $request->speed < 0.8) {
            throw new ValidatorException("语速不正确");
        }
        $result = Plugin::Keling()->tts($query);
        return $request->kernel->success($result->toArray(), '请求成功');
    }

    public function ttslist(Request $request)
    {
        $types = [
            "职业", "少男", "少女", "男青年", "女青年", "儿童", "中年", "老人", "方言"
        ];
        $type  = $request->type;
        if (! empty($type)) {
            if (! in_array($type, $types)) {
                throw new ValidatorException("类型不正确");
            }
        }
        array_unshift($types, "全部");
        $key                = 'kl_ttslist_'.md5($type);
        $token              = Cache::get($key, '');
        $query              = [
            'type' => $request->type
        ];
        $data['audio_list'] = $types;
        if (empty($token)) {
            $result = Plugin::Keling()->dsx_tts($query);
            if ($result->isSuccess()) {
                $resultData = $result->toArray();
                Cache::set($key, $resultData['ttsList'], 60 * 60 * 24);
                $data['result'] = $resultData['ttsList'];
            } else {
                throw new ValidatorException($result->getMessage());
            }
        } else {
            $data['result'] = $token;
        }
        return $request->kernel->success($data, '查询成功');
    }

    public function upload(Request $request)
    {
        $request->kernel->validate([
            'fileurl' => 'required',
            'type'    => 'required',
        ], [
            'fileurl.required' => '请输入文件地址',
            'type.required'    => '文件类型'
        ]);
        $fileUrl = $request->fileurl;
        if ($request->lang && $request->short) {
            $fileUrl = $fileUrl.sprintf('?x-oss-process=image/resize,l_%s,s_%s,m_mfit', $request->lang,
                    $request->short);
        }
        $result = Plugin::klHelper()->updateFile($fileUrl, $request->type);
        if ($result['status'] != 200) {
            return $request->kernel->error($result['message']);
        }
        $data = $result['data'];
        return $request->kernel->success($data, 'success');
    }
}
