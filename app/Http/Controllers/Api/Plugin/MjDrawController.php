<?php

namespace App\Http\Controllers\Api\Plugin;

use App\Http\Controllers\Api\Plugin\Traits\MjTrait;
use App\Http\Controllers\Controller;
use App\Models\PluginMjDraw;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class MjDrawController extends Controller
{
    use MjTrait;

    public function changeFace(Request $request)
    {
        $request->kernel->validate([
            'image' => 'required|url',
            'face'  => 'required|url',
            'hr'    => 'required|integer',
        ], [
            'image.required' => '请上传人物图片',
            'image.url'      => '人物图片格式错误',
            'face.required'  => '请上传脸部图片',
            'face.url'       => '脸部图片格式错误',
            'hr.required'    => '请选择品质',
            'hr.integer'     => '品质格式错误',
        ]);
        if ($this->passImage($request->image)) {
            return $request->kernel->error('图片格式错误');
        }
        $user = $request->kernel->user();
        $data = [
            'user_id' => $user->id,
            'type'    => PluginMjDraw::TYPE_PLUGIN_FACE,
            'inputs'  => [
                $request->face,
                $request->image,
            ],
            'params'  => [],
        ];
        return $this->createTask($data, $request);
    }

    public function expansion(Request $request)
    {
        $request->kernel->validate([
            'image'  => 'required|url',
            'params' => 'required',
        ], [
            'image.required'  => '请上传人物图片',
            'image.url'       => '人物图片格式错误',
            'params.required' => '请确认填充大小',
        ]);
        if ($this->passImage($request->image)) {
            return $request->kernel->error('图片格式错误');
        }
        $user     = $request->kernel->user();
        $size     = getimagesize($request->image); // 直接传递流资源
        $width    = $size[0] ?? 0;
        $height   = $size[1] ?? 0;
        $paddings = explode(',', $request->params);
        foreach ($paddings as $key => $padding) {
            $target         = match ($key) {
                0, 2 => $width,
                1, 3 => $height,
            };
            $paddings[$key] = (float) min(bcdiv($padding, $target, 2), 1);
        }
        $data = [
            'user_id' => $user->id,
            'type'    => PluginMjDraw::TYPE_PLUGIN_EXPAND,
            'inputs'  => [$request->image],
            'params'  => [
                'paddings' => $paddings,
            ],
        ];
        return $this->createTask($data, $request);
    }

    public function anime(Request $request)
    {
        $request->kernel->validate([
            'image'    => 'required|url',
            'style_id' => 'required|integer',
        ], [
            'image.required'    => '请上传主图',
            'image.url'         => '主图格式错误',
            'style_id.required' => '请传入风格编号',
            'style_id.integer'  => '风格编号错误',
        ]);
        if ($this->passImage($request->image)) {
            return $request->kernel->error('图片格式错误');
        }
        $style = collect($this->getConfigFormJsonFile('transfer'));

        $style = $style->where('style_id', $request->style_id)->first();
        if (blank($style)) {
            return $request->kernel->error('风格编号错误');
        }
        $user = $request->kernel->user();
        $data = [
            'user_id' => $user->id,
            'type'    => PluginMjDraw::TYPE_PLUGIN_ANIME,
            'inputs'  => [$request->image],
            'params'  => $style,
        ];
        return $this->createTask($data, $request);
    }

    public function lineDraft(Request $request)
    {
        $request->kernel->validate([
            'image'    => 'required|url',
            'style_id' => 'required|integer',
            'steps'    => 'required|integer|min:25|max:35',
            'scale'    => 'required|integer|min:3|max:7',
        ], [
            'image.required'    => '请上传主图',
            'image.url'         => '主图格式错误',
            'style_id.required' => '请选择通用底模',
            'style_id.integer'  => '通用底模参数错误',
            'steps.required'    => '请选择采样步数',
            'steps.integer'     => '采样步数格式错误',
            'steps.min'         => '采样步数最小:min',
            'steps.max'         => '采样步数最大:max',
            'scale.required'    => '请选择提示词相关性',
            'scale.integer'     => '提示词相关性格式错误',
            'scale.min'         => '提示词相关性最小:min',
            'scale.max'         => '提示词相关性最大:max',
        ]);

        if ($this->passImage($request->image)) {
            return $request->kernel->error('图片格式错误');
        }
        $user   = $request->kernel->user();
        $size   = getimagesize($request->image); // 直接传递流资源
        $width  = $size[0] ?? 0;
        $height = $size[1] ?? 0;
        if ($width < 512 || $height < 512) {
            return $request->kernel->error('图片最小边不能小于512px');
        }
        $data = [
            'user_id' => $user->id,
            'type'    => PluginMjDraw::TYPE_PLUGIN_LINEDRAFT,
            'inputs'  => [
                [
                    'key'   => 'extra_image_key',
                    'url'   => $request->image,
                    'scene' => 'image',
                ]
            ],
            'params'  => [
                'image_key_type'   => 1,
                'mark'             => 'lineDraft',
                'template'         => 'mx_advance',
                'style_id'         => (int) $request->style_id,
                'content'          => $request->prompt ?: '',
                'count'            => 1,
                'seed'             => $this->getSeed(),
                'keys'             => ['17', '63'],
                'params'           => [(string) $request->steps, (string) $request->scale, '62'],
                'layout'           => 8,
                'negative_content' => $request->negative_prompt ?: '',
                'lora'             => $request->lora ? collect(explode(',', $request->lora))->map(function ($item) {
                    $value = explode('|', $item);
                    return [
                        'id'       => (int) $value[0],
                        'strength' => $value[1],
                    ];
                }) : [],
                'control_net'      => $request->control_net ? collect(explode(',',
                    $request->control_net))->map(function ($item) {
                    $value = explode('|', $item);
                    return [
                        'id'       => (int) $value[0],
                        'strength' => $value[1],
                    ];
                }) : [],
            ],
        ];
        return $this->createTask($data, $request);
    }

    public function artisticFont(Request $request)
    {
        $request->kernel->validate([
            'image'    => 'required|url',
            'style_id' => 'required|integer',
            'prompt'   => 'required',
        ], [
            'image.required'    => '请上传主图',
            'image.url'         => '主图格式错误',
            'style_id.required' => '请选择风格',
            'style_id.integer'  => '风格参数错误',
            'prompt.required'   => '请输入描述文字',
        ]);
        if ($this->passImage($request->image)) {
            return $request->kernel->error('图片格式错误');
        }
        $user = $request->kernel->user();
        $data = [
            'user_id' => $user->id,
            'type'    => PluginMjDraw::TYPE_PLUGIN_ARTISTICFONT,
            'inputs'  => [
                [
                    'key'   => 'extra_image_key',
                    'url'   => $request->image,
                    'scene' => 'image',
                ]
            ],
            'params'  => [
                'image_key_type' => 1,
                'template'       => 'artistic_font',
                'style_id'       => (int) $request->style_id,
                'content'        => $request->prompt ?: '',
                'count'          => 1,
                'seed'           => $this->getSeed(),
                'keys'           => ['40'],
                'layout'         => 8,
            ],
        ];
        return $this->createTask($data, $request);
    }

    public function qrcode(Request $request)
    {
        $request->kernel->validate([
            'image'    => 'required|url',
            'prompt'   => 'required',
            'strength' => 'required|numeric|between:1,2',
        ], [
            'image.required'    => '请上传主图',
            'image.url'         => '主图格式错误',
            'prompt.required'   => '请输入描述文字',
            'strength.required' => '请选择二维透明度',
            'strength.numeric'  => '二维码透明度格式错误',
            'strength.between'  => '二维码透明度在:min~:max之间',
        ]);
        if ($this->passImage($request->image)) {
            return $request->kernel->error('图片格式错误');
        }
        $user = $request->kernel->user();
        $data = [
            'user_id' => $user->id,
            'type'    => PluginMjDraw::TYPE_PLUGIN_QRCODE,
            'inputs'  => [
                [
                    'key'   => 'extra_image_key',
                    'url'   => $request->image,
                    'scene' => 'image',
                ]
            ],
            'params'  => [
                'image_key_type' => 1,
                'template'       => 'artistic_qr',
                'count'          => 1,
                'keys'           => ["40"],
                'seed'           => $this->getSeed(),
                'layout'         => 8,
                'params'         => [(string) $request->strength ?: '1.5'],
                'content'        => $request->prompt ?: '',
                'style_id'       => 0,
            ]
        ];
        return $this->createTask($data, $request);
    }

    public function mjBlend(Request $request)
    {
        $request->kernel->validate([
            'image' => 'required',
        ], [
            'image.required' => '请上传主图',
        ]);

        $user       = $request->kernel->user();
        $imageArray = explode(',', $request->image);
        if (count($imageArray) < 2 || count($imageArray) > 4) {
            return $request->kernel->error('图片数量不正确，请在2-4张图片');
        }
        foreach ($imageArray as $image) {
            if (! Str::isUrl($image)) {
                return $request->kernel->error('图片地址存在异常');
            }
            if ($this->passImage($image)) {
                return $request->kernel->error('图片格式错误');
            }
        }
        $data = [
            'user_id' => $user->id,
            'type'    => PluginMjDraw::TYPE_PLUGIN_BLEND,
            'inputs'  => [
                [
                    'key'   => 'extra_image_key',
                    'url'   => $imageArray,
                    'scene' => 'image',
                ]
            ],
            'params'  => [
                'image_key_type' => 1,
                'fl'             => 1,
                'layout'         => 8,
            ]
        ];
        return $this->createTask($data, $request);
    }

    public function transfer(Request $request)
    {
        $request->kernel->validate([
            'image'    => 'required|url',
            'style_id' => 'required|integer',
        ], [
            'image.required'    => '请上传主图',
            'image.url'         => '主图格式错误',
            'style_id.required' => '请传入风格编号',
            'style_id.integer'  => '风格编号错误',
        ]);
        if ($this->passImage($request->image)) {
            return $request->kernel->error('图片格式错误');
        }
        $style = collect($this->getConfigFormJsonFile('transfer'));
        $style = $style->where('style_id', $request->style_id)->first();
        if (blank($style)) {
            return $request->kernel->error('风格编号错误');
        }

        $user = $request->kernel->user();
        $data = [
            'user_id' => $user->id,
            'type'    => PluginMjDraw::TYPE_PLUGIN_TRANSFER,
            'inputs'  => [$request->image],
            'params'  => $style,
        ];
        return $this->createTask($data, $request);
    }

    public function mirror(Request $request)
    {
        $request->kernel->validate([
            'image'  => 'required',
            'prompt' => 'required',
        ], [
            'image.required'  => '请上传主图',
            'prompt.required' => '请输入描述内容',
        ]);

        $imageArray = explode(',', $request->image);
        if (count($imageArray) < 2 || count($imageArray) > 5) {
            return $request->kernel->error('图片数量不正确，请在2-5张图片');
        }
        foreach ($imageArray as $image) {
            if (! Str::isUrl($image)) {
                return $request->kernel->error('图片地址存在异常');
            }
            if ($this->passImage($image)) {
                return $request->kernel->error('图片格式错误');
            }
        }
        $user = $request->kernel->user();
        $data = [
            'user_id' => $user->id,
            'type'    => PluginMjDraw::TYPE_PLUGIN_MIRROR,
            'inputs'  => [
                [
                    'key'   => 'head_image_keys',
                    'url'   => $imageArray,
                    'scene' => 'image',
                ]
            ],
            'params'  => [
                'content'  => $request->prompt ?: '',
                'layout'   => 8,
                'count'    => 1,
                'style_id' => 0,
                'keys'     => ['50'],
                'template' => 'artistic_photo',
                'seed'     => $this->getSeed(),
                'params'   => ["512", "768"],
            ],
        ];
        return $this->createTask($data, $request);
    }

    public function query(Request $request)
    {
        $task = PluginMjDraw::where('no', $request->no)->first();
        if (! $task) {
            return $request->kernel->error('任务不存在');
        }
        return $request->kernel->success($task->getAssetResource(), 'success');
    }
}