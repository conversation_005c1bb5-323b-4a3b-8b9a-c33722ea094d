<?php

namespace App\Http\Controllers\Api\Prompts;

use App\Http\Controllers\Controller;
use App\MCP\Traits\OpenAiToolClient;

class BasePromptController extends Controller
{
    protected function doChat($system, $prompt)
    {
        $openAi = new OpenAiToolClient('sk-6625f866cdcd41feb700ae01715e75df',
            'https://dashscope.aliyuncs.com/compatible-mode/v1');
        $result = $openAi->chat('qwen-plus-latest', $system, $prompt, 8000, [
            'enable_search'  => true,
            'search_options' => [
                'forced_search'   => true,
                'search_strategy' => 'pro',
            ],
        ]);
        return $result;
    }
}