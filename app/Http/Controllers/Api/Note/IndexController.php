<?php

namespace App\Http\Controllers\Api\Note;

use App\Http\Controllers\Controller;
use App\Http\Requests\Note\AddNoteRequest;
use App\Http\Requests\Note\NoteSetTagRequest;
use App\Http\Requests\Note\UpdateNoteRequest;
use App\Http\Resources\Note\NoteBaseResource;
use App\Http\Resources\Note\NoteCollection;
use App\Http\Resources\Note\NoteResource;
use App\Models\Note;
use App\Models\NoteTag;
use App\Packages\FastGpt\FastGpt;
use Cache;
use Illuminate\Http\Request;
use Modules\Interaction\Models\Browse;

class IndexController extends Controller
{
    public function index(request $request)
    {
        $user        = $request->kernel->user();
        $tag_id      = $request->tag_id;     // all  no_tag 无标签
        $is_archived = $request->is_archived;//是否归档
        $orderBy     = $request->order_by ?? 'created_at';
        $sortType    = $request->sort_type ?? 'desc';

        $query = Note::query()
            ->where('type', Note::TYPE_TEXT)
            ->where('user_id', $user->id)
            ->when($tag_id, function ($query) use ($tag_id) {
                if ($tag_id === 'no_tag') {
                    $query->whereDoesntHave('tags');
                } elseif ($tag_id !== 'all') {
                    $query->whereHas('tags', function ($query) use ($tag_id) {
                        $query->where('note_tags.id', $tag_id);
                    });
                }
            })
            ->whereDoesntHave('knowledgeSet')
            ->when(is_numeric($is_archived), function ($query) use ($is_archived) {
                $query->where('is_archived', $is_archived);
            })
            ->orderBy($orderBy, $sortType);

        $tops  = (clone $query)
            ->where('type', Note::TYPE_TEXT)
            ->where('is_top', 1)
            ->get();
        $notes = (clone $query)->where('is_top', '<>', 1)->paginate();
        return $request->kernel->success([
            'tops'  => NoteBaseResource::collection($tops),
            'notes' => new NoteCollection($notes),
        ]);
    }

    public function list(Request $request)
    {
        $title = $request->title;
        $notes = Note::query()
            ->where('type', Note::TYPE_TEXT)
            ->where('user_id', $request->kernel->user()->id)
            ->when($title, function ($query) use ($title) {
                $query->where('title', 'like', '%'.$title.'%')
                    ->orWhere('content', 'like', '%'.$title.'%');
            })
            ->orderByDesc('created_at')
            ->paginate();

        return $request->kernel->success(new NoteCollection($notes));
    }

    /**
     * Notes: 添加
     *
     * @Author: 玄尘
     * @Date: 2025/2/25 15:47
     * @param  \App\Http\Requests\Note\AddNoteRequest  $request
     * @return mixed
     */
    public function store(AddNoteRequest $request)
    {
        try {
            $user    = $request->kernel->user();
            $details = $request->details;
            $type    = $request->type ?? 1;
            $note    = Note::create([
                'user_id' => $user->id,
                'content' => $details,
                'is_top'  => 0,
                'type'    => $type,
            ]);

            if ($request->filled('tag_ids')) {
                $tagIds = explode(',', $request->tag_ids);

                // 验证 tag_ids 是否都存在于 note_tags 表中
                $existingTagIds = NoteTag::whereIn('id', $tagIds)->pluck('id')->toArray();

                // 如果有任何 tag_id 不存在，抛出一个异常或者返回一个错误
                $invalidTagIds = array_diff($tagIds, $existingTagIds);

                if (! empty($invalidTagIds)) {
                    return $request->kernel->error('以下标签 ID 不存在: '.implode(',', $invalidTagIds));
                }

                $note->tags()->sync($existingTagIds);
            }
            return $request->kernel->success('添加成功');
        } catch (\Exception $exception) {
            return $request->kernel->error($exception->getMessage());
        }
    }

    /**
     * Notes: 编辑
     *
     * @Author: 玄尘
     * @Date: 2025/2/25 15:51
     * @param  \App\Http\Requests\Note\UpdateNoteRequest  $request
     * @return mixed
     */
    public function update(UpdateNoteRequest $request)
    {
        try {
            $details = $request->details;
            $tag_ids = $request->tag_ids;
            $note_id = $request->note_id;
            $type    = $request->type;

            $note = Note::findOrFail($note_id);
            if ($type) {
                $note->type = $type;
            }
            $note->content = $details;
            $note->save();

            if ($tag_ids) {
                $tagIds = explode(',', $tag_ids);

                // 验证 tag_ids 是否都存在于 note_tags 表中
                $existingTagIds = NoteTag::whereIn('id', $tagIds)->pluck('id')->toArray();

                // 如果有任何 tag_id 不存在，抛出一个异常或者返回一个错误
                $invalidTagIds = array_diff($tagIds, $existingTagIds);

                if (! empty($invalidTagIds)) {
                    return $request->kernel->error('以下标签 ID 不存在: '.implode(',', $invalidTagIds));
                }

                $note->tags()->sync($existingTagIds);
            } else {
                $note->tags()->detach();
            }
            return $request->kernel->success('编辑成功');
        } catch (\Exception $exception) {
            return $request->kernel->error($exception->getMessage());
        }
    }

    /**
     * Notes: 设置标签
     *
     * @Author: 玄尘
     * @Date: 2025/2/25 16:58
     * @param  \Illuminate\Http\Request  $request
     * @return mixed
     */
    public function setTag(NoteSetTagRequest $request)
    {
        $note_id = $request->note_id;
        $note    = Note::findOrFail($note_id);

        if ($request->filled('tag_ids')) {
            $tagIds = explode(',', $request->tag_ids);

            // 验证 tag_ids 是否都存在于 note_tags 表中
            $existingTagIds = NoteTag::whereIn('id', $tagIds)->pluck('id')->toArray();

            // 如果有任何 tag_id 不存在，抛出一个异常或者返回一个错误
            $invalidTagIds = array_diff($tagIds, $existingTagIds);

            if (! empty($invalidTagIds)) {
                return $request->kernel->error('以下标签 ID 不存在: '.implode(',', $invalidTagIds));
            }

            $note->tags()->sync($existingTagIds);
        } else {
            $note->tags()->detach();
        }

        return $request->kernel->success('设置标签成功');
    }

    public function show(Request $request)
    {
        $request->kernel->validate([
            'note_id' => 'required|exists:notes,id',
        ], [
            'note_id.required' => '缺少小记id',
            'note_id.exists'   => '小记不存在',
        ]);

        $note = Note::find($request->note_id);
        //增加浏览量
        $note->increment('browse_count');
        Browse::create([
            'user_id'        => $request->kernel->id(),
            'browsable_type' => $note->getMorphClass(),
            'browsable_id'   => $note->id,
        ]);
        return $request->kernel->success(new NoteResource($note));
    }

    /**
     * Notes: 删除小记
     *
     * @Author: 玄尘
     * @Date: 2025/2/26 13:41
     * @param  \Illuminate\Http\Request  $request
     * @return mixed
     */
    public function delete(Request $request)
    {
        $note_id = $request->note_id;
        if (! $note_id) {
            return $request->kernel->error('缺少note_id');
        }
        $note = Note::find($note_id);
        if (! $note) {
            return $request->kernel->error('小记不存在');
        }

        //删除知识库合集
        $knowledgeSet = $note->knowledgeSet;
        if ($knowledgeSet) {
            if ($knowledgeSet->collection_id) {
                $result = FastGpt::set()->deleteSet($knowledgeSet->collection_id);
            }
            $knowledgeSet->delete();
        }

        $note->delete();
        return $request->kernel->success('删除成功');
    }

    /**
     * 保存日记草稿
     *
     * @param  Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function cacheSave(Request $request)
    {
        $request->validate([
            'details' => 'required|string', // 草稿内容
        ], [
            'details.required' => '内容不能为空',
            'details.string'   => '内容必须是字符串',
        ]);

        $userId  = $request->kernel->id();
        $details = $request->details;

        // 存储具体草稿内容，应该是个列表
        $draftsListKey = "user:{$userId}:diary:drafts";
        $list          = Cache::get($draftsListKey, []);
        $list[]        = [
            'details' => $details,
            'time'    => now()->format('Y-m-d H:i:s')
        ];

        Cache::put($draftsListKey, $list, now()->addDays(7)); // 7天过期

        return $request->kernel->success('保存成功');
    }

    /**
     * 获取日记草稿
     *
     * @param  Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function cacheGet(Request $request)
    {
        $userId        = $request->kernel->id();
        $draftsListKey = "user:{$userId}:diary:drafts";
        $list          = Cache::get($draftsListKey, []);

        return $request->kernel->success($list);
    }

    public function archive(Request $request)
    {
        $request->validate([
            'note_id' => 'required|exists:notes,id',
        ], [
            'note_id.required' => '请传入要归档的小记',
            'note_id.exists'   => '小记不存在',
        ]);

        $note              = Note::find($request->note_id);
        $note->is_archived = ! $note->is_archive;
        $note->save();

        return $request->kernel->success('设置成功');
    }

    public function setTop(Request $request)
    {
        $request->validate([
            'note_id' => 'required|exists:notes,id',
        ], [
            'note_id.required' => '缺少note_id',
            'note_id.exists'   => '小记不存在',
        ]);

        $note         = Note::find($request->note_id);
        $note->is_top = ! $note->is_top;
        $note->save();

        return $request->kernel->success([
            'is_top' => $note->is_top
        ]);
    }

    /**
     * Notes: 修改标题
     *
     * @Author: 玄尘
     * @Date: 2025/3/25 20:38
     * @param  \Illuminate\Http\Request  $request
     * @return mixed
     */
    public function updateTitle(Request $request)
    {
        $request->validate([
            'note_id' => 'required|exists:notes,id',
            'title'   => 'required|string',
        ], [
            'title.required'   => '缺少标题',
            'note_id.required' => '缺少小记id',
            'note_id.exists'   => '小记不存在',
        ]);

        $title   = $request->title;
        $note_id = $request->note_id;

        $note        = Note::find($note_id);
        $note->title = $title;
        $note->save();
        return $request->kernel->success('修改标题成功');
    }

}
