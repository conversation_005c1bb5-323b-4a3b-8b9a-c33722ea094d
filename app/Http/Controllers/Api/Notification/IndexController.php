<?php

namespace App\Http\Controllers\Api\Notification;

use App\Http\Controllers\ApiController;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Notifications\DatabaseNotification;
use Modules\Notification\Http\Resources\NotificationCollection;

class IndexController extends ApiController
{

    /**
     * Notes   : 消息列表
     *
     * @Date   : 2023/8/10 10:35
     * <AUTHOR> <Jason.C>
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        $user    = $request->kernel->user();
        $type    = $request->type ?? '';
        $is_read = $request->is_read ?? '';

        $resource = $user->notifications()
            ->whereIn('type', [
                'App\Notifications\UserCommentLikeNotice',
                'App\Notifications\UserCommentReplyNotice',
                'App\Notifications\UserCommentUnifyAssetNotice',
                'App\Notifications\UserFavoriteUnifyAssetNotice',
                'App\Notifications\UserFollowNotice',
                'App\Notifications\UserLikeUnifyAssetNotice',
                'App\Notifications\PluginJmDrawSuccessNotification',
                'App\Notifications\PluginJmDrawErrorNotification',
            ])
            ->select(['id', 'type', 'data', 'read_at', 'created_at'])
            ->when($type, function (Builder $builder, $type) {
                $builder->where('type', $type);
            })
            ->when($is_read, function (Builder $builder, $is_read) {
                if ($is_read == 1) {
                    $builder->whereNotNull('read_at');
                } else {
                    $builder->whereNull('read_at');
                }
            })
            ->latest('id')
            ->paginate($request->pageSize ?? 10);

        return $this->success(new NotificationCollection($resource));
    }

    public function markAsRead(Request $request): JsonResponse
    {
        $id   = $request->id;
        $info = DatabaseNotification::find($id);
        if (! $info) {
            return $request->kernel->error('信息不存在');
        }

        $user = $request->kernel->user();
        if ($info->notifiable->isNot($user)) {
            return $request->kernel->error('您没有权限操作该信息');
        }
        $info->markAsRead();

        return $this->success();
    }

    /**
     * Notes: 标记全部已读
     *
     * @Author: 玄尘
     * @Date: 2025/4/7 11:16
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function maskAllAsRead(Request $request): JsonResponse
    {
        $user = $request->kernel->user();

        $notifications = $user->unreadNotifications()
            ->when($request->type, function (Builder $builder, $type) {
                $builder->where('type', $type);
            })
            ->get();
        $notifications->each->markAsRead();

        return $this->success('操作成功');
    }

    public function maskAsReadById(Request $request)
    {
        $notification_ids = $request->notification_ids;
        $user             = $request->kernel->user();

        if (! $notification_ids) {
            return $request->kernel->error('缺少id');
        }
        if (! is_array($notification_ids)) {
            $notification_ids = explode(',', $notification_ids);
        }
        $notifications = $user->unreadNotifications()
            ->whereIn('id', $notification_ids)
            ->get();

        $notifications->each->markAsRead();

        return $this->success();
    }

    public function count(Request $request): JsonResponse
    {
        $user = $request->kernel->user();

        return $this->success([
            'total'  => $user->notifications()
                ->whereNotIn('type', ['Modules\User\Notifications\IdentityChangedNotification'])
                ->when($request->type, function (Builder $builder, $type) {
                    $builder->where('type', $type);
                })->count(),
            'unread' => $user->unreadNotifications()
                ->whereNotIn('type', ['Modules\User\Notifications\IdentityChangedNotification'])
                ->when($request->type, function (Builder $builder, $type) {
                    $builder->where('type', $type);
                })->count(),
        ]);
    }

}
