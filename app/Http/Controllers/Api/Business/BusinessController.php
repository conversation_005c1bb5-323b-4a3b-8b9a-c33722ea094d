<?php

namespace App\Http\Controllers\Api\Business;

use App\Exceptions\ValidatorException;
use App\Http\Controllers\ApiController;
use App\Http\Requests\Business\AddBusinessRequest;
use App\Http\Resources\Resource\ResourceResource;
use App\Models\BrowseRecord;
use App\Models\Business;
use App\Models\Company;
use App\Models\CompanyUser;
use App\Models\Contact;
use App\Models\Realname;
use App\Models\Resources;
use App\Models\User;
use App\Models\UserPrivacy;
use App\Traits\BusinessTrait;
use Carbon\Carbon;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Storage;
use Modules\Interaction\Models\Like;
use Modules\User\Models\UserInfo;

class BusinessController extends ApiController
{
    use BusinessTrait;

    public function my(Request $request)
    {
        $type = $request->type ?? 0;

        $user = $request->kernel->user();

        //检测是否实名认证
        $isRealname = $user->realName()->where('is_check', 1)->exists();

        $businesses = Business::where('uid', $user->id)
            ->when($type == 1, function ($query) {
                return $query->where('company_id', '>', 0);
            })
            ->latest()
            ->get();

        $companyName = [];
        foreach ($businesses as &$business) {
            if (in_array($business->company_name, $companyName)) {
                continue;
            } else {
                $companyName[] = $business->company_name;
            }

            $business->is_realname = 0;
            if ($isRealname) {
                $business->is_realname = 1;
            }

            $business->is_manage = 0;
            if ($business->is_work == 1) {
                $business->is_manage = $user->isManagementRole($business->company_id);
            }
        }
        return $request->kernel->success($businesses);
    }

    public function add(Request $request)
    {
        $validate = new AddBusinessRequest();
        $request->kernel->validate($validate->rules(), $validate->messages());

        $user = $request->kernel->user();
//        $department = $user->department;
//        if (! $department) {
//            throw new ValidatorException('您还没有加入部门');
//        }

        $business      = $request->business;//业务介绍 business_type不为0时候 传数组
        $business_type = $request->business_type;
        $company       = $request->company;
        $phone         = $request->phone;
        $company_name  = $request->company_name;
        $company_type  = $request->company_type;
        $avatar        = $request->avatar;
        $position      = $request->position;
        $bid           = $request->bid ?? '';

        if ($company_type != 0) {
            $company = json_encode($company);
        }
        if ($business_type != 0) {
            $business = json_encode($business);
        }

        $info = [
            'phone'              => $request->phone,
            'nickname'           => $request->nickname,
            'business'           => $business,
            'business_type'      => $business_type,
            'company'            => $company,
            'company_name'       => $company_name,
            'company_type'       => $company_type,
            'avatar'             => $avatar ?? $user->info->avatar_url,
            'nickname_show'      => $request->nickname_show ?? 1,
            'email'              => $request->email ?? '',
            'email_show'         => $request->email_show ?? 0,
            'email_color'        => $request->email_color ?? null,
            'email_bold'         => $request->email_bold ?? 0,
            'email_size'         => $request->email_size ?? null,
            'company_icon'       => $request->company_icon ?? '',
            'company_icon_show'  => $request->company_icon_show ?? 1,
            'company_name_show'  => $request->company_name_show ?? 1,
            'company_name_size'  => $request->company_name_size ?? null,
            'company_name_bold'  => $request->company_name_bold ?? 0,
            'company_name_color' => $request->company_name_color ?? '',
            'industry'           => $request->industry ?? '',
            'avatar_show'        => $request->avatar_show ?? 1,
            'wechat'             => $request->wechat ?? '',
            'address'            => $request->address ?? '',
            'address_show'       => $request->address_show ?? 0,
            'address_bold'       => $request->address_bold ?? 0,
            'address_color'      => $request->address_color ?? null,
            'address_size'       => $request->address_size ?? null,
            'introduction'       => $request->introduction ?? '',
            'annex'              => $request->annex ?? '',
            'phone_show'         => $request->phone_show ?? 1,
            'phone_size'         => $request->phone_size ?? null,
            'phone_bold'         => $request->phone_bold ?? 0,
            'phone_color'        => $request->phone_color ?? '',
            'nickname_size'      => $request->nickname_size ?? null,
            'nickname_bold'      => $request->nickname_bold ?? 0,
            'nickname_color'     => $request->nickname_color ?? '',
            'position'           => $position,
            'position_show'      => $request->position_show ?? 1,
            'position_bold'      => $request->position_bold ?? 0,
            'position_color'     => $request->position_color ?? '',
            'position_size'      => $request->position_size ?? 12,
            'wiki'               => $request->wiki ?? '',
            'wiki_bold'          => $request->wiki_bold ?? 0,
            'wiki_color'         => $request->wiki_color ?? null,
            'wiki_size'          => $request->wiki_size ?? null,
            'background'         => $request->background ?? '',
            'layout'             => $request->layout ?? '',
            'annex_name'         => $request->annex_name ?? '',
        ];

        DB::beginTransaction();
        try {
            if ($bid) {
                $business = Business::find($bid);
                if (! $business) {
                    throw new ValidatorException("要修改的名片不存在");
                }

                if ($business->uid != $user->id) {
                    throw new ValidatorException("您没有权限修改此名片");
                }

                $business->update($info);
            } else {
                $companyInfo = Company::where('company_name', $company_name)->first();
                if ($companyInfo && $business) {
                    $companyUser = CompanyUser::where('company_id', $companyInfo->id)
                        ->whereHas('user', function ($query) use ($phone) {
                            $query->where('username', $phone);
                        })
                        ->first();

                    if ($companyUser) {
                        $info['company_id'] = $companyInfo->id;
                        $info['is_company'] = $companyInfo->is_check;//是否认证
                    }
                }
                $defaultBusiness = Business::where('uid', $user->id)
                    ->where('is_default', 1)
                    ->first();

                $info['uid'] = $user->id;
                if (! $defaultBusiness) {
                    $info['is_default'] = 1;
                }

                $business = Business::create($info);
            }

            $companyUser = CompanyUser::where('uid', $user->id)
                ->where('bid', 0)
                ->where('is_deleted', 0)
                ->first();

            if ($companyUser && $companyUser->position == $info['position']) {
                $company = Company::where('id', $companyUser->company_id)
                    ->where('is_check', 1)
                    ->first();

                if ($company && $company->company_name == $info['company_name']) {
                    CompanyUser::where('uid', $user->id)
                        ->where('bid', 0)
                        ->where('is_deleted', 0)
                        ->update([
                            'bid' => $business->bid,
                        ]);
                }
            }

            if (! $business) {
                DB::rollBack();  //回滚事务
                throw new ValidatorException($bid ? "名片编辑失败" : "名片创建失败");
            }
            DB::commit();  //提交事务
            return $request->kernel->success(true);
        } catch (ValidatorException $e) {
            DB::rollBack();  // 回滚事务
            throw new ValidatorException($bid ? "名片编辑失败" : "名片创建失败");
        }
    }

    public function delete(Request $request)
    {
        $request->kernel->validate([
            'bid' => 'required',
        ], [
            'bid.required' => '名片id不可为空'
        ]);
        $user = $request->kernel->user();
        $bid  = $request->bid;

        $business = Business::find($bid);

        if (! $business) {
            throw new ValidatorException("名片不存在");
        }

        if ($business->uid != $user->id) {
            throw new ValidatorException("只能删除自己的名片");
        }

        $res = $business->delete();
        CompanyUser::where('bid', $business->id)->where('is_check', 0)->delete();
        if ($res) {
            return $request->kernel->success(true);
        }

        throw new ValidatorException("删除失败");
    }

    public function switch(Request $request)
    {
        $request->kernel->validate([
            'bid' => 'required',
        ], [
            'bid.required' => '名片id不可为空'
        ]);
        $user = $request->kernel->user();
        $bid  = $request->bid;

        $business = Business::find($bid);

        if (! $business) {
            throw new ValidatorException("未查询到该名片");
        }

        if ($business->uid != $user->id) {
            throw new ValidatorException("只能切换自己的名片");
        }

        try {
            Business::where('uid', $business->uid)->update([
                'is_default' => 0
            ]);
            $business->is_default = 1;
            $business->save();
            return $request->kernel->success(true);
        } catch (ValidatorException $e) {
            throw new ValidatorException($e->getMessage());
        }
    }

    public function copy(Request $request)
    {
        $request->kernel->validate([
            'bid' => 'required',
        ], [
            'bid.required' => '缺少名片id'
        ]);

        $user = $request->kernel->user();
        $bid  = $request->bid;

        $business = Business::find($bid);

        if (! $business) {
            throw new ValidatorException("未查询到该名片");
        }

        if ($business->uid != $user->id) {
            throw new ValidatorException("您没有权限复制此名片");
        }

        $newBusiness             = $business->replicate();
        $newBusiness->is_default = 0;
        $newBusiness->is_work    = 0;
        $newBusiness->company_id = 0;
        $newBusiness->is_company = 0;
        $newBusiness->save();

        if ($newBusiness) {
            return $request->kernel->success($newBusiness);
        }

        throw new ValidatorException("复制失败");
    }

    public function resources(Request $request)
    {
        $result = Resources::OfEnabled()
            ->where('exp_time', '>', now()->toDateTimeString())
            ->get();

        return $request->kernel->success(ResourceResource::collection($result));
    }

    public function detail(Request $request)
    {
        $request->kernel->validate([
            'bid' => 'required',
        ], [
            'bid.required' => '缺少名片id'
        ]);

        $user = $request->kernel->user();
        $bid  = $request->bid;

        $business = Business::find($bid);

        if (! $business) {
            throw new ValidatorException("名片不存在", 404);
        }

        if ($business->uid != $user->id) {
            throw new ValidatorException("只能查看自己的名片");
        }

//        $realname = Realname::where('uid', $user->id)
//            ->where('is_check', 1)
//            ->first();
//
//        if ($realname) {
//            $business->company_type = 1;
//        } else {
//            $business->business_type = 0;
//        }

//        if ($business->is_work == 1) {
//            $company = Company::where('id', $business->company_id)
//                ->where('is_check', 1)
//                ->first();
//
//            $business->company_type  = $company->company_type;
//            $business->business_type = $company->business_type;
//            $business->company       = $company->company;
//            $business->business      = $company->business;
//        }
        $business->company  = $business->getCompany();
        $business->business = $business->getBusiness();
        return $request->kernel->success($business);
    }

    public function send(Request $request)
    {
        $request->kernel->validate([
            'bid' => 'required',
        ], [
            'bid.required' => '缺少名片id',
        ]);

        $user = $request->kernel->user();
        $bid  = $request->bid;
        $num  = $request->num;

        $business = Business::find($bid);

        if (! $business) {
            throw new ValidatorException("名片不存在");
        }

        if ($business->uid != $user->id) {
            throw new ValidatorException("只能发送自己的名片");
        }

        $num = isset($num) ? (int) $num : 1;

        $business->num = $business->num + $num;
        $business->save();

        return $request->kernel->success([
            'send_num' => $business->num
        ]);
    }

    public function browse(Request $request)
    {
        $request->kernel->validate([
            'bid' => 'required',
        ], [
            'bid.required' => '缺少名片id',
        ]);

        $userId = $request->kernel->id();

        $bid  = $request->bid;
        $pass = $request->pass ?? '';

        $business = Business::find($bid);
        if (! $business) {
            throw new ValidatorException("名片不存在");
        }

        //查看是否有浏览记录
        if ($business->uid != $userId) {
            $browse = BrowseRecord::where('uid', $userId)
                ->where('bid', $bid)
                ->first();

            if (! $browse) {
                BrowseRecord::create([
                    'bid' => $bid,
                    'uid' => $userId,
                ]);
            } else {
                $browse->count += 1;
                $browse->save();
            }
        }

        $realname = Realname::where('uid', $business->uid)->first();

        $business->is_realname = 0;
        if (! empty($realname) && $realname->is_check == 1) {
            $business->is_realname = 1;
        }

        //获取点赞前5人的头像
        $ids = Like::OfItem($business)->take(5)->pluck('user_id');

        //对隐藏数据进行处理
        $data = data_processing($business->toArray());

        $data['position'] = $business->position;
        $data['uid']      = $business->uid;

        if ($business->is_work == 1) {
            $company = Company::find($business->company_id);
            if ($company && $company->is_check == 1) {
                $data['company_name']  = $company->company_name;
                $data['company_type']  = $company->company_type;
                $data['company']       = $company->getCompany();
                $data['business_type'] = $company->business_type;
                $data['business']      = $company->getBusiness();
            }
        }

        $data['browse_avatar'] = UserInfo::whereIn('user_id', $ids)
            ->get()
            ->map(function ($user) {
                return $user->avatar_url;
            });

        //获取浏览总人数
        $data['browse_count'] = BrowseRecord::where('bid', $business->id)->count();

        //获取点赞人数
        $data['like_count'] = Like::OfItem($business)->count();
        $data['is_like']    = 0;

        $like = Like::OfItem($business)->OfUser($userId)->first();

        if ($like) {
            $data['is_like'] = 1;
        }

        //未建立联系
        $is_change         = $this->findLink($business->uid, $business->id, $userId);
        $data['is_change'] = $is_change;
        //判断需要密码的
        $privacy = UserPrivacy::where('uid', $business->uid)->first();
        if ($business->uid == $userId) {
            $data['is_star'] = 1;
            return $request->kernel->success($data);
        }
        $data['is_star'] = 1;
        if (! $privacy) {
            $data['is_star'] = 1;
            return $request->kernel->success($data);
        } else {
            if ($pass) {
                $userPass = User::where(['id' => $business->uid])->value('pass');
                if ($pass == $userPass) {
                    $data['is_star'] = 1;
                    return $data;
                } else {
                    throw new ValidatorException("密码错误");
                }
            }
        }
        if ($is_change == 0 || $is_change == 1 || $is_change == 4) {
            $data['is_company_name'] = 0;
            if ($privacy->company_name == 1 && ! empty($data['company_name'])) {
                $data['is_star']         = 0;
                $data['is_company_name'] = 1;
                $data['company_name']    = "******";
            }
            $data['is_phone'] = 0;
            if ($privacy->phone == 1 && ! empty($data['phone'])) {
                $data['is_star']  = 0;
                $data['is_phone'] = 1;
                $data['phone']    = maskData($data['phone'], 0, -4);
            }
            $data['is_nickname'] = 0;
            if ($privacy->nickname == 1 && ! empty($data['nickname'])) {
                $data['is_star']     = 0;
                $data['is_nickname'] = 1;
                $data['nickname']    = strlen($data['nickname']) > 1 ? maskDatas($data['nickname'], 0, 3) : "******";
            }
            $data['is_email'] = 0;
            if ($privacy->email == 1 && ! empty($data['email'])) {
                $data['is_star']  = 0;
                $data['is_email'] = 1;
                $data['email']    = "******";
            }
            $data['is_address'] = 0;
            if ($privacy->address == 1 && ! empty($data['address'])) {
                $data['is_star']    = 0;
                $data['is_address'] = 1;
                $data['address']    = "******";
            }
            $data['is_wechat'] = 0;
            if ($privacy->wechat == 1 && ! empty($data['wechat'])) {
                $data['is_star']   = 0;
                $data['is_wechat'] = 1;
                $data['wechat']    = "******";
            }
        }

        $data['is_change'] = $is_change;
        return $request->kernel->success($data);
    }

    public function exchange(Request $request)
    {
        $request->kernel->validate([
            'from_bid' => 'required',
            'to_bid'   => 'required',
        ], [
            'from_bid.required' => '请选择要交换的名片',
            'to_bid.required'   => '请选择要交换的名片',
        ]);

        $user     = $request->kernel->user();
        $from_bid = $request->from_bid;
        $to_bid   = $request->to_bid;

        $fromBusiness = Business::find($from_bid);

        if (! $fromBusiness) {
            throw new ValidatorException("未查询到您的名片");
        }
        if ($fromBusiness->uid != $user->id) {
            throw new ValidatorException("您没有权限交换此名片");
        }

        $toBusiness = Business::find($to_bid);

        if (! $toBusiness) {
            throw new ValidatorException("要交换的名片不存在");
        }

        return DB::transaction(function () use ($fromBusiness, $toBusiness, $request) {
            try {
                $exists = Contact::where('from_bid', $fromBusiness->id)
                    ->where('to_bid', $toBusiness->id)
                    ->exists();
                if ($exists) {
                    throw new ValidatorException('您已经交换过此名片');
                }
                Contact::create([
                    'uid'       => $fromBusiness->uid,
                    'from_bid'  => $fromBusiness->id,
                    'to_bid'    => $toBusiness->id,
                    'dstid'     => $toBusiness->uid,
                    'status'    => 0,
                    'is_change' => 1,
                ]);
                return $request->kernel->success(true);
            } catch (ValidatorException $e) {
                throw new ValidatorException($e->getMessage());
            }
        });
    }

    /**
     * Notes: 同意交换
     *
     * @Author: 玄尘
     * @Date: 2024/12/5 08:54
     */
    public function agreeExchage(Request $request)
    {
        $request->kernel->validate([
            'from_bid' => 'required',
            'to_bid'   => 'required',
        ], [
            'from_bid.required' => '请选择要交换的名片',
            'to_bid.required'   => '请选择要交换的名片',
        ]);

        $user     = $request->kernel->user();
        $from_bid = $request->from_bid;
        $to_bid   = $request->to_bid;

        $fromBusiness = Business::find($from_bid);

        if (! $fromBusiness) {
            throw new ValidatorException("未查询到您的名片");
        }

//        if ($fromBusiness->uid != $user->id) {
//            throw new ValidatorException("您没有权限交换此名片");
//        }

        $toBusiness = Business::find($to_bid);

        if (! $toBusiness) {
            throw new ValidatorException("要交换的名片不存在");
        }

        if ($toBusiness->uid != $user->id) {
            throw new ValidatorException("您没有权限操作");
        }

        $res = Contact::where('from_bid', $from_bid)
            ->where('to_bid', $to_bid)
            ->update(['status' => 1]);

        Contact::create([
            'uid'       => $toBusiness->uid,
            'from_bid'  => $toBusiness->id,
            'to_bid'    => $fromBusiness->id,
            'dstid'     => $fromBusiness->uid,
            'status'    => 1,
            'is_change' => 0,
        ]);

        if ($res) {
            return $request->kernel->success(true);
        }
        throw new ValidatorException("操作失败");
    }

    public function businessInfo(Request $request)
    {
        $user = $request->kernel->user();

        //todo 检测我的名片
        $ids = Business::where('uid', $user->id)->pluck('id');

        //查看我的名片被访问情况
        //当天时间戳
        $start_time = Carbon::now()->startOfDay();
        $end_time   = Carbon::now()->endOfDay();

        $data = [
            'browse_today' => BrowseRecord::whereIn('bid', $ids)
                ->whereBetween('created_at', [$start_time, $end_time])
                ->count(),
            'browse_total' => BrowseRecord::whereIn('bid', $ids)->count(),
            'send_total'   => Business::whereIn('id', $ids)->sum('num') ?? 0,
            'change_total' => Contact::where('uid', $user->id)
                ->where('is_change', 0)
                ->where('status', 0)
                ->count(),
            'my_browse'    => BrowseRecord::where('uid', $user->id)->count(),
        ];
        return $request->kernel->success($data);
    }

    public function visitorMy(Request $request)
    {
        $request->kernel->validate([
            'bid' => ['nullable', 'integer'],
        ], [
            'bid.integer' => '名片id必须是数字'
        ]);

        $user = $request->kernel->user();
        $bid  = $request->bid ?? '';

        $page     = $request->page ?? 1;
        $pageSize = $request->pagesize ?? 10;

        if (! $bid) {
            $businessIds = Business::where('uid', $user->id)->pluck('id');
        } else {
            $viewBusiness = Business::find($bid);
            if (! $viewBusiness) {
                throw new ValidatorException('名片不存在');
            }
            if ($viewBusiness->uid != $user->id) {
                throw new ValidatorException('您没有权限查看此名片');
            }

            $businessIds = [$bid];
        }

        $browseRecords = BrowseRecord::whereIn('bid', $businessIds)
            ->forPage($page, $pageSize)
            ->latest('id')
            ->paginate();

        $userIds = $browseRecords->pluck('uid')->unique();

        //获取所有用户的名片
        $businessData = Business::whereIn('uid', $userIds)
            ->where('is_default', 1)
            ->get()
            ->keyBy('uid');

        // 获取所有真实姓名
        $realnames = Realname::whereIn('uid', $userIds)
            ->where('is_check', 1)
            ->get()
            ->keyBy('uid');

        // 获取所有用户信息
        $users = User::with('info')->whereIn('id', $userIds)->get()->keyBy('id');

        $fieldList = [];
        foreach ($browseRecords as $k => $browseRecord) {
            $uid = $browseRecord->uid;
            //是否有名片
            if (isset($businessData[$uid])) {
                $business      = $businessData[$uid];
                $fieldList[$k] = [
                    'id'           => $business->id,
                    'nickname'     => $business->nickname,
                    'is_work'      => $business->is_work,
                    'is_company'   => $business->is_company,
                    'avatar'       => $business->avatar,
                    'count'        => $business->count,
                    'company_name' => $business->company_name,
                    'annex'        => $business->annex ?? '',
                    'annex_name'   => $business->annex_name ?? '',
                    'position'     => $business->position,
                    'is_change'    => $this->findLink($uid, $business->id, $user->id),
                ];
            } else {
                $browseRecordUser = $users->get($uid);
                $fieldList[$k]    = [
                    'is_change' => 4,
                    'nickname'  => $browseRecordUser->info->nickname ?? '',
                    'avatar'    => $browseRecordUser->info->avatar ?? '',
                ];
            }

            $fieldList[$k] += [
                'to_id'        => $browseRecord->bid,
                'visitor_time' => $browseRecord->created_at,
                'is_check'     => isset($realnames[$uid]) ? 1 : 0,
            ];
        }

        $browseRecordTotal = $browseRecords->count();
        $data              = [
            'total'        => $browseRecordTotal,
            'per_page'     => $pageSize,
            'current_page' => $page,
            'last_page'    => ceil($browseRecordTotal / $pageSize),
            'data'         => $fieldList,
        ];

        return $request->kernel->success($data);
    }

    public function myVisitor(Request $request)
    {
        $user = $request->kernel->user();

        $page     = $request->page ?? 1;
        $pageSize = $request->pagesize ?? 10;

        $browseRecords = BrowseRecord::where('uid', $user->id)
            ->with(['business', 'realname'])
            ->whereHas('business')
            ->latest('id')
            ->paginate($pageSize, ['*'], 'page', $page);

        $fieldList = $browseRecords->map(function ($browseRecord) use ($user) {
            $business = $browseRecord->business;

            return [
                'id'           => $browseRecord->bid,
                'nickname'     => $business->nickname,
                'is_work'      => $business->is_work,
                'is_company'   => $business->is_company,
                'avatar'       => $business->avatar,
                'count'        => $business->count,
                'company_name' => $business->company_name,
                'annex'        => $business->annex ?? '',
                'annex_name'   => $business->annex_name ?? '',
                'position'     => $business->position,
                'visitor_time' => $browseRecord->created_at,
                'is_change'    => $this->findLink($business->uid, $browseRecord->bid, $user->id),
                'is_check'     => $browseRecord->realname ? 1 : 0,
            ];
        });

        $data['total']        = $browseRecords->total();
        $data['per_page']     = $browseRecords->perPage();
        $data['current_page'] = $browseRecords->currentPage();
        $data['last_page']    = $browseRecords->lastPage();
        $data['data']         = $fieldList;

        return $request->kernel->success($data);
    }

    public function qrcode(Request $request)
    {
        $request->kernel->validate([
            'bid' => 'required|integer',
        ], [
            'bid.required' => '缺少名片id',
            'bid.integer'  => '名片id必须是数字',
        ]);

        $user = $request->kernel->user();
        $bid  = $request->bid;

        $business = Business::find($bid);
        if (! $business) {
            throw new ValidatorException('名片不存在');
        }

        $size       = $request->size ?? '430';
        $envVersion = $request->version ?? 'release';
        $data       = [
            'username' => $user->username,
            'nickname' => $user->info->nickname ?? '',
            'avatar'   => $user->info->avatar_url ?? '',
        ];

        try {
            $app  = app('wechat.mini');
            $name = md5('business'.$bid).'.png';
            $path = "share/business/{$envVersion}/{$name}";
            if (! Storage::has($path)) {
                $response = $app->getClient()->postJson('/wxa/getwxacodeunlimit', [
                    'scene'       => http_build_query([
                        'bid' => $bid
                    ]),
                    'page'        => 'pages/out/card',
                    'width'       => $size,
                    'is_hyaline'  => true,
                    'env_version' => $envVersion,
                    'check_path'  => false,
                ]);

                if ($response->getStatusCode() == 200) {
                    $content = $response->getContent();
                    $content = json_decode($content, true);
                    if (isset($content['errcode'])) {
                        throw new ValidatorException('获取失败');
//                    throw new ValidatorException($content['errmsg']);
                    }
                    Storage::put($path, $response->getContent());
                } else {
                    throw new ValidatorException('获取失败');
                }
            }

            $data = array_merge($data, [
                'qrcode_url' => Storage::url($path),
                'qrcode'     => "data:image/png;base64,".base64_encode(Storage::get($path))
            ]);
            return $request->kernel->success($data);
        } catch (Exception $e) {
            throw new ValidatorException($e->getMessage());
        }
    }
}
