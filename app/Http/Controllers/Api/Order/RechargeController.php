<?php

namespace App\Http\Controllers\Api\Order;

use App\Http\Controllers\Controller;
use App\Models\RechargeOrder;
use App\Models\RechargePackage;
use Illuminate\Http\Request;
use Modules\Payment\Models\Payment;

class RechargeController extends Controller
{
    public function create(Request $request)
    {
        $request->kernel->validate([
            'goods_id' => ['required', 'integer', 'exists:recharge_packages,id'],
            'gateway'  => 'required',
            'channel'  => 'required',
        ], [
            'goods_id.required' => "请选择您要充值的套餐",
            'goods_id.integer'  => "请选择您要充值的套餐",
            'goods_id.exists'   => "充值套餐不存在，请刷新后重新选择",
            'gateway.required'  => '请传入支付网关',
            'channel.required'  => '请传入支付渠道',
        ]);
        $user    = $request->kernel->user();
        $package = RechargePackage::find($request->goods_id);
        $item    = RechargeOrder::create([
            'user_id'    => $user->id,
            'package_id' => $package->id,
            'price'      => $package->price,
            'score'      => $package->score,
            'source'     => $package->toArray(),
        ]);
        if ($item) {
            $payment = Payment::createPayment($user, $item, $request->gateway, $request->channel);

            if ($payment) {
                $platform = $request->kernel->getBody('Platform');

                $data = [
                    'no'     => $payment->no,
                    'title'  => '充值'.$item->score.'积分',
                    'price'  => $payment->amount,
                    'params' => $payment->getPaymentParams(),
                ];
                if ($platform == 'ios') {
                    $data['apple_goods_id'] = $package->apple_goods_id;
                }
                return $request->kernel->success($data);
            } else {
                return $request->kernel->error('创建订单失败');
            }
        } else {
            return $request->kernel->error('创建订单失败');
        }
    }
}