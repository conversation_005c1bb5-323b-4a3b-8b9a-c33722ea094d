<?php

namespace App\Http\Controllers\Api\Voice;

use App\Http\Controllers\Controller;
use App\Http\Resources\AiAbout\BailianTts\BailianTtsResource;
use App\Models\AudioSystemTimbre;
use App\Models\AudioTimbre;
use App\Models\SystemConfig;
use Exception;
use GuzzleHttp\Client;
use Illuminate\Http\Request;

class IndexController extends Controller
{
    protected Client $client;
    protected string $ttsModel = 'cosyvoice-v1';

    public function __construct()
    {
        $this->client = new Client([
            'base_uri' => 'https://dashscope.aliyuncs.com/api/v1/services/audio/tts/customization',
            'verify'   => false,
        ]);
    }

    public function create(Request $request)
    {
        $request->kernel->validate([
            'voice_address'       => 'required',
            'voice_subject_title' => 'required',
            'scenarios'           => 'nullable|string|max:200',
            'image_url'           => 'nullable|string|max:200'
        ], [
            'voice_address.required'       => '请上传音频文件',
            'voice_subject_title.required' => '请输入音色名称',
            'scenarios.max'                => '声音描述最大:max个字节'
        ]);
        $user = $request->kernel->user();

        try {
            $result = $this->doAliApi([
                "action"       => "create_voice",
                "target_model" => $this->ttsModel,
                "prefix"       => sprintf('%s%s', 'user', $user->id),
                "url"          => $request->voice_address,
            ]);
            if ($result['output']['voice_id'] ?? '') {
                $result = AudioTimbre::create([
                    "user_id"   => $user->id,
                    "voice_id"  => $result['output']['voice_id'],
                    'model'     => $this->ttsModel,
                    "url"       => $request->voice_address,
                    "image_url" => $request->image_url,
                    "people"    => $request->voice_subject_title,
                    'scenarios' => $request->scenarios,
                    "status"    => 1,
                ]);
                if ($result) {
                    return $request->kernel->success(new BailianTtsResource($result), '声音创建成功');
                }
                return $request->kernel->error("系统插入错误");
            } else {
                return $request->kernel->error('创建失败');
            }
        } catch (Exception $exception) {
            return $request->kernel->error($exception->getMessage());
        }
    }

    protected function doAliApi(array $input)
    {
        $apiKey = SystemConfig::getValue('bailian_api_key');

        $response = $this->client->request('POST', '', [
            'headers' => [
                'Authorization' => 'Bearer '.$apiKey,
            ],
            'json'    => [
                'model' => 'voice-enrollment',
                'input' => $input
            ],
        ]);
        if ($response->getStatusCode() == 200) {
            $data = json_decode($response->getBody()->getContents(), true);
            return $data;
        } else {
            throw new Exception($response->getBody()->getContents());
        }
    }

    public function update(Request $request)
    {
        $request->kernel->validate([
            'voice_id' => 'required|exists:audio_timbres,voice_id',
            'people'   => 'required|string|max:50',
        ], [
            'voice_id.required' => '请选择声音',
            'voice_id.exists'   => '声音不存在',
            'people.required'   => '请输入声音名称',
            'people.max'        => '声音名称最大:max个字节'
        ]);
        $user  = $request->kernel->user();
        $voice = AudioTimbre::ofUser($user)
            ->where("voice_id", $request->voice_id)
            ->first();
        if (! $voice) {
            return $request->kernel->error('找不到声音数据');
        }
        try {
            $voice->people = $request->people;
            $voice->save();
            return $request->kernel->success([], '更新成功');
        } catch (Exception $exception) {
            return $request->kernel->error($exception->getMessage());
        }
    }

    public function query(Request $request)
    {
        $request->kernel->validate([
            'voice_id' => 'required|exists:audio_timbres,voice_id'
        ], [
            'voice_id.required' => '请选择声音',
            'voice_id.exists'   => '声音不存在'
        ]);

        $item = AudioTimbre::ofUser($request->kernel->user())
            ->where("voice_id", $request->voice_id)
            ->first();
        if (! $item) {
            return $request->kernel->error('找不到声音数据');
        }
        try {
            $result = $this->doAliApi([
                "action"   => "query_voice",
                "voice_id" => $item->voice_id
            ]);
            if (($result['usage']['count'] ?? 0) <= 0) {
                return $request->kernel->error('删除失败');
            }
            $item->delete();
            return $request->kernel->success([], '删除成功');
        } catch (Exception $exception) {
            return $request->kernel->error($exception->getMessage());
        }
    }

    public function delete(Request $request)
    {
        $request->kernel->validate([
            'voice_id' => 'required|exists:audio_timbres,voice_id'
        ], [
            'voice_id.required' => '请选择声音',
            'voice_id.exists'   => '声音不存在'
        ]);

        $item = AudioTimbre::ofUser($request->kernel->user())
            ->where("voice_id", $request->voice_id)
            ->first();
        if (! $item) {
            return $request->kernel->error('找不到声音数据');
        }

        try {
            $result = $this->doAliApi([
                "action"   => "delete_voice",
                "voice_id" => $item->voice_id
            ]);
            if (($result['usage']['count'] ?? 0) <= 0) {
                return $request->kernel->error('删除失败');
            }
            $item->delete();
            return $request->kernel->success([], '删除成功');
        } catch (Exception $exception) {
            return $request->kernel->error($exception->getMessage());
        }
    }

    public function systemVoiceLists(Request $request)
    {
        $result = AudioSystemTimbre::ofEnabled()
            ->orderBy('id')
            ->get();
        return $request->kernel->success(BailianTtsResource::collection($result), '查询成功');
    }
}
