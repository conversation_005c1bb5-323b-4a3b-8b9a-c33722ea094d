<?php

namespace App\Http\Controllers\Api\Voice;

use App\Http\Controllers\Controller;
use App\Http\Resources\AiAbout\BailianTts\BailianTtsResource;
use App\Http\Resources\Voice\XunfeiTaskResource;
use App\Models\AudioXfSystemTimbre;
use App\Models\AudioXfTimbre;
use DateTimeZone;
use Illuminate\Http\Request;

class XunfeiController extends Controller
{
    use XunfeiTrait;

    public function getVcnList(Request $request)
    {
        $result = AudioXfSystemTimbre::ofEnabled()
            ->orderBy('id')
            ->get();
        return $request->kernel->success(BailianTtsResource::collection($result), '查询成功');
    }

    public function getAuth(Request $request)
    {
        $host = $request->host;
        $path = $request->path;
        $time = now()->setTimezone(new DateTimeZone('GMT'))->format('D, d M Y H:i:s T');

        $string    = sprintf("host: %s\ndate: %s\nGET %s HTTP/1.1", $host, $time, $path);
        $stringSha = hash_hmac('sha256', $string, $this->apiSecret, true);
        //        $stringShaHax        = bin2hex($stringSha);
        $base64              = base64_encode($stringSha);
        $authorizationOrigin = sprintf("api_key=\"{$this->apiKey}\",algorithm=\"hmac-sha256\",headers=\"host date request-line\", signature=\"{$base64}\"");
        $authorization       = base64_encode($authorizationOrigin);
        $queryParmas         = [
            'authorization' => $authorization,
            'host'          => $host,
            'date'          => $time,
        ];
        $url                 = sprintf("wss://%s%s?%s", $host, $path, http_build_query($queryParmas));

        return $request->kernel->success([
            'url'   => $url,
            'appid' => $this->appId,
        ]);
    }

    public function getTrainText(Request $request)
    {
        $result = $this->getResponse('task/traintext', [
            'textId' => 5001,
        ]);
        if ($result['code'] === 0) {
            return $request->kernel->success($result['data']);
        } else {
            return $request->kernel->error($result['desc']);
        }
    }

    public function addAudio(Request $request)
    {
        $request->kernel->validate([
            'task_id'   => 'required|exists:audio_xf_timbres,task_id',
            'audio_url' => 'required|url',
            'text_id'   => 'required',
            'seg_id'    => 'required',
        ]);
        $user = $request->kernel->user();
        $task = AudioXfTimbre::ofUser($user)->where('task_id', $request->task_id)->first();
        if (! $task) {
            return $request->kernel->error('任务不存在');
        }

        $data   = [
            'taskId'    => $request->task_id,
            'audioUrl'  => $request->audio_url,
            'textId'    => (int) $request->text_id,
            'textSegId' => (int) $request->seg_id,
        ];
        $result = $this->getResponse('audio/v1/add', $data);
        if ($result['code'] === 0) {
            $task->logs()->updateOrCreate([
                'text_id' => $request->text_id,
                'seg_id'  => $request->seg_id,
            ], [
                'audio_url' => $request->audio_url,
                'flag'      => $result['flag'],
            ]);
            return $request->kernel->success([
                'test'   => $result,
                'result' => $result['flag'],
                'task'   => new XunfeiTaskResource($task),
            ]);
        } else {
            return $request->kernel->error($result['desc']);
        }
    }

    public function createTask(Request $request)
    {
        $user   = $request->kernel->user();
        $result = $this->getResponse('task/add', [
            'resourceType' => 12,
            'thirdUser'    => $user->username,
        ]);
        if ($result['code'] === 0) {
            $task = AudioXfTimbre::create([
                'user_id' => $user->id,
                'task_id' => $result['data'],
                'status'  => AudioXfTimbre::STATUS_CREATE,
            ]);
            return $request->kernel->success(new XunfeiTaskResource($task));
        } else {
            return $request->kernel->error($result['desc']);
        }
    }

    public function lists(Request $request)
    {
        $user   = $request->kernel->user();
        $status = $request->status ?? null;// 0  停用 1正常 4创建中 5提交中 9失败
        $lists  = AudioXfTimbre::query()
            ->ofUser($user)
            ->when(is_numeric($status), function ($query) use ($status) {
                $query->where('status', $status);
            })
            ->orderByDesc('created_at')
            ->get();
        return $request->kernel->success(XunfeiTaskResource::collection($lists));
    }

    public function submitTask(Request $request)
    {
        $request->kernel->validate([
            'task_id' => 'required|exists:audio_xf_timbres,task_id',
        ]);
        $user = $request->kernel->user();
        $task = AudioXfTimbre::ofUser($user)->where('task_id', $request->task_id)->first();
        if (! $task) {
            return $request->kernel->error('任务不存在');
        }
        $result = $this->getResponse('task/submit', [
            'taskId' => $task->task_id,
        ]);
        if ($result['code'] === 0) {
            $task->status = AudioXfTimbre::STATUS_SUBMIT;
            $task->save();
            $task->jobQuery();
            return $request->kernel->success([
                'result' => $result['flag'],
                'task'   => new XunfeiTaskResource($task),
            ]);
        } else {
            return $request->kernel->error($result['desc']);
        }
    }

    public function query(Request $request)
    {
        $request->kernel->validate([
            'task_id' => 'required|exists:audio_xf_timbres,task_id',
        ]);
        $user = $request->kernel->user();
        $task = AudioXfTimbre::ofUser($user)->where('task_id', $request->task_id)->first();
        if (! $task) {
            return $request->kernel->error('任务不存在');
        }
        return $request->kernel->success(new XunfeiTaskResource($task));
    }

}