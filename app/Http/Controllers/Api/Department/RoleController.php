<?php

namespace App\Http\Controllers\Api\Department;

use App\Exceptions\ValidatorException;
use App\Http\Controllers\ApiController;
use App\Models\CompanyRole;
use App\Models\User;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use <PERSON><PERSON>les\User\Models\Department;

class RoleController extends ApiController
{
    public function index(Request $request): JsonResponse
    {
        $roles = CompanyRole::query()
            ->where('label', "<>", 'administrator')
            ->oldest()
            ->select('id', 'name')
            ->get();

        return $request->kernel->success($roles);
    }

    /**
     * Notes: 添加角色
     *
     * @Author: 玄尘
     * @Date: 2024/12/19 10:31
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     * @throws \App\Exceptions\ValidatorException
     */
    public function join(Request $request): JsonResponse
    {
        $request->kernel->validate([
            'user_id'       => 'required|exists:users,id|integer',
            'department_id' => 'required|exists:user_departments,id|integer',
            'role_id'       => 'required|exists:company_roles,id|integer',
        ], [
            'user_id.required'       => '缺少用户id',
            'user_id.exists'         => '用户不存在',
            'user_id.integer'        => '用户id只能是数字',
            'department_id.required' => '缺少部门id',
            'department_id.exists'   => '部门不存在',
            'department_id.integer'  => '部门id只能是数字',
            'role_id.required'       => '缺少角色id',
            'role_id.exists'         => '角色不存在',
            'role_id.integer'        => '角色id只能是数字',
        ]);

        $user = $request->kernel->user();

        $userId       = $request->input('user_id');
        $departmentId = $request->input('department_id');
        $roleId       = $request->input('role_id');

        $department = Department::find($departmentId);

        if (! $user->isManagementRole($department->company_id)) {
            throw new ValidatorException('您没有权限');
        }

        $audience = User::find($userId);

        $role = CompanyRole::find($roleId);
        $role->canJoin($user, $audience, $departmentId);

        $departmentId = $role->getDepartmentId($departmentId);

        $audience->companyDepartmentRoles()
            ->create([
                'department_id'   => $departmentId,
                'company_role_id' => $roleId,
            ]);
        return $request->kernel->success();
    }

    /**
     * Notes: 删除用户角色
     *
     * @Author: 玄尘
     * @Date: 2024/12/19 11:13
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     * @throws \App\Exceptions\ValidatorException
     */
    public function leave(Request $request): JsonResponse
    {
        $request->kernel->validate([
            'user_id'       => 'required|exists:users,id|integer',
            'department_id' => 'required|exists:user_departments,id|integer',
            'role_id'       => 'required|exists:company_roles,id|integer',
        ], [
            'user_id.required'       => '缺少用户id',
            'user_id.exists'         => '用户不存在',
            'user_id.integer'        => '用户id只能是数字',
            'department_id.required' => '缺少部门id',
            'department_id.exists'   => '部门不存在',
            'department_id.integer'  => '部门id只能是数字',
            'role_id.required'       => '缺少角色id',
            'role_id.exists'         => '角色不存在',
            'role_id.integer'        => '角色id只能是数字',
        ]);

        $user = $request->kernel->user();

        $userId       = $request->input('user_id');
        $departmentId = $request->input('department_id');
        $roleId       = $request->input('role_id');

        $department = Department::find($departmentId);

        if (! $user->isManagementRole($department->company_id)) {
            throw new ValidatorException('您没有权限');
        }

        $audience = User::find($userId);

        $role = CompanyRole::find($roleId);
        $role->canLeave($user, $audience, $departmentId);

        $departmentId = $role->getDepartmentId($departmentId);

        $info = $audience->companyDepartmentRoles()
            ->where('department_id', $departmentId)
            ->where('company_role_id', $roleId)
            ->first();
        if (! $info) {
            throw new ValidatorException('此人没有此权限');
        }

        $info->delete();

        return $request->kernel->success('移除成功');
    }

}