<?php

namespace App\Http\Controllers\Api\Bailian;

use App\Exceptions\ValidatorException;
use App\Http\Controllers\ApiController;
use App\Models\BailianDirectory;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class DirectoryController extends ApiController
{

    /**
     * 创建知识库目录
     *
     * @param  Request  $request
     * @return JsonResponse
     */
    public function create(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'knowledge_id' => 'required|exists:bailian_knowledge,id',
            'name'         => 'required|string|max:255',
        ]);

        if ($validator->fails()) {
            throw new ValidatorException($validator->errors()->first());
        }

        $knowledgeId       = $request->input('knowledge_id');
        $name              = $request->input('name');
        $user              = $request->kernel->user();
        $knowledge_item_id = $request->input('knowledge_item_id', null);
        // 创建目录
        $directory = BailianDirectory::create([
            'knowledge_id' => $knowledgeId,
            'user_id'      => $user->id,
            'name'         => $name,
        ]);

        $directory->addToKnowledgeBase($knowledgeId, null, [
            'parent_id' => $knowledge_item_id
        ]);

        return $request->kernel->success('目录创建成功');
    }

    /**
     * Notes: 更新知识库目录
     *
     * @Author: 玄尘
     * @Date: 2025/5/8 17:01
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     * @throws \App\Exceptions\ValidatorException
     */
    public function update(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'id'   => 'required|exists:bailian_directories,id',
            'name' => 'required|string|max:255',
        ], [
            'id.required'   => '目录id不能为空',
            'id.exists'     => '目录不存在',
            'name.required' => '目录名称不能为空',
            'name.string'   => '目录名称必须为字符串',
            'name.max'      => '目录名称长度不能超过255个字符',
        ]);

        if ($validator->fails()) {
            throw new ValidatorException($validator->errors()->first());
        }

        // 获取目录
        $directory         = BailianDirectory::find($request->id);
        $user              = $request->kernel->user();
        $knowledge_item_id = $request->input('knowledge_item_id', null);

        if ($directory->user->isNot($user)) {
            return $request->kernel->error('您没有权限修改该目录');
        }

        $directory->update([
            'name' => $request->name,
        ]);
        if ($directory->knowledgeItem) {
            $data = [
                'title' => $request->name,
            ];
            if ($knowledge_item_id) {
                $data['parent_id'] = $knowledge_item_id;
            }
            $directory->knowledgeItem->update($data);
        }

        return $request->kernel->success('目录更新成功');
    }

    /**
     * Notes: 删除知识库目录
     *
     * @Author: 玄尘
     * @Date: 2025/5/8 17:04
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     * @throws \Throwable
     */
    public function delete(Request $request): JsonResponse
    {
        $validator = Validator::make($request->all(), [
            'id' => 'required|exists:bailian_directories,id',
        ], [
            'id.required' => '目录id不能为空',
            'id.exists'   => '目录不存在',
        ]);

        if ($validator->fails()) {
            throw new ValidatorException($validator->errors()->first());
        }
        // 获取目录
        $directory = BailianDirectory::find($request->id);
        $user      = $request->kernel->user();
        if ($directory->user->isNot($user)) {
            return $request->kernel->error('您没有权限删除该目录');
        }

        DB::beginTransaction();
        try {
            $knowledgeItem = $directory->knowledgeItem;
            if ($knowledgeItem) {
                if ($knowledgeItem->children()->count() > 0) {
                    $knowledgeItem->children()->update([
                        'parent_id' => null
                    ]);
                }
                $knowledgeItem->delete();
            }

            // 删除目录
            $directory->delete();

            DB::commit();
            return $request->kernel->success('目录删除成功');
        } catch (Exception $e) {
            DB::rollBack();
            return $request->kernel->error('目录删除失败: '.$e->getMessage());
        }
    }

}
