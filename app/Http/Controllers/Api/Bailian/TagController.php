<?php

namespace App\Http\Controllers\Api\Bailian;

use App\Exceptions\ValidatorException;
use App\Http\Controllers\ApiController;
use App\Http\Resources\Bailian\Tag\TagResource;
use App\Models\BailianTag;
use App\Packages\BailianAssistant\BLAssistant;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;

class TagController extends ApiController
{
    /**
     * 获取标签列表
     *
     * @param  Request  $request
     * @return JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        $user = $request->kernel->user();
        $tags = BailianTag::query()
            ->withCount('articles')
            ->where('user_id', $user->id)
            ->get();
        return $request->kernel->success(TagResource::collection($tags));
    }

    /**
     * 创建标签
     *
     * @param  Request  $request
     * @return JsonResponse
     * @throws ValidatorException
     */
    public function store(Request $request): JsonResponse
    {
        $request->kernel->validate([
            'name' => ['required', 'string', 'max:50'],
        ], [
            'name.required' => '标签名称不能为空',
            'name.max'      => '标签名称最多50个字符',
        ]);

        $user = $request->kernel->user();
        $name = $request->name;

        $tag = BailianTag::create([
            'user_id' => $user->id,
            'name'    => $name,
        ]);

        return $request->kernel->success(new TagResource($tag));
    }

    /**
     * 删除标签
     *
     * @param  Request  $request
     * @return JsonResponse
     * @throws ValidatorException
     */
    public function delete(Request $request): JsonResponse
    {
        $request->kernel->validate([
            'id' => ['required', 'exists:bailian_tags,id'],
        ], [
            'id.required' => '标签ID不能为空',
            'id.exists'   => '标签不存在',
        ]);

        $user = $request->kernel->user();
        $tag  = BailianTag::find($request->id);

        if ($tag->user->isNot($user)) {
            throw new ValidatorException('您没有权限删除此标签');
        }

        $tag->delete();

        return $request->kernel->success('删除成功');
    }

    /**
     * Notes: 根据文字生成标签
     *
     * @Author: 玄尘
     * @Date: 2025/5/29 19:42
     * @param  \Illuminate\Http\Request  $request
     * @return mixed
     * @throws \Laravel\Octane\Exceptions\DdException
     */
    public function createByArticle(Request $request)
    {
        $text = $request->text;
        $user = $request->kernel->user();
        $res  = app(BLAssistant::class)->understanding()->generateTagsByText($text);
        if (! $res['status']) {
            return $request->kernel->error($res['message']);
        }

        $output   = json_decode($res['data']['output'], true);
        $tagTexts = $output['tags'];
        if (empty($tagTexts)) {
            return $request->kernel->error('没有生成标签');
        }
        $tags = [];
        foreach ($tagTexts as $tag) {
            $tags[] = $user->tags()->updateOrCreate([
                'name' => $tag
            ]);
        }

        return $request->kernel->success(TagResource::collection($tags));
    }

}
