<?php

namespace App\Http\Controllers\Api\Rec;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Str;

class XunfeiRecController extends Controller
{
    private string $version      = 'v1.0';
    private string $appId        = '7RUJz8';
    private string $accessKey    = '41rYi9FI95';
    private string $accessSecret = 'Q38R7bj1l142P74YIN3GW07O2k5Yx515';

    public function getSign(Request $request)
    {
        return $request->kernel->success($this->authSign());
    }

    private function authSign()
    {
        $time       = now()->format('Y-m-d\TH:i:sO');
        $uuid       = Str::uuid()->toString();
        $baseString = sprintf("%s,%s,%s,%s,%s",
            $this->version,
            $this->appId,
            $this->accessKey,
            $time,
            $uuid);
        $sign       = base64_encode(hash_hmac('sha1', urlencode($baseString), $this->accessSecret, true));
        $authString = $baseString.','.$sign;
        return [
            'auth_string' => urlencode($authString),
            'config'      => [
                'versionNumber' => $this->version,
                'appId'         => $this->appId,
                'accessKeyId'   => $this->accessKey,
                'utc'           => $time,
                'randomNumber'  => $uuid,
                'signature'     => $sign,
            ],
        ];
    }

}