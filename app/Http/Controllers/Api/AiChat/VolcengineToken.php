<?php

namespace App\Http\Controllers\Api\AiChat;

use DateTime;

class VolcengineToken
{
    private static  $_version        = "001";
    private static  $_version_length = 3;
    private static  $_app_id_length  = 24;
    public          $appID, $appKey, $roomID, $userID;
    public          $issuedAt, $nonce, $expireAt, $privileges;
    protected array $PrivilegesA     = array(
        "PrivPublishStream"      => 0,
        "privPublishAudioStream" => 1,
        "privPublishVideoStream" => 2,
        "privPublishDataStream"  => 3,
        "PrivSubscribeStream"    => 4,
    );

    // New initializes token struct by required parameters.

    public function __construct($appID, $appKey, $roomID, $userID)
    {
        $this->appID  = $appID;
        $this->appKey = $appKey;
        $this->roomID = $roomID;
        $this->userID = $userID;

        $this->issuedAt   = $this->v_now();
        $this->nonce      = rand(0, 100000);
        $this->expireAt   = 0;
        $this->privileges = array();
    }

    // AddPrivilege adds permission for token with an expiration.

    function v_now()
    {
        date_default_timezone_set("UTC");
        $date = new DateTime();
        return $date->getTimestamp();
    }

    // ExpireTime sets token expire time, won't expire by default.
    // The token will be invalid after expireTime no matter what privilege's expireTime is.

    public static function parse($raw)
    {
        if (strlen($raw) <= self::$_version_length + self::$_app_id_length) {
            return;
        }
        if (substr($raw, 0, self::$_version_length) !== self::$_version) {
            return;
        }

        $token        = new VolcengineToken();
        $token->appID = substr($raw, self::$_version_length, self::$_app_id_length);
        $content      = (base64_decode(substr($raw, self::$_version_length + self::$_app_id_length,
            strlen($raw) - (self::$_version_length + self::$_app_id_length))));

        $pos = 0;
        $len = unpack("v", $content.substr($pos, 2))[1];
        $pos += 2;
        $msg = substr($content, $pos, $len);

        $pos              += $len;
        $sigLen           = unpack("v", substr($content, $pos, 2))[1];
        $pos              += 2;
        $token->signature = substr($content, $pos, $sigLen);

        $p               = 0;
        $token->nonce    = unpack("V", substr($msg, $p, 4))[1];
        $p               += 4;
        $token->issuedAt = unpack("V", substr($msg, $p, 4))[1];
        $p               += 4;
        $token->expireAt = unpack("V", substr($msg, $p, 4))[1];
        $p               += 4;
        $roomLen         = unpack("v", substr($msg, $p, 2))[1];
        $p               += 2;
        $token->roomID   = substr($msg, $p, $roomLen);
        $p               += $roomLen;
        $uidLen          = unpack("v", substr($msg, $p, 2))[1];
        $p               += 2;
        $token->userID   = substr($msg, $p, $uidLen);
        $p               += $uidLen;
        $size            = unpack("v", substr($msg, $p, 2))[1];
        $p               += 2;
        $privileges      = array();
        for ($i = 0; $i < $size; $i++) {
            $key                 = unpack("v", substr($msg, $p, 2));
            $p                   += 2;
            $value               = unpack("V", substr($msg, $p, 4));
            $p                   += 4;
            $privileges[$key[1]] = $value[1];
        }
        $token->privileges = $privileges;

        return $token;
    }

    public function addPrivilege($key, $expireTimestamp)
    {
        $this->privileges[$key] = $expireTimestamp;

        if ($key === $this->PrivilegesA["PrivPublishStream"]) {
            $this->privileges[$this->PrivilegesA["privPublishAudioStream"]] = $expireTimestamp;
            $this->privileges[$this->PrivilegesA["privPublishVideoStream"]] = $expireTimestamp;
            $this->privileges[$this->PrivilegesA["privPublishDataStream"]]  = $expireTimestamp;
        }
        return $this;
    }

    // Serialize generates the token string

    public function expireTime($expireTimestamp)
    {
        $this->expireAt = $expireTimestamp;
        return $this;
    }

    // Verify checks if this token valid, called by server side.

    public function serialize()
    {
        $msg       = $this->packMsg();
        $signature = hash_hmac('sha256', implode(array_map("chr", $msg)), $this->appKey, true);
        $content   = array_merge(unpack("C*", pack("v", count($msg))), $msg,
            unpack("C*", $this->v_packString($signature)));
        $ret       = self::$_version.$this->appID.base64_encode(implode(array_map("chr", $content)));
        return $ret;
    }

    // Parse retrieves token information from raw string

    public function packMsg()
    {
        $buffer = unpack("C*", pack("V", $this->nonce));
        $buffer = array_merge($buffer, unpack("C*", pack("V", $this->issuedAt)));
        $buffer = array_merge($buffer, unpack("C*", pack("V", $this->expireAt)));
        $buffer = array_merge($buffer, unpack("C*", $this->v_packString($this->roomID)));
        $buffer = array_merge($buffer, unpack("C*", $this->v_packString($this->userID)));

        $buffer = array_merge($buffer, unpack("C*", pack("v", sizeof($this->privileges))));

        sort($this->privileges);
        foreach ($this->privileges as $key => $value) {
            $buffer = array_merge($buffer, unpack("C*", pack("v", $key)));
            $buffer = array_merge($buffer, unpack("C*", pack("V", $value)));
        }
        return $buffer;
    }

    function v_packString($value)
    {
        return pack("v", strlen($value)).$value;
    }

    public function verify($key)
    {
        if ($this->expireAt > 0 && $this->v_now() > $this->expireAt) {
            return false;
        }
        $this->appKey = $key;
        return $this->signature === hash_hmac('sha256', implode(array_map("chr", $this->packMsg())), $this->appKey,
                true);
    }
}