<?php

namespace App\Http\Controllers\Api\AiChat;

use AlibabaCloud\SDK\ICE\V20201109\Models\AIAgentRuntimeConfig\avatarChat3D;
use AlibabaCloud\SDK\ICE\V20201109\Models\AIAgentRuntimeConfig\visionChat;
use AlibabaCloud\SDK\ICE\V20201109\Models\AIAgentRuntimeConfig\voiceChat;
use AlibabaCloud\SDK\ICE\V20201109\Models\AIAgentTemplateConfig;
use AlibabaCloud\SDK\ICE\V20201109\Models\GenerateAIAgentCallRequest;
use AlibabaCloud\SDK\ICE\V20201109\Models\StopAIAgentInstanceRequest;
use AlibabaCloud\Tea\Exception\TeaError;
use AlibabaCloud\Tea\Utils\Utils\RuntimeOptions;
use App\Http\Controllers\Controller;
use App\Http\Resources\Draw\AudioCollection;
use App\Http\Resources\Draw\AudioResource;
use App\Models\AiUnifyAsset;
use App\Models\AudioAiCompanion;
use App\Models\AudioRole;
use App\Models\AudioRoom;
use App\Models\DrawAudio;
use App\Models\DrawAudioStyle;
use App\Models\SystemConfig;
use App\Models\User;
use App\Packages\Suno\Suno;
use Exception;
use Firebase\JWT\JWT;
use GuzzleHttp\Client;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;

class AudioController extends Controller
{
    public function captcha(Request $request)
    {
        $result = Suno::tools()->captcha();
        $data   = [
            'captcha_key'  => '',
            'image_base64' => '',
            'title_base64' => ''
        ];
        if ($result->isSuccess()) {
            $data = $result->toArray();
        }
        return $request->kernel->success($data);
    }

    public function gen(Request $request)
    {
        $request->kernel->validate([
            'prompt'       => 'required|string|max:1000',
            'captcha_data' => 'required',
            'captcha_key'  => 'required',
        ], [
            'prompt.required'       => '请填写描述内容',
            'prompt.max'            => '描述内容最大:max个字符',
            'captcha_data.required' => '请先进行图文验证',
            'captcha_key.required'  => '请先进行图文验证',
        ]);
        $user  = $request->kernel->user();
        $score = SystemConfig::getValue('audio_score', 0);
        if ($user->account->score < $score) {
            return $request->kernel->error('您当前剩余积分不足');
        }
        $audio = DrawAudio::create([
            'user_id'      => $user->id,
            'mode'         => 1,
            'score'        => $score,
            'model'        => 'chirp-v4',
            'instrumental' => $request->instrumental ? 1 : 0,
            'prompt'       => $request->prompt,
            'status'       => AiUnifyAsset::STATUS_INIT,
            'is_asset'     => $request->is_asset ? true : false,
            'ext'          => [
                'captcha_data' => $request->captcha_data,
                'captcha_key'  => $request->captcha_key,
            ]
        ]);
        if ($audio) {
            return $request->kernel->success(new AudioResource($audio));
        } else {
            return $request->kernel->error('创建失败');
        }
    }

    public function genCustom(Request $request)
    {
        $request->kernel->validate([
            'prompt' => 'required|string|max:1000',
            'title'  => 'required|max:50'
        ], [
            'prompt.required' => '请填写描述内容',
            'prompt.max'      => '描述内容最大:max个字符',
            'title.required'  => '请输入歌名',
            'title.max'       => '歌名最大:max个字符',
        ]);
        $user  = $request->kernel->user();
        $score = SystemConfig::getValue('audio_score', 0);
        if ($user->account->score < $score) {
            return $request->kernel->error('您当前剩余积分不足');
        }
        $audio = DrawAudio::create([
            'user_id'      => $user->id,
            'mode'         => 2,
            'gender'       => $request->gender,
            'score'        => $score,
            'name'         => $request->title,
            'tags'         => $request->tags,
            'model'        => 'chirp-v4',
            'instrumental' => $request->instrumental ? 1 : 0,
            'prompt'       => $request->prompt,
            'status'       => AiUnifyAsset::STATUS_INIT,
            'is_asset'     => $request->is_asset ? true : false,
            'ext'          => [
                'captcha_data' => $request->captcha_data,
                'captcha_key'  => $request->captcha_key,
                'type'         => 'master'
            ]
        ]);
        if ($audio) {
            return $request->kernel->success(new AudioResource($audio));
        } else {
            return $request->kernel->error('创建失败');
        }
    }

    public function lists(Request $request)
    {
        $user     = $request->kernel->user();
        $sortKey  = $request->sort_key ?: '';
        $sortType = $request->sort_type ?: '';
        $sortKey  = match ($sortKey) {
            'name', 'duration', 'created_at' => $sortKey,
            default => '',
        };
        $sortType = match ($sortType) {
            'asc', 'desc' => $sortType,
            default => '',
        };
        $lists    = DrawAudio::ofUser($user)
            ->when(filled($sortKey) && filled($sortType), function (Builder $query) use ($sortKey, $sortType) {
                $query->orderBy($sortKey, $sortType)
                    ->orderBy('over_at', 'desc');
            }, function (Builder $query) {
                $query->orderBy('created_at', 'desc');
            })
            ->orderByDesc('created_at')
            ->paginate($request->pagesize ?: 10);
        return $request->kernel->success(new AudioCollection($lists));
    }

    public function getTags(Request $request)
    {
        $tags = [];
        foreach (DrawAudioStyle::TYPES as $key => $type) {
            $tags[$key] = DrawAudioStyle::ofEnabled()
                ->where('type', $key)
                ->select('group_title', 'title')
                ->get()
                ->groupBy('group_title')
                ->toArray();
        }
        return $request->kernel->success($tags);
    }

    public function isIng(Request $request)
    {
        $no     = $request->no;
        $parent = DrawAudio::where('no', $no)
            ->whereNull('parent_no')
            ->first();
        $data   = [
            'status' => 0,
            'child'  => [],
        ];
        if (! $parent) {
            $children = DrawAudio::where('parent_no', $no)->get();
            if (! $children->isEmpty()) {
                $data['status'] = 1;
                $data['child']  = $children->map(function ($child) {
                    return new AudioResource($child);
                })->toArray();
            }
        }
        return $request->kernel->success($data);
    }

    public function result(Request $request)
    {
        $user = $request->kernel->user();
        $item = DrawAudio::ofUser($user)
            ->where('no', $request->no)
            ->first();
        if (! $item) {
            return $request->kernel->error('生成的音频不存在');
        }

        return $request->kernel->success(new AudioResource($item));
    }

    public function delete(Request $request)
    {
        $user = $request->kernel->user();
        $item = DrawAudio::ofUser($user)
            ->where('no', $request->no)
            ->first();
        if (! $item) {
            return $request->kernel->error('任务不存在');
        }
        if (! $item->canDelete()) {
            return $request->kernel->error('当前任务不可删除');
        }
        if ($item->delete()) {
            return $request->kernel->success([], '删除成功');
        } else {
            return $request->kernel->error('删除失败');
        }
    }

    public function createRoom(Request $request)
    {
        $request->kernel->validate([
            'role_id' => 'required|integer|exists:audio_roles,id',
        ], [
            'role_id.required' => '请选择角色',
            'role_id.integer'  => '请选择角色',
            'role_id.exists'   => '角色信息不存在',
        ]);
        $user = $request->kernel->user();
        $this->checkTime($user);
        $role   = AudioRole::find($request->role_id);
        $gender = strtolower($request->gender ?: 'female');
        if (! in_array($gender, ['male', 'female'])) {
            return $request->kernel->error('性别信息错误');
        }
        $name   = sprintf("我的名字叫【%s】", $role->$gender);
        $answer = sprintf("# 回答要求：\n%s", $role->remark);
        $prompt = sprintf("%s\n%s\n%s\n", $name, $role->about, $answer);
        list($maxTime, $timeMinutes) = $this->getRtcCheck($user);

        $token    = $this->getToken($timeMinutes * 60);
        $voice    = [
            'voiceType'    => strtoupper($gender),
            'systemPrompt' => $prompt,
        ];
        $client   = new Client();
        $response = $client->request('POST',
            'https://bigmodel.cn/api/biz/rtc/rtcRoomToken',
            [
                'headers' => [
                    'Authorization' => $token,
                ],
                'json'    => $voice,
            ]);
        if ($response->getStatusCode() == 200) {
            $data = json_decode($response->getBody()->getContents(), true);
            if ($data['code'] == 200) {
                list($maxTime, $timeMinutes) = $this->getRtcCheck($user);
                $item = AudioRoom::create([
                    'type'           => AudioRoom::BIGMODEL,
                    'user_id'        => $user->id,
                    'role_id'        => $role->id,
                    'score'          => 0,
                    'voice'          => $voice,
                    'auth_token'     => $data['data']['auth_token'],
                    'audio_user_id'  => $data['data']['user_id'],
                    'room_id'        => $data['data']['room_id'],
                    'server_user_id' => $data['data']['server_user_id'],
                    'expiration_at'  => now()->addMinutes($timeMinutes),
                ]);

                return $request->kernel->success([
                    'auth_token'     => $data['data']['auth_token'],
                    'timestamp'      => $data['data']['timestamp'],
                    'audio_user_id'  => $data['data']['user_id'],
                    'room_id'        => $data['data']['room_id'],
                    'server_user_id' => $data['data']['server_user_id'],
                    'expiration_at'  => $item->expiration_at->toDateTimeString(),
                ]);
            } else {
                return $request->kernel->error($data['msg']);
            }
        } else {
            return $request->kernel->error('创建房间失败');
        }
    }

    protected function checkTime(User $user)
    {
        list($maxTime, $maxMinutes) = $this->getRtcCheck($user);
        $time = AudioRoom::where('user_id', $user->id)
            ->whereDate('created_at', now()->toDateString())
            ->count();
        if ($maxTime <= $time) {
            throw new Exception('您今天已经创建了'.$maxTime.'次对话，请明天再试');
        }
    }

    protected function getRtcCheck(User $user): array
    {
        $identity   = $user->identityFirst();
        $maxTime    = $identity->getRules('rtc', 3);
        $maxMinutes = $identity->getRules('rtc_time', 3);
        return [$maxTime, $maxMinutes];
    }

    protected function getToken(int $expireSeconds = 600)
    {
        //        $keys        = '106ec37796bd90c5790b1a1b0bb3522a.XjAlhxVhzi35sI2o';
        $keys        = env('ZHIPU_APPKEY');
        $apiKeyInfo  = explode('.', $keys);
        $apiKey      = $apiKeyInfo[0];
        $apiSecret   = $apiKeyInfo[1];
        $currentTime = time();
        $exp         = $currentTime + $expireSeconds;

        $payload = [
            'api_key'   => $apiKey,
            'exp'       => $exp,
            'timestamp' => $currentTime
        ];
        $headers = [
            'sign_type' => 'SIGN',
        ];
        try {
            return JWT::encode(
                payload: $payload,
                key: $apiSecret,
                alg: 'HS256',
                head: $headers,
            );
        } catch (Exception $e) {
            Log::error('Error generating JWT token: '.$e->getMessage());
            return null;
        }
    }

    public function generateAIAgentCall(Request $request)
    {
        $user = $request->kernel->user();
        $this->checkTime($user);
        list($maxTime, $timeMinutes) = $this->getRtcCheck($user);

        $client    = AudioRoom::getClient();
        $appId     = 'a681d68c-07fa-4b80-b346-c7bbc0895744';
        $appKey    = '92d1ac33d909894af869d4d280952ae2';
        $channelId = Str::random(16);
        $AIAgentId = '60d74e9757b94611ae6fca6332e36ee4';
        $time      = $timeMinutes * 60;
        $token     = hash('sha256',
            $appId.$appKey.$channelId.$request->kernel->user()->username.$time);

        $runtime = new RuntimeOptions([
            "ignoreSSL" => true
        ]);

        try {
            $params                      = [
                "agentUserId" => $AIAgentId,
                "authToken"   => $token,
                "channelId"   => $channelId
            ];
            $AIAgentTemplateConfig       = new AIAgentTemplateConfig([
                "avatarChat3D" => new avatarChat3D($params),
                "visionChat"   => new visionChat($params),
                "voiceChat"    => new voiceChat($params)
            ]);
            $startAIAgentInstanceRequest = new GenerateAIAgentCallRequest([
                "AIAgentId"      => $AIAgentId,
                "templateConfig" => $AIAgentTemplateConfig,
                'expire'         => $time,
                'userId'         => $request->kernel->user()->username,
            ]);
            $startResponse               = $client->generateAIAgentCallWithOptions($startAIAgentInstanceRequest,
                $runtime);
            $body                        = $startResponse->body;

            $room = AudioRoom::create([
                'type'           => AudioRoom::ALIRTC,
                'user_id'        => $user->id,
                'room_id'        => $body->channelId,
                'auth_token'     => $body->token,
                'server_user_id' => $body->AIAgentUserId,
                'audio_user_id'  => $body->userId,
                'expiration_at'  => now()->addMinutes($timeMinutes),
                'score'          => 0,
                'ext'            => json_decode(base64_decode($body->token), true),
            ]);

            return $request->kernel->success([
                'agentUserId'    => $body->AIAgentUserId,
                'app_id'         => $appId,
                'user_id'        => $body->userId,
                'channel_id'     => $body->channelId,
                'token'          => $token,
                'rtc_token'      => $body->token,
                'timestamp'      => $time,
                'gslb'           => ['https://gw.rtn.aliyuncs.com'],
                'start_response' => $startResponse->body,
                'expiration_at'  => $room->expiration_at->toDateTimeString(),
            ]);
        } catch (Exception $error) {
            if (! ($error instanceof TeaError)) {
                $error = new TeaError([], $error->getMessage(),
                    $error->getCode(), $error);
            }
            return $request->kernel->error($error->getMessage());
        }
    }

    public function closeRoom(Request $request)
    {
        if (blank($request->instance_id)) {
            return $request->kernel->error('请传入instance_id');
        }
        $client                     = AudioRoom::getClient();
        $stopAIAgentInstanceRequest = new StopAIAgentInstanceRequest([
            "instanceId" => $request->instance_id,
        ]);
        $runtime                    = new RuntimeOptions([]);
        try {
            // 复制代码运行请自行打印 API 的返回值
            $client->stopAIAgentInstanceWithOptions($stopAIAgentInstanceRequest,
                $runtime);
            return $request->kernel->success([], '关闭成功');
        } catch (Exception $error) {
            if (! ($error instanceof TeaError)) {
                $error = new TeaError([], $error->getMessage(),
                    $error->getCode(), $error);
            }
            return $request->kernel->error($error->getMessage());
        }
    }

    public function realtimeToken(Request $request)
    {
        return $request->kernel->success([
            'auth_token' => $this->getToken(),
        ]);
    }

    public function videoResource(Request $request)
    {
        return $request->kernel->success([
            'talk'    => 'http://watecdn.watestar.com/upload/talk.mp4',
            'no_talk' => 'http://watecdn.watestar.com/upload/no_talk.mp4',
        ]);
    }

    public function getDescription(Request $request)
    {
        $json = [
            'rule_id'     => 1,
            'title'       => '小特',
            'description' => '您好，我的您的AI视觉助理，一个全新多模态大模型，目前我支持任意分辨率和极端长宽比图像识别与推理能力。我可以批改作业、图片数据抽取、图像分类、还可以根据图片生成图像故事。快来拍一张照片发给我试试吧！',
            'cover'       => 'https://watestar.oss-cn-hangzhou.aliyuncs.com/2024/12/28/5de5912295a1b5e45e302996c797f71d.jpeg',
            'count'       => 29,
            'score'       => 5,
            'list'        => [
                [
                    'icon'   => 'https://watestar.oss-cn-hangzhou.aliyuncs.com/app/icon/tongyong.svg',
                    'male'   => '通用',
                    'female' => '你的名字叫小特，你是一个通用的AI',
                ], [
                    'icon'   => 'https://watestar.oss-cn-hangzhou.aliyuncs.com/app/icon/shuiqian.svg',
                    'male'   => '睡前故事',
                    'female' => '你的名字叫小特，你是一个睡眠的AI',
                ], [
                    'icon'   => 'https://watestar.oss-cn-hangzhou.aliyuncs.com/app/icon/daoyou.svg',
                    'male'   => '导游',
                    'female' => '你的名字叫小特，你是一个导游的AI',
                ]
            ],
        ];
        return $request->kernel->success($json);
    }

    public function getRole(Request $request)
    {
        $user  = $request->kernel->user();
        $lists = AudioRole::ofEnabled()
            ->ordered()
            ->get();
        list($maxTime, $timeMinutes) = $this->getRtcCheck($user);
        $used            = AudioRoom::where('user_id', $user->id)
            ->whereDate('created_at', now()->toDateString())
            ->count();
        $aiCompanionUsed = AudioAiCompanion::ofUser($user)
            ->whereDate('created_at', now()->toDateString())
            ->count();
        $allUsed         = (int) bcadd($aiCompanionUsed, $used, 0);

        return $request->kernel->success([
            'day'          => (int) $maxTime,
            'used'         => $allUsed,
            'remaining'    => max((int) bcsub($maxTime, $allUsed, 0), 0),
            'time_minutes' => (int) $timeMinutes,
            'use_score'    => 0,
            'score'        => (string) $user->account->score,
            'role'         => [
                'name'        => '小特',
                'avatar'      => 'https://cdn.watestar.com/avatar/avatar_1.jpg',
                'description' => "**「视频通话」**：作为你的全能小助手，只要开启视频连线，我会紧盯画面**每个细节**！从**小学**的鸡兔同笼到**大学**微积分，你指着屏幕问，我立马拆**知识点**讲透～\n\n**「AI陪伴」**：你知道吗？我能切换好多种方言跟你唠嗑呢，像**四川话**的 “巴适得板”、**广东话**的 “你食咗饭未”，分分钟让你感觉像在跟老乡聊天～要是你想听点特别的，我还能秒变**夹子音**，软乎乎地跟你说 “宝子今天开心吗”，或者凑在 “耳边” 用**悄悄话**的语气讲小秘密呀～",
                'voice'       => [
                    'default_voice_id' => '	zh_female_shuangkuaisisi_emo_v2_mars_bigtts',
                    'name'             => '温暖阿虎/Alvin'
                ]
            ]
        ]);
    }

    public function createLyrics(Request $request)
    {
        $request->kernel->validate([
            'title' => 'required|max:50',
        ], [
            'title.required' => '请输入歌名',
            'title.max'      => '歌名最长:max个字符',
        ]);
        ignore_user_abort(true);
        set_time_limit(300);
        session_write_close();
        header('Content-Type: text/event-stream');
        header('Cache-Control: no-cache');
        header('Connection: keep-alive');
        header('X-Accel-Buffering: no');
        ob_implicit_flush(1);
        $result = Suno::tools()->createLyrics($request->title);
        foreach ($result->getIterator() as $chunk) {
            echo "data:".$chunk['message']."\n\n";
            ob_flush();
            flush();
            usleep(20000);
        }
    }

    private function createAuth(
        string $dsa,
        string $version,
        string $accessKey,
        string $secretKey,
        $region,
        int $expireSeconds
    ): string {
        if ($accessKey == "") {
            throw new Exception("invalid accessKey");
        }
        if ($secretKey == "") {
            throw new Exception("invalid secretKey");
        }
        $timestamp    = time() + $expireSeconds;
        $deadline     = gmdate("Ymd\THis\Z", $timestamp);
        $kDate        = hash_hmac('sha256', $deadline, $secretKey, true);
        $kRegion      = hash_hmac('sha256', $region, $kDate, true);
        $kService     = hash_hmac('sha256', "vod", $kRegion, true);
        $kCredentials = hash_hmac('sha256', "request", $kService, true);
        $dateKey      = bin2hex($kCredentials);
        $data         = $dsa."&".$version."&".$timestamp;
        switch ($dsa) {
            case "HMAC-SHA1":
                $sign = base64_encode(hash_hmac('sha1', $data, $dateKey, true));
                break;
            default:
                throw new Exception("invalid dsa");
        }
        return $dsa.":".$version.":".$timestamp.":".$accessKey.":".$sign;
    }
}
