<?php

namespace App\Http\Controllers\Api\AiChat;

use App\Http\Controllers\Controller;
use App\Http\Resources\Voice\AudioTtsResource;
use App\Http\Resources\VolcengineRtc\ConfigResource;
use App\Http\Resources\VolcengineRtc\VolcengineRoleResource;
use App\Models\AudioAiCompanion;
use App\Models\AudioRoom;
use App\Models\AudioVolcengineRole;
use App\Models\AudioVolcengineTts;
use App\Models\AudioVolcengineUserConfig;
use App\Models\User;
use App\Packages\Volcengine\RtcBase;
use App\Packages\Volcengine\Tools\RtcAccountToken;
use App\Packages\Volcengine\Volcengine;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Str;
use Illuminate\Validation\Rule;

class VolcengineRtcController extends Controller
{
    private string $appId = '';

    private string $appKey = '';

    public function __construct()
    {
        $this->appId  = env('VOLCENGINE_RTC_APPID');
        $this->appKey = env('VOLCENGINE_RTC_APPKEY');
    }

    public function setVoiceRole(Request $request)
    {
        $request->kernel->validate([
            'room_id' => 'nullable|exists:audio_rooms,room_id',
            'role_id' => 'required|exists:audio_volcengine_roles,id',
        ], [
            'room_id.exists'   => '房间不存在',
            'role_id.required' => '角色不能为空',
            'role_id.exists'   => '角色不存在',
        ]);

        $user = $request->kernel->user();
        $role = AudioVolcengineRole::find($request->role_id);
        if (blank($request->room_id)) {
            $config = AudioVolcengineUserConfig::setRoleConfig($user, $role);
            return $request->kernel->success([
                'config' => new ConfigResource($config)
            ], '设置角色成功');
        }
        $roomId = $request->room_id;
        $room   = AudioRoom::where('room_id', $roomId)->first();
        $userId = 'user_'.$user->id;
        $bootId = $room->ext['boot_id'] ?? Str::uuid()->toString();
        $taskId = Str::uuid()->toString();
        $config = AudioVolcengineUserConfig::setRoleConfig($user, $role);

        $params = $this->getApiParams(
            config: $config,
            roomId: $roomId,
            taskId: $taskId,
            bootId: $bootId,
            userId: $userId,
        );

        try {
            $result = $this->doStartVoiceChat($roomId, $taskId, $params);
            return $request->kernel->success([
                'config' => new ConfigResource($config)
            ], '设置角色成功');
        } catch (Exception $exception) {
            return $request->kernel->error($exception->getMessage());
        }
    }

    protected function getApiParams(
        AudioVolcengineUserConfig $config,
        string $roomId,
        string $taskId,
        string $bootId,
        string $userId,
        bool $vision = true
    ) {
        $AsrConfig      = [
            'Provider'          => 'volcano',
            'ProviderParams'    => [
                'Mode'        => 'smallmodel',
                'AppId'       => '**********',
                //'ApiResourceId' => 'volc.bigasr.sauc.concurrent',
                'AccessToken' => 'KWHuQZBapsQnhKcG9KRVEvG2rs1j63s7',
            ],
            'VADConfig'         => [
                'SilenceTime' => 600
            ],
            'InterruptConfig'   => [
                'InterruptSpeechDuration' => 500,
                'InterruptKeywords'       => [],
            ],
            'TurnDetectionMode' => 0
        ];
        $TTSConfig      = [
            'IgnoreBracketText' => [1, 2, 3, 4, 5],
            'Provider'          => 'volcano_bidirection',
            'ProviderParams'    => [
                'app'        => [
                    'appid' => '**********',
                    'token' => 'KWHuQZBapsQnhKcG9KRVEvG2rs1j63s7',
                ],
                'audio'      => [
                    'voice_type'  => $config->tts['audio']['voice_type'] ?? 'zh_female_meilinvyou_emo_v2_mars_bigtts',
                    'pitch_rate'  => $config->tts['audio']['pitch_rate'] ?? 0,//语调  -12  ~ 12
                    'speech_rate' => $config->tts['audio']['speech_rate'] ?? 0,//语速-50  ~ 100
                ],
                'Additions'  => [
                    'enable_latex_tn'          => true,
                    'disable_markdown_filter'  => true,
                    'enable_language_detector' => true,
                ],
                'ResourceId' => 'volc.service_type.10029',
            ],
        ];
        $LLMConfig      = [
            'Mode'                  => 'ArkV3',
            'EndPointId'            => match ($vision) {
                true => 'ep-20250607104213-6v9f8',
                false => 'ep-20250326135603-bgp9p',
            },
            'SystemMessages'        => [
                '非必要，请不要说从这些图片中，或者在图片中,或者这张图片中，你可以不说,如果有必要要说，请说画面中',
                $config->role->prompt
            ],
            'HistoryLength'         => 5,
            'Tools'                 => $this->getTools(),
            'Prefill'               => false,
            "TopP"                  => 0.8,
            'VisionConfig'          => match ($vision) {
                true => [
                    'Enable'         => true,
                    'SnapshotConfig' => $this->imageModel($config->image_model),
                ],
                false => [
                    'Enable' => false,
                ],
            },
            'FunctionCallingConfig' => [
                "ServerMessageUrl"       => "https://ai.api.watestar.com/api/function/call",
                "ServerMessageSignature" => "textFunctionCall"
            ],
        ];
        $SubtitleConfig = [
            "DisableRTSSubtitle" => false,
            "SubtitleMode"       => 1
        ];
        $AgentConfig    = [
            "TargetUserId"   => [$userId],
            "WelcomeMessage" => $config->role->prologue,
            "UserId"         => $bootId,
        ];

        return [
            'AppId'       => $this->appId,
            'RoomId'      => $roomId,
            'TaskId'      => $taskId,
            'Config'      => [
                'ASRConfig'             => $AsrConfig,
                'TTSConfig'             => $TTSConfig,
                'LLMConfig'             => $LLMConfig,
                'SubtitleConfig'        => $SubtitleConfig,
                'FunctionCallingConfig' => [
                    "ServerMessageUrl"       => "https://ai.api.watestar.com/api/function/call",
                    "ServerMessageSignature" => "textFunctionCall"
                ],
            ],
            'AgentConfig' => $AgentConfig,
        ];
    }

    private function getTools()
    {
        $data = [];
        foreach (config('mcp-server.voice-tools', []) as $tool) {
            $event  = new $tool();
            $data[] = [
                'type'     => 'function',
                'function' => [
                    'name'        => $event->name(),
                    'description' => $event->description(),
                    'parameters'  => $event->inputSchema()
                ]
            ];
        }
        return $data;
    }

    private function imageModel($imageModel)
    {
        return match ($imageModel) {
            'low' => [
                'StreamType'  => 0,
                'ImageDetail' => 'low',
                'Interval'    => 1000,
                'ImagesLimit' => 1,
            ],
            'normal' => [
                'StreamType'  => 0,
                'ImageDetail' => 'high',
                'Interval'    => 1000,
                'ImagesLimit' => 1,
            ],
            'high' => [
                'StreamType'  => 0,
                'ImageDetail' => 'high',
                'Interval'    => 500,
                'ImagesLimit' => 1,
            ],
            default => [
                'StreamType'  => 0,
                'ImageDetail' => 'low',//普通
                'Interval'    => 1000,
                'ImagesLimit' => 1,
            ],
        };
    }

    public function doStartVoiceChat(string $roomId, string $taskId, array $params)
    {
        $rtcRequest = Volcengine::rtc()->getRequest();
        $rtcRequest->setAction('StartVoiceChat')
            ->setVersion('2024-12-01')
            ->setAppId($this->appId)
            ->setRoomId($roomId)
            ->setTaskId($taskId)
            ->setParams($params);

        $RtcClient = new RtcBase();
        $RtcClient->setAccessKey(env('DOUBAO_APPID', ''));
        $RtcClient->setSecretKey(env('DOUBAO_SECRET', ''));
        $result = $RtcClient->startVoiceChat([
            'verify' => false,
            'query'  => $rtcRequest->getQueryBody(),
            'json'   => $rtcRequest->getParams(),
        ]);
        $result = json_decode($result, true);
        if (($result['Result'] ?? '') == 'ok') {
            $room = AudioRoom::where('room_id', $roomId)->first();
            if ($room) {
                $ext            = $room->ext ?? [];
                $ext['task_id'] = $params['TaskId'];
                $room->ext      = $ext;
                $room->save();
            }
            return $result;
        } else {
            $error = $result['ResponseMetadata']['Error'] ?? '';
            if (blank($error)) {
                $error = '未知错误';
            } elseif (Str::isJson($error)) {
                $errorData = json_decode($error, true);
                $error     = $errorData['Message'] ?? '未知错误';
            }
            throw new Exception($error);
        }
    }

    public function StartVoiceChat(Request $request)
    {
        $user = $request->kernel->user();

        $identity  = $user->identityFirst();
        $rtcNumber = $identity->getRules('rtc');
        $rtcTime   = $identity->getRules('rtc_time');
        $toDayUsed = $this->getUsedCount($user);
        if ($rtcNumber <= $toDayUsed) {
            return $request->kernel->error('实时音视频余次数不足：今日已使用【'.$toDayUsed.'】');
        }

        $userId = 'user_'.$user->id;
        $roomId = Str::uuid()->toString();
        $bootId = Str::uuid()->toString();
        $taskId = Str::uuid()->toString();
        $config = AudioVolcengineUserConfig::setImageModel($user, 'normal');
        $params = $this->getApiParams(
            config: $config,
            roomId: $roomId,
            taskId: $taskId,
            bootId: $bootId,
            userId: $userId,
        );
        try {
            $result = $this->doStartVoiceChat($roomId, $taskId, $params);

            $accountToken = new RtcAccountToken(
                $this->appId,
                $this->appKey,
                $roomId,
                $userId,
            );
            $accountToken->expireTime(0);
            $accountToken->addPrivilege(0, time() + 3600);
            $token = $accountToken->serialize();

            AudioRoom::updateOrCreate([
                'room_id' => $roomId,
            ], [
                'type'          => AudioRoom::VOLCENGINE,
                'user_id'       => $user->id,
                'role_id'       => $user->name,
                'score'         => 0,
                'voice'         => $config->tts['audio']['voice_type'] ?? '',
                'auth_token'    => $token,
                'audio_user_id' => $config->tts['audio']['voice_type'] ?? '',
                'expiration_at' => now()->addMinutes($rtcTime),
                'ext'           => [
                    'request_id' => empty($result['ResponseMetadata']['RequestId']) ? '' : $result['ResponseMetadata']['RequestId'],
                    'app_id'     => $this->appId,
                    'room_id'    => $roomId,
                    'task_id'    => $taskId,
                    'boot_id'    => $bootId,
                    'user_id'    => $userId,
                ]
            ]);
            return $request->kernel->success([
                'app_id'  => $this->appId,
                'app_key' => $this->appKey,
                'room_id' => $roomId,
                'task_id' => $taskId,
                'boot_id' => $bootId,
                'user_id' => $userId,
                'token'   => $token,
                'config'  => new ConfigResource($config),
            ], '房间创建成功');
        } catch (Exception $exception) {
            return $request->kernel->error($exception);
        }
    }

    private function getUsedCount(User $user)
    {
        $used            = AudioRoom::where('user_id', $user->id)
            ->whereDate('created_at', now()->toDateString())
            ->count();
        $aiCompanionUsed = AudioAiCompanion::ofUser($user)
            ->whereDate('created_at', now()->toDateString())
            ->count();
        return (int) bcadd($aiCompanionUsed, $used, 0);
    }

    public function setImageModel(Request $request)
    {
        $request->kernel->validate([
            'room_id'     => 'nullable|exists:audio_rooms,room_id',
            'image_model' => ['required', Rule::in(['low', 'normal', 'high'])],
        ], [
            'room_id.exists'       => '房间不存在',
            'image_model.required' => '模式不能为空',
            'image_model.in'       => '模式不正确',
        ]);
        $user = $request->kernel->user();
        if (blank($request->room_id)) {
            $config = AudioVolcengineUserConfig::setImageModel($user,
                $request->image_model,
            );
            return $request->kernel->success([
                'config' => new ConfigResource($config)
            ], '设置流畅度成功');
        }
        $roomId = $request->room_id;
        $room   = AudioRoom::where('room_id', $roomId)->first();
        $user   = $request->kernel->user();
        $userId = 'user_'.$user->id;
        $bootId = $room->ext['boot_id'] ?? Str::uuid()->toString();
        $taskId = Str::uuid()->toString();
        $config = AudioVolcengineUserConfig::setImageModel($user,
            $request->image_model,
        );
        $params = $this->getApiParams(
            config: $config,
            roomId: $roomId,
            taskId: $taskId,
            bootId: $bootId,
            userId: $userId,
        );

        try {
            $result = $this->doStartVoiceChat($roomId, $taskId, $params);
            return $request->kernel->success(new ConfigResource($config), '设置流畅度成功');
        } catch (Exception $exception) {
            return $request->kernel->error($exception->getMessage());
        }
    }

    public function setCamera(Request $request)
    {
        $request->kernel->validate([
            'room_id' => 'nullable|exists:audio_rooms,room_id',
            'switch'  => ['required', Rule::in(['on', 'off'])],
        ], [
            'room_id.exists'  => '房间不存在',
            'switch.required' => '请上传摄像头参数',
            'switch.in'       => '模式不正确',
        ]);
        $roomId = $request->room_id;
        $user   = $request->kernel->user();
        $config = AudioVolcengineUserConfig::getConfig($user);
        $room   = AudioRoom::where('room_id', $roomId)->first();
        $bootId = $room->ext['boot_id'] ?? Str::uuid()->toString();
        $taskId = Str::uuid()->toString();
        $userId = 'user_'.$user->id;

        $params = $this->getApiParams(
            config: $config,
            roomId: $roomId,
            taskId: $taskId,
            bootId: $bootId,
            userId: $userId,
            vision: match ($request->switch) {
                'on' => true,
                'off' => false,
                default => true,
            },
        );

        try {
            $result = $this->doStartVoiceChat($roomId, $taskId, $params);
            return $request->kernel->success([], '设置成功');
        } catch (Exception $exception) {
            return $request->kernel->error($exception->getMessage());
        }
    }

    public function getConfig(Request $request)
    {
        $user   = $request->kernel->user();
        $config = AudioVolcengineUserConfig::getConfig($user);
        return $request->kernel->success(new ConfigResource($config));
    }

    public function setVoiceTts(Request $request)
    {
        $request->kernel->validate([
            'room_id'     => 'nullable|exists:audio_rooms,room_id',
            'voice_type'  => 'required|exists:audio_volcengine_tts,voice_type',
            'pitch_rate'  => 'required|numeric|min:-12|max:12',
            'speech_rate' => 'required|numeric|min:-50|max:100',
        ], [
            'room_id.exists'       => '房间不存在',
            'voice_type.required'  => '声音模板不能为空',
            'voice_type.exists'    => '声音模板不存在',
            'pitch_rate.required'  => '音调不能为空',
            'pitch_rate.numeric'   => '音调必须是数字',
            'pitch_rate.min'       => '音调不能小于:min',
            'pitch_rate.max'       => '音调不能大于:max',
            'speech_rate.required' => '语速不能为空',
            'speech_rate.numeric'  => '语速必须是数字',
            'speech_rate.min'      => '语速不能小于:min',
            'speech_rate.max'      => '语速不能大于:max',
        ]);
        $user = $request->kernel->user();
        if (blank($request->room_id)) {
            $config = AudioVolcengineUserConfig::setTtsAudio($user,
                $request->voice_type,
                $request->pitch_rate,
                $request->speech_rate
            );
            return $request->kernel->success([
                'config' => new ConfigResource($config)
            ], '设置音色成功');
        }
        $roomId = $request->room_id;
        $room   = AudioRoom::where('room_id', $roomId)->first();
        $userId = 'user_'.$user->id;
        $bootId = $room->ext['boot_id'] ?? Str::uuid()->toString();
        $taskId = Str::uuid()->toString();
        $config = AudioVolcengineUserConfig::setTtsAudio($user,
            $request->voice_type,
            $request->pitch_rate,
            $request->speech_rate
        );
        $params = $this->getApiParams(
            config: $config,
            roomId: $roomId,
            taskId: $taskId,
            bootId: $bootId,
            userId: $userId,
        );

        try {
            $result = $this->doStartVoiceChat($roomId, $taskId, $params);
            return $request->kernel->success(new ConfigResource($config), '设置音色成功');
        } catch (Exception $exception) {
            return $request->kernel->error($exception->getMessage());
        }
    }

    public function StopVoiceChat(Request $request): JsonResponse
    {
        $request->kernel->validate([
            'room_id' => 'required|exists:audio_rooms,room_id',
        ], [
            'room_id.required' => '房间ID不能为空',
            'room_id.exists'   => '房间不存在',
        ]);

        $room      = AudioRoom::where('room_id', $request->room_id)->first();
        $RtcClient = new RtcBase();
        $RtcClient->setAccessKey(env('DOUBAO_APPID', ''));
        $RtcClient->setSecretKey(env('DOUBAO_SECRET', ''));
        $result = $RtcClient->stopVoiceChat([
            'verify' => false,
            'query'  => [
                'Action'  => 'StopVoiceChat',
                'Version' => '2024-12-01',
            ],
            'json'   => [
                'AppId'  => $this->appId,
                'RoomId' => $room->room_id,
                'TaskId' => $room->ext['task_id'],
            ],
        ]);
        $result = json_decode($result, true);
        if (($result['Result'] ?? '') == 'ok') {
            $room->update([
                'status' => AudioRoom::STATUS_OVER,
            ]);
            return $request->kernel->success([], '成功');
        } else {
            $error = $result['ResponseMetadata']['Error'] ?? '';
            if (blank($error)) {
                $error = '未知错误';
            } elseif (Str::isJson($error)) {
                $errorData = json_decode($error, true);
                $error     = $errorData['Message'] ?? '未知错误';
            }
            return $request->kernel->error($error);
        }
    }

    public function VoiceInterrupt(Request $request)
    {
        $request->kernel->validate([
            'room_id' => 'required|exists:audio_rooms,room_id',
        ], [
            'room_id.required' => '房间ID不能为空',
            'room_id.exists'   => '房间不存在',
        ]);
        $room      = AudioRoom::where('room_id', $request->room_id)->first();
        $RtcClient = new RtcBase();
        $RtcClient->setAccessKey(env('DOUBAO_APPID', ''));
        $RtcClient->setSecretKey(env('DOUBAO_SECRET', ''));
        $result = $RtcClient->updateVoiceChat([
            'verify' => false,
            'query'  => [
                'Action'  => 'UpdateVoiceChat',
                'Version' => '2024-12-01',
            ],
            'json'   => [
                'AppId'   => $this->appId,
                'RoomId'  => $request->room_id,
                'TaskId'  => $room->ext['task_id'],
                'Command' => 'interrupt',
            ],
        ]);
        $result = json_decode($result, true);
        if (($result['Result'] ?? '') == 'ok') {
            return $request->kernel->success([], '打断成功');
        } else {
            $error = $result['ResponseMetadata']['Error'] ?? '';
            if (blank($error)) {
                $error = '未知错误';
            } elseif (Str::isJson($error)) {
                $errorData = json_decode($error, true);
                $error     = $errorData['Message'] ?? '未知错误';
            } elseif (is_array($error)) {
                $error = $error['Message'];
            }
            return $request->kernel->error($error);
        }
    }

    public function getVoiceList(Request $request)
    {
        $list = AudioVolcengineTts::get();
        return $request->kernel->success(AudioTtsResource::collection($list));
    }

    public function getRoleList(Request $request)
    {
        $type = (int) ($request->type ?: '0');
        $list = AudioVolcengineRole::when($type === 1, function ($query) use ($request) {
            $user = $request->kernel->user();
            $query->where('user_id', $user->id);
        }, function ($query) {
            $query->where('user_id', 0);
            $query->where('is_websocket', 0);
        })->get();
        return $request->kernel->success(VolcengineRoleResource::collection($list));
    }

    public function getCompanionConfig(Request $request)
    {
        $user = $request->kernel->user();

        $identity  = $user->identityFirst();
        $rtcNumber = $identity->getRules('rtc');
        $rtcTime   = $identity->getRules('rtc_time');
        $toDayUsed = $this->getUsedCount($user);
        if ($rtcNumber <= $toDayUsed) {
            return $request->kernel->error('AI陪伴剩余次数不足：今日已使用【'.$toDayUsed.'】');
        }
        if (AudioAiCompanion::create([
            'user_id'    => $user->id,
            'expired_at' => now()->addMinutes($rtcTime),
        ])) {
            return $request->kernel->success([
                'message'         => '创建成功',
                'expired_at'      => now()->addMinutes($rtcTime)->toDateTimeString(),
                'expired_minutes' => (int) $rtcTime,
                'domain'          => env('AI_COMPANION_APP_DOMAIN', ''),
                'dialog_url'      => env('AI_COMPANION_APP_DIALOG_URI', ''),
                'app_id'          => env('AI_COMPANION_APP_ID', ''),
                'app_key'         => env('AI_COMPANION_APP_KEY', ''),
                'app_token'       => env('AI_COMPANION_APP_TOKEN', ''),
            ]);
        } else {
            return $request->kernel->error('意外创建失败');
        }
    }

    public function getRoleListSocket(Request $request)
    {
        $list = AudioVolcengineRole::where('is_websocket', 1)->get();

        return $request->kernel->success([
            'domain'     => env('AI_COMPANION_APP_DOMAIN', ''),
            'dialog_url' => env('AI_COMPANION_APP_DIALOG_URI', ''),
            'app_id'     => env('AI_COMPANION_APP_ID', ''),
            'app_key'    => env('AI_COMPANION_APP_KEY', ''),
            'app_token'  => env('AI_COMPANION_APP_TOKEN', ''),
            'roles'      => VolcengineRoleResource::collection($list),
        ]);
    }

    public function saveRole(Request $request)
    {
        $request->kernel->validate([
            'role_name'       => 'required|min:4',
            'voice_id'        => 'required|exists:audio_volcengine_tts,voice_type',
            'pitch_rate'      => 'required|integer|min:-12|max:12',
            'speech_rate'     => 'required|integer|min:-50|max:100',
            'description'     => 'required|string|max:100',
            'system_messages' => 'required|string',
            'prologue'        => 'required|string|max:30',
            'logo'            => 'required|url'
        ], [
            'role_name.required'       => '场景标题不能为空',
            'role_name.min'            => '场景标题不能少于:min个字',
            'voice_id.required'        => '请选择音色',
            'voice_id.exists'          => '音色不存在',
            'pitch_rate.required'      => '请输入语调',
            'pitch_rate.integer'       => '请输入正确的语调',
            'pitch_rate.min'           => '语调最小为:min',
            'pitch_rate.max'           => '语调最大为:max',
            'speech_rate.required'     => '请输入语速',
            'speech_rate.integer'      => '请输入正确的语速',
            'speech_rate.min'          => '语速最小为:min',
            'speech_rate.max'          => '语速最大为:max',
            'system_messages.required' => '请输入系统描述词',
            'system_messages.string'   => '请输入正确的系统描述词',
            'description.required'     => '请输入角色的描述',
            'description.string'       => '请输入正确的角色描述',
            'prologue.required'        => '请输入开场白',
            'prologue.max'             => '开场白最多:max个字符',
            'logo.required'            => '请上传头像',
            'logo.string'              => '请输入正确的头像地址',
            'logo.url'                 => '请输入正确的头像地址',
        ]);
        $user    = $request->kernel->user();
        $data    = [
            'role_name'   => $request->role_name,
            'logo'        => $request->logo,
            'description' => $request->description,
            'status'      => 1,
            'voice_id'    => $request->voice_id,
            'pitch_rate'  => $request->pitch_rate,
            'speech_rate' => $request->speech_rate,
            'prompt'      => $request->system_messages,
            'prologue'    => $request->prologue,
        ];
        $role    = AudioVolcengineRole::updateOrCreate([
            'user_id' => $user->id,
            'id'      => $request->id ?: null,
        ], $data);
        $message = '修改成功';
        if ($role->wasRecentlyCreated) {
            $message = '创建成功';
        }
        return $request->kernel->success(new VolcengineRoleResource($role), $message);
    }

    public function getRole(Request $request)
    {
        $request->kernel->validate([
            'id' => 'required'
        ], [
            'id.required' => '请选择要删除的角色'
        ]);
        $user = $request->kernel->user();
        $role = AudioVolcengineRole::where('id', $request->id)
            ->ofUser($user)->first();
        if ($role) {
            return $request->kernel->success(new VolcengineRoleResource($role));
        } else {
            return $request->kernel->error('角色不存在');
        }
    }

    public function deleteRole(Request $request)
    {
        $request->kernel->validate([
            'id' => 'required|exists:audio_volcengine_roles,id'
        ], [
            'id.required' => '请选择要删除的角色',
            'id.exists'   => '角色不存在'
        ]);
        $user = $request->kernel->user();
        if (AudioVolcengineRole::where('id', $request->id)
            ->ofUser($user)
            ->delete()) {
            return $request->kernel->success([], '删除成功');
        } else {
            return $request->kernel->error('角色不存在');
        }
    }

    private function getGreeting()
    {
        $hour = (int) date('H');

        $greeting    = [
            'morning'   => [
                '早上好！今天的天气很适合出门，有什么计划吗？',
                '早呀！咖啡和阳光哪个先到？新的一天要活力满满哦~',
                '清晨好时光，早餐吃什么？我最近发现了几家不错的早餐店。',
                '早安！有没有兴趣听听今日运势？或者聊聊你喜欢的星座？',
                '早啊！最近有没有看到什么有趣的短视频？分享一下呗~',
                '早上好！需要推荐几本提升效率的书吗？我最近在读《深度工作》。',
                '清晨快乐！通勤路上想听点什么？音乐、播客还是新闻？',
                '早安！今天想聊点什么？科技、美食还是旅行？',
                '早呀！有没有尝试过晨间冥想？我可以分享几个简单的方法。',
                '早上好！今天的穿搭灵感来自哪里？需要帮你搭配一下吗？',
                '早！有没有什么困扰你的小问题？或许我能帮你出出主意。',
                '早上好呀！最近有没有什么想学习的新技能？',
                '清晨的第一杯茶/咖啡，你更喜欢哪一种？',
                '早！有没有什么一直想做但没机会的小事？',
                '早上好！今天的日程安排如何？需要提醒你什么吗？',
                '早呀！如果今天有一个惊喜，你希望是什么？',
                '清晨时光，适合制定小目标~今天的重点任务是什么？',
                '早安！有没有什么冷知识想考考我？',
                '早！最近有没有发现什么好听的音乐？分享一下歌单吧~',
                '早上好！今天想聊点轻松的话题，还是深入探讨某个领域？'
            ],
            'forenoon'  => [
                '上午好！工作/学习进度如何？需要休息一下聊聊天吗？',
                '上午愉快！有没有遇到什么有趣的客户/同学？',
                '上午好！最近有没有关注什么行业动态？我可以帮你汇总信息。',
                '上午时光，来聊点有意思的吧！你最喜欢的电影类型是什么？',
                '上午好！有没有什么想吐槽的同事/同学？我保证不告诉别人~',
                '上午愉快！需要推荐一些提高效率的工具吗？',
                '上午好！今天的会议/课程多吗？',
                '上午时光，分享一个冷知识：章鱼有三个心脏~',
                '上午好！最近有没有尝试新的办公技巧？',
                '上午愉快！想不想讨论一下人工智能对未来工作的影响？',
                '上午好！有没有什么想了解的历史事件？我可以给你讲讲~',
                '上午时光，适合来点小确幸！你今天遇到的第一件开心事是什么？',
                '上午好！需要帮你查一下天气或交通情况吗？',
                '上午愉快！最近有没有发现什么好用的APP？',
                '上午好！工作间隙放松一下，你平时有什么减压小技巧？',
                '上午时光，来个小挑战！用三个词形容你的今天~',
                '上午好！有没有什么想聊的职场话题？比如晋升、人际关系...',
                '上午愉快！今天的午餐计划吃什么？需要推荐附近的餐厅吗？',
                '上午好！最近有没有什么想培养的好习惯？',
                '上午时光，分享一句你最近很喜欢的话吧~'
            ],
            'noon'      => [
                '中午好！午餐吃什么？有没有发现什么新的美食？',
                '中午愉快！午休时间打算怎么度过？小憩还是刷手机？',
                '中午好！今天的午餐有什么特别的吗？自己做的还是外卖？',
                '午餐时间，适合聊聊美食！你最喜欢的菜系是什么？',
                '中午好！有没有什么想分享的午餐趣事？',
                '中午愉快！午休准备做点什么？我推荐听点轻音乐放松一下~',
                '中午好！最近有没有尝试新的食谱？',
                '午餐时光，来个美食话题！你最难忘的一顿饭是什么时候？',
                '中午好！今天的午餐搭配有什么讲究吗？',
                '中午愉快！需要我帮你推荐一些健康的午餐选择吗？',
                '中午好！午餐后是散步还是继续工作？',
                '午餐时间，适合闲聊~你最想去哪个城市品尝美食？',
                '中午好！最近有没有发现什么好吃的零食？',
                '中午愉快！午休前要不要听个短笑话放松一下？',
                '中午好！今天的午餐有没有什么特别的故事？',
                '午餐时光，分享一个你最喜欢的餐厅吧~',
                '中午好！午休时间要不要一起做个简单的伸展运动？',
                '中午愉快！午餐后是喝咖啡还是喝茶？',
                '中午好！最近有没有什么想尝试的新菜式？',
                '午餐时间，适合聊点轻松的~你小时候最喜欢的食物是什么？'
            ],
            'afternoon' => [
                '下午好！下午的时光容易犯困，来聊点有趣的提提神吧~',
                '下午愉快！有没有什么想聊的？电影、音乐还是旅行？',
                '下午好！今天下午有什么重要的会议/任务吗？',
                '下午时光，来讨论点有深度的话题吧！比如科技伦理、社会现象...',
                '下午好！最近有没有什么想学习的新领域？',
                '下午愉快！需要我帮你查找一些资料吗？',
                '下午好！想不想了解一些有趣的历史冷知识？',
                '下午时光，适合分享经验！你最擅长的技能是什么？',
                '下午好！今天下午有没有什么期待的事情？',
                '下午愉快！最近有没有什么想吐槽的社会热点？',
                '下午好！工作/学习累了的话，我们来聊点轻松的吧~',
                '下午时光，来个思维小游戏！如果你有超能力，你希望是什么？',
                '下午好！有没有什么想了解的文化习俗？',
                '下午愉快！今天下午有没有什么计划？',
                '下午好！最近有没有什么想培养的兴趣爱好？',
                '下午时光，分享一个你最近的小成就吧~',
                '下午好！需要我帮你制定一个学习计划吗？',
                '下午愉快！今天下午的天气怎么样？适合做什么？',
                '下午好！想不想讨论一下未来的职业规划？',
                '下午时光，适合来点创意！如果你可以改变一件事，你会改变什么？'
            ],
            'evening'   => [
                '晚上好！今天过得如何？有没有什么有趣的事情发生？',
                '晚上愉快！晚餐吃了什么美味？自己做的还是出去吃的？',
                '晚上好！忙碌一天后，准备怎么放松一下？',
                '夜晚时光，适合聊聊生活~你理想中的周末是什么样的？',
                '晚上好！最近有没有看什么好电影或电视剧？',
                '晚上愉快！今天有没有什么想分享的小确幸？',
                '晚上好！想不想听听有趣的故事或奇闻轶事？',
                '夜晚来临，来聊聊兴趣爱好吧！你平时喜欢做什么？',
                '晚上好！最近有没有什么新的感悟或想法？',
                '晚上愉快！今天最开心的事情是什么？',
                '晚上好！准备怎么度过这个夜晚？',
                '夜晚时光，适合深入聊天~你最欣赏自己的哪一点？',
                '晚上好！有没有什么想聊的人生话题？比如梦想、目标...',
                '晚上愉快！今天有没有什么遗憾？明天打算怎么弥补？',
                '晚上好！需要我帮你推荐一本好书或一部好电影吗？',
                '夜晚宁静，来分享一下你最喜欢的一句话吧~',
                '晚上好！最近有没有什么想尝试的新活动？',
                '晚上愉快！今天的晚餐有没有什么特别的故事？',
                '晚上好！准备睡前读点书还是听点音乐？',
                '夜晚时光，适合回忆~分享一个你难忘的童年趣事吧~'
            ],
            'night'     => [
                '夜深了，今天过得充实吗？有没有什么想总结的？',
                '夜深人静，有没有什么心事想聊聊？我在这里倾听~',
                '夜深了，准备休息了吗？需要我推荐一些助眠的小技巧吗？',
                '夜晚宁静，来总结一下今天的收获和不足吧~',
                '夜深了，有没有什么想分享的回忆或经历？',
                '深夜时光，放松身心聊聊吧~你最难忘的旅行经历是什么？',
                '夜深了，今天有没有什么遗憾？明天打算怎么改进？',
                '夜晚已深，期待明天有什么计划？我可以帮你制定一个小目标~',
                '夜深了，愿你的明天充满阳光~今天有没有什么值得感恩的事情？',
                '深夜安好，今天有什么值得开心的事？分享一下吧~',
                '夜深了，准备休息前，要不要听个睡前小故事？',
                '深夜宁静，适合思考人生~你认为幸福是什么？',
                '夜深了，有没有什么想了解的知识领域？我可以给你科普一下~',
                '夜晚时光，适合放松~你平时有什么睡前习惯？',
                '夜深了，今天有没有什么新的发现或灵感？',
                '深夜安好，来聊点轻松的吧~你最喜欢的季节是什么？',
                '夜深了，准备休息了吗？明天有什么期待的事情？',
                '深夜宁静，分享一句你最近很有感触的话吧~',
                '夜深了，愿你的梦境甜美~你平时会做梦吗？有没有什么有趣的梦？',
                '深夜时光，适合反思~今天有没有什么想感谢的人或事？'
            ]
        ];
        $greetString = match ($hour) {
            5, 6, 7, 8 => Arr::random($greeting['morning'], 1),
            9, 10, 11 => Arr::random($greeting['forenoon'], 1),
            12, 13 => Arr::random($greeting['noon'], 1),
            14, 15, 16, 17 => Arr::random($greeting['afternoon'], 1),
            18, 19, 20, 21 => Arr::random($greeting['evening'], 1),
            default => Arr::random($greeting['night'], 1)
        };

        return $greetString[0];
    }
}
