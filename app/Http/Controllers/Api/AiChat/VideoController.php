<?php

namespace App\Http\Controllers\Api\AiChat;

use App\Exceptions\ValidatorException;
use App\Http\Controllers\Controller;
use App\Http\Resources\Draw\VideoResource;
use App\Models\DrawVideo;
use App\Models\DrawVideoSize;
use App\Models\DrawVideoStyle;
use App\Models\SystemConfig;
use Illuminate\Http\Request;

class VideoController extends Controller
{
    public function init(Request $request)
    {
        $quality = getKeyTitle(DrawVideo::QUALITY_ARRAY);
        $size    = DrawVideoSize::ofEnabled()
            ->ordered()
            ->select('id', 'name')
            ->get()
            ->toArray();
        $styles  = DrawVideoStyle::ofEnabled()
            ->ordered()
            ->select('id', 'name')
            ->get()
            ->toArray();
        return $request->kernel->success([
            'quality' => $quality,
            'sizes'   => $size,
            'styles'  => $styles,
        ]);
    }

    public function draw(Request $request)
    {
        $request->kernel->validate([
            'prompt'    => 'required',
            'image_url' => 'nullable|url',
            'style_id'  => 'nullable|exists:draw_video_styles,id',
        ], [
            'prompt.required' => '请输入描述内容',
            'image_url.url'   => '图片文件格式不正确',
        ]);
        $styleId = $request->style_id;
        $user    = $request->kernel->user();
        if ($user->account->score <= 5) {
            throw new ValidatorException('积分余额不足');
        }
        $quality = $request->quality ?: 'quality';
        $size    = $request->size_id ?: '';
        if ($size) {
            $size = DrawVideoSize::find($size);
        } else {
            $size = DrawVideoSize::ofEnabled()
                ->orderByDesc('is_default')
                ->orderByDesc('id')
                ->first();
        }
        $identity = $user->identities()->first();
        $isVip    = $identity->getRules('video_is_vip', 0);
        $score    = SystemConfig::getValue('draw_video', 0);
        $item     = DrawVideo::create([
            'model'      => 'cogvideox',
            'user_id'    => $user->id,
            'score'      => $score,
            'prompt'     => $request->prompt,
            'image_url'  => $request->image_url,
            'is_vip'     => $isVip,
            'style_id'   => $styleId,
            'size_id'    => $size->id,
            'with_audio' => $request->audio ? true : false,
            'quality'    => $quality,
            'params'     => [
                'style' => $styleId ? DrawVideoStyle::where('id', $styleId)
                    ->value('name') : '默认',
                'size'  => $size->getText(),
            ]
        ]);
        if ($item) {
            return $request->kernel->success(new VideoResource($item),
                '已排入制作队列');
        } else {
            return $request->kernel->error('创建任务失败');
        }
    }

    public function history(Request $request)
    {
        $user     = $request->kernel->user();
        $pagesize = $request->pagesize ?: 20;
        $lists    = DrawVideo::ofUser($user)
            ->orderByDesc('updated_at')
            ->paginate($pagesize);
        return $request->kernel->success([
            'data'         => $lists->map(function ($item) {
                return new VideoResource($item);
            }),
            'total'        => $lists->total(),
            'per_page'     => $lists->perPage(),
            'current_page' => $lists->currentPage(),
            'last_page'    => $lists->lastPage(),
        ]);
    }

    public function delete(Request $request)
    {
        $user = $request->kernel->user();
        $item = DrawVideo::ofUser($user)
            ->where('no', $request->no)
            ->first();
        if (! $item) {
            return $request->kernel->error('视频任务不存在');
        }
        if (! $item->canDelete()) {
            return $request->kernel->error('当前任务不可删除');
        }
        if ($item->delete()) {
            return $request->kernel->success([], '删除成功');
        } else {
            return $request->kernel->error('删除失败');
        }
    }

    public function result(Request $request)
    {
        $user = $request->kernel->user();
        $item = DrawVideo::ofUser($user)
            ->where('no', $request->no)
            ->first();
        if (! $item) {
            return $request->kernel->error('视频任务不存在');
        }
        if ($item->status == DrawVideo::STATUS_SUCCESS) {
            $data = new VideoResource($item);
            return $request->kernel->success(array_merge($data->toArray($request),
                [
                    'success' => 1,
                ]), '绘制完成');
        } elseif ($item->status == DrawVideo::STATUS_ERROR) {
            return $request->kernel->success([
                'success' => 2,
                'message' => $item->error_message,
            ], '绘制失败');
        } else {
            return $request->kernel->success([
                'success' => 0,
            ], '暂未完成');
        }
    }
}