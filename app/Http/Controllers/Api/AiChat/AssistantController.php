<?php

namespace App\Http\Controllers\Api\AiChat;

use App\Http\Controllers\Controller;
use App\Http\Resources\Assistant\AssistantLogsCollection;
use App\Http\Resources\Assistant\AssistantLogsResource;
use App\Http\Resources\Assistant\MemoryNodeCollection;
use App\Http\Resources\Assistant\MemoryNodeResource;
use App\Models\AudioXfSystemTimbre;
use App\Models\AudioXfTimbre;
use App\Models\BailianAssistant;
use App\Models\BailianAssistantNode;
use App\Models\BailianAssistantSetting;
use App\Models\ChatAssistant;
use App\Packages\Assistant\Assistant;
use App\Packages\BailianAssistant\BLAssistant;
use Exception;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;

class AssistantController extends Controller
{
    public function index(Request $request)
    {
        config([
            'app.debug' => false,
        ]);
        ignore_user_abort(true);
        set_time_limit(300);
        session_write_close();
        header('Content-Type: text/event-stream');
        header('Cache-Control: no-cache');
        header('Connection: keep-alive');
        header('X-Accel-Buffering: no');
        ob_implicit_flush(1);
        try {
            $request->kernel->validate([
                'message'  => 'required|string',
                'file_url' => 'nullable|url',
            ], [
                'message.required' => '消息不能为空',
                'message.string'   => '消息参数异常',
                'file_url.url'     => '文件地址',
            ]);
            $user  = $request->kernel->user();
            $group = ChatAssistant::firstOrCreate([
                'user_id' => $user->id
            ]);
            if ($group->wasRecentlyCreated) {
            }
            $log = null;
            if ($request->log_id) {
                $log = $group->logs()->find($request->log_id);
            }
            if (! $log) {
                $log = $group->logs()->create([
                    'user_id'  => $user->id,
                    'lat'      => $request->lat ?: '',
                    'lng'      => $request->lng ?: '',
                    'message'  => $request->message ?: '',
                    'file_url' => $request->file_url ?: '',
                ]);
            } else {
                $log->lat = $request->lat ?: '';
                $log->lng = $request->lng ?: '';
                $log->save();
            }
            $kernel = Assistant::kernel($log);
            $kernel->run();
        } catch (Exception $exception) {
            $request->kernel->sseError($exception->getMessage());
        }
    }

    public function bailian(Request $request)
    {
        config([
            'app.debug' => false,
        ]);
        if (function_exists('apache_setenv')) {
            apache_setenv('no-gzip', 1);
        }
        ini_set('output_buffering', 'off');
        ini_set('zlib.output_compression', false);
        ini_set('implicit_flush', true);
        while (ob_get_level() > 0) {
            ob_end_flush();
        }
        ob_start();
        ignore_user_abort(true);
        set_time_limit(0);
        session_write_close();
        header('Content-Type: text/event-stream');
        header('Cache-Control: no-cache');
        header('Connection: keep-alive');
        header('X-Accel-Buffering: no');
        ob_implicit_flush();
        try {
            $request->kernel->validate([
                'message'  => 'required|string',
                'file_url' => 'nullable|url',
            ], [
                'message.required' => '消息不能为空',
                'message.string'   => '消息参数异常',
                'file_url.url'     => '文件地址',
            ]);
            $user           = $request->kernel->user();
            $group          = BailianAssistant::firstOrCreate([
                'user_id' => $user->id
            ]);
            $knowledgeIds   = $request->knowledge_ids ?: '';
            $sessionFileIds = $request->session_file_ids ?: '';
            $imageUrl       = $request->image_url ?: '';
            $bailian        = new BLAssistant();
            $bailian->completion()
                ->setKnowledgeId($knowledgeIds)
                ->setSessionFileIds($sessionFileIds)
                ->setImageUrl($imageUrl)
                ->setOther([
                    'lat' => $request->lat,
                    'lng' => $request->lng,
                    'ip'  => $request->ip(),
                ])
                ->chat($request->message, $group);
        } catch (Exception $exception) {
            info($exception);
            $request->kernel->sseError($exception->getMessage());
        }
    }

    public function bailianLogs(Request $request)
    {
        $lastId = $request->last_id ?? '';
        $user   = $request->kernel->user();
        $group  = BailianAssistant::firstOrCreate([
            'user_id' => $user->id
        ]);
        $logs   = $group->logs()
            ->with([
                'actions',
                'files',
                'knowledges' => function ($query) {
                    $query->has('knowledge');
                }
            ])
            ->when($lastId, function (Builder $builder, $lastId) {
                $builder->where('id', '<', $lastId);
            })
            ->orderBy('created_at', 'desc')
            ->paginate($request->pagesize ?: 5);
        return $request->kernel->success(new AssistantLogsCollection($logs), '获取成功');
    }

    public function lastAsset(Request $request)
    {
        $user      = $request->kernel->user();
        $group     = BailianAssistant::firstOrCreate([
            'user_id' => $user->id
        ]);
        $lastAsset = $group->logs()
            ->where('ai_unify_asset_id', '>', 0)
            ->orderByDesc('id')
            ->first();
        return $request->kernel->success($lastAsset ? new AssistantLogsResource($lastAsset) : (object) []);
    }

    /**
     * 更新记忆片段
     *
     * @param  \Illuminate\Http\Request  $request
     * @return void
     */
    public function updateNode(Request $request)
    {
        $request->kernel->validate([
            'node_id' => 'required|string|exists:bailian_assistant_nodes,memory_node_id',
            'content' => 'required|string|max:50',
        ], [
            'node_id.required' => '记忆片段ID不能为空',
            'node_id.string'   => '记忆片段ID异常',
            'node_id.exists'   => '记忆片段ID异常',
            'content.required' => '记忆片段内容不能为空',
            'content.string'   => '记忆片段内容异常',
            'content.max'      => '记忆片段内容不能超过:max个字符',
        ]);
        $user = $request->kernel->user();
        $node = BailianAssistantNode::find($request->node_id);
        if ($node->memory->user_id != $user->id) {
            return $request->kernel->error('记忆片段异常');
        }
        try {
            $node->updateContent($request->post('content'));
            return $request->kernel->success(new MemoryNodeResource($node), '更新成功');
        } catch (Exception $exception) {
            return $request->kernel->error($exception->getMessage());
        }
    }

    public function createNode(Request $request)
    {
        $request->kernel->validate([
            'content' => 'required|string|max:50',
        ], [
            'content.required' => '记忆片段内容不能为空',
            'content.string'   => '记忆片段内容异常',
            'content.max'      => '记忆片段内容不能超过:max个字符',
        ]);
        try {
            $user  = $request->kernel->user();
            $group = BailianAssistant::firstOrCreate([
                'user_id' => $user->id
            ]);
            $node  = $group->createNode($request->post('content'));
            return $request->kernel->success(new MemoryNodeResource($node), '创建成功');
        } catch (Exception $exception) {
            return $request->kernel->error($exception->getMessage());
        }
    }

    public function setSetting(Request $request)
    {
        $user = $request->kernel->user();
        $attr = [
            'avatar',
            'voice_type',
            'voice_id',
            'font_size',
        ];
        $data = [];
        foreach ($attr as $item) {
            if ($request->$item) {
                $data[$item] = $request->$item;
            }
        }
        $data = array_filter($data);
        if (blank($data)) {
            return $request->kernel->error('没有设置任何参数');
        }
        $setting             = BailianAssistantSetting::updateOrCreate([
            'user_id' => $user->id,
        ], $data);
        $setting             = BailianAssistantSetting::find($setting->getKey());
        $setting->voice_name = match ($setting->voice_type) {
            'system' => AudioXfSystemTimbre::where('voice_id', $setting->voice_id)->value('people') ?? '',
            'custom' => AudioXfTimbre::where('voice_id', $setting->voice_id)->value('people') ?? '',
        };
        return $request->kernel->success($setting);
    }

    public function getSetting(Request $request)
    {
        $user                = $request->kernel->user();
        $timbre              = AudioXfSystemTimbre::ofEnabled()->first();
        $setting             = BailianAssistantSetting::firstOrCreate([
            'user_id' => $user->id,
        ], [
            'avatar'     => 'https://cdn.watestar.com/upload/default.png',
            'voice_type' => 'system',
            'voice_id'   => $timbre->voice_id,
            'font_size'  => '14',
        ]);
        $setting->voice_name = match ($setting->voice_type) {
            'system' => AudioXfSystemTimbre::where('voice_id', $setting->voice_id)->value('people') ?? '',
            'custom' => AudioXfTimbre::where('voice_id', $setting->voice_id)->value('people') ?? '',
        };
        return $request->kernel->success($setting);
    }

    /**
     * 清除上下文
     *
     * @param  \Illuminate\Http\Request  $request
     * @return mixed
     */
    public function disposal(Request $request)
    {
        $user  = $request->kernel->user();
        $group = BailianAssistant::where('user_id', $user->id)->first();
        if ($group) {
            try {
                $group->disposal();
            } catch (Exception $exception) {
                return $request->kernel->error('清空失败');
            }
        }
        return $request->kernel->success([], '清除成功');
    }

    /**
     * 获取记忆片段
     *
     * @return void
     */
    public function getNodes(Request $request)
    {
        $user  = $request->kernel->user();
        $group = BailianAssistant::where('user_id', $user->id)->first();
        $nodes = $group?->nodes()
            ->whereNull('type')
            ->when($request->keyword, function ($query, $keyword) {
                $query->where('content', 'like', "%{$keyword}%");
            })
            ->paginate($request->pagesize ?: 20) ?: collect([]);

        return $request->kernel->success(new MemoryNodeCollection($nodes));
    }

    /**
     * 刷新记忆片段
     *
     * @param  \Illuminate\Http\Request  $request
     * @return mixed
     */
    public function refreshNodes(Request $request)
    {
        $user  = $request->kernel->user();
        $group = BailianAssistant::where('user_id', $user->id)->first();
        if ($group) {
            $group->refreshNodeListSync();
            return $request->kernel->success([], '更新成功');
        } else {
            return $request->kernel->success([], '更新成功');
        }
    }

    /**
     * 删除某一个片段
     *
     * @param  \Illuminate\Http\Request  $request
     * @return mixed
     */
    public function deleteNode(Request $request)
    {
        $request->kernel->validate([
            'node_id' => 'required|string|exists:bailian_assistant_nodes,memory_node_id',
        ], [
            'node_id.required' => '记忆片段ID不能为空',
            'node_id.string'   => '记忆片段ID异常',
            'node_id.exists'   => '记忆片段ID异常',
        ]);
        $user = $request->kernel->user();
        $node = BailianAssistantNode::find($request->node_id);
        if ($node->memory->user_id != $user->id) {
            return $request->kernel->error('记忆片段异常');
        }
        try {
            $node->deleteContent();
            return $request->kernel->success(new MemoryNodeResource($node), '删除成功');
        } catch (Exception $exception) {
            return $request->kernel->error($exception->getMessage());
        }
    }

    /**
     * 清空记忆片段
     *
     * @param  \Illuminate\Http\Request  $request
     * @return mixed
     */
    public function cleanNode(Request $request)
    {
        try {
            $user = $request->kernel->user();

            $group = BailianAssistant::where('user_id', $user->id)->first();
            $nodes = $group?->nodes()
                ->whereNull('type')->get() ?: [];
            foreach ($nodes as $node) {
                try {
                    $node->deleteContent();
                } catch (Exception $exception) {
                    continue;
                }
            }
            return $request->kernel->success([], '清空成功');
        } catch (Exception $exception) {
            return $request->kernel->error($exception->getMessage());
        }
    }
}