<?php

namespace App\Http\Controllers\Api\AiChat;

use App\Exceptions\ValidatorException;
use App\Http\Controllers\Controller;
use App\Models\AppAiSearchGroup;
use App\Traits\CheckGroup;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;

class AppGroupController extends Controller
{
    use CheckGroup;

    public function index(Request $request)
    {
        $userId   = $request->kernel->id();
        $pageSize = $request->pagesize ?? 10;
        $page     = $request->page ?? 1;

        try {
            $query = AppAiSearchGroup::query()
                ->where('user_id', $userId)
                ->where('is_delete', 0)
                ->latest('id')
                ->select('id', 'title', 'cover', DB::raw("updated_at as updated_time"))
                ->latest('id');

            $list  = (clone $query)->forPage($page, $pageSize)->get();
            $count = (clone $query)->count();

            if ($list->isEmpty() && $page == 1) {
                $title = '新的会话';
                $group = AppAiSearchGroup::create([
                    'user_id' => $userId,
                    'title'   => $title,
                ]);

                $list  = collect([
                    [
                        'id'           => $group->id,
                        'title'        => $title,
                        'updated_time' => (string) $group->updated_at,
                    ]
                ]);
                $count = 1;
            }

            return $request->kernel->success([
                'list'  => $list,
                'count' => $count
            ]);
        } catch (Exception $e) {
            throw new ValidatorException("失败: ".$e->getMessage());
        }
    }

    public function create(Request $request)
    {
        $request->kernel->validate([
            'title' => 'required',
        ], [
            'title.required' => '请输入标题',
        ]);

        $userId = $request->kernel->id();

        $title   = $request->title ?? '';
        $groupId = $request->groupId ?? '';

        try {
            if ($groupId) {
                $group = AppAiSearchGroup::find($groupId);
                $this->checkGroup($group, $userId);

                $group->title = $title;
                $group->save();

                $message = '更新成功';
            } else {
                $group   = AppAiSearchGroup::create([
                    'user_id' => $userId,
                    'title'   => $title,
                ]);
                $groupId = $group->id;
                $message = '创建成功';
            }
            return $request->kernel->success([
                'id' => $groupId
            ], $message);
        } catch (Exception $e) {
            throw new ValidatorException("提交失败:".$e->getMessage());
        }
    }

    public function delete(Request $request)
    {
        $request->kernel->validate([
            'id' => 'required',
        ], [
            'id.required' => 'ID为空',
        ]);

        $userId  = $request->kernel->id();
        $groupId = $request->groupId ?? '';

        try {
            $group = AppAiSearchGroup::find($groupId);
            $this->checkGroup($group, $userId, false);
            $group->is_delete = 1;
            $group->save();
            return $request->kernel->success([], '删除成功');
        } catch (Exception $e) {
            throw new ValidatorException("删除失败:".$e->getMessage());
        }
    }

}