<?php

namespace App\Http\Controllers\Api\AiChat;

use App\Http\Controllers\Controller;
use Exception;
use GuzzleHttp\Client;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;

class ServiceController extends Controller
{
    public function volcengineStandalone(Request $request)
    {
        $appId       = '6684464157';
        $accessToken = 'KWHuQZBapsQnhKcG9KRVEvG2rs1j63s7';
        $secretKey   = 'FcDfxVRVV0BpXzyXI4wFbQCDfie9mXtO';
        return $request->kernel->success([
            'app_id'       => $appId,
            'access_token' => $accessToken,
            'secret_key'   => $secretKey,
        ]);
    }

    public function volcengineAudioToken(Request $request)
    {
        $appKey = 'qkmiGasial';
        if (! blank(Cache::get('volcengineAudioToken_'.$appKey, ''))) {
            return $request->kernel->success(Cache::get('volcengineAudioToken_'.$appKey));
        }
        $accessKey   = 'AKLTYWFjZTBhODJkY2UwNDA5OWEyNTNkMzQ3NzNlNTNkY2I';
        $secretKey   = 'TTJRMllUY3hOVGcyTkRObE5ESTBOR0kzWlRJNFpXUXpNRGhpWlRJM1pqWQ==';
        $host        = "open.volcengineapi.com";
        $authVersion = "volc-auth-v1";
        $region      = "cn-north-1";
        $service     = "sami";
        // token的有效期，不能超过一天
        $expiration = 36000;
        $requestStr = "request";
        $algorithm  = "HMAC-SHA256";
        $action     = "GetToken";
        $version    = "2021-07-27";
        $method     = "POST";
        $path       = "/";

        $query       = "Action=".$action."&Version=".$version;
        $url         = "https://".$host.$path."?".$query;
        $format_date = gmdate("Ymd\THis\Z");
        $date        = substr($format_date, 0, 8);
        $body        = array(
            "appkey"        => $appKey,
            "token_version" => $authVersion,
            "expiration"    => $expiration
        );
        $bodyObj     = json_encode($body);
        $bodyHash256 = hash("sha256", $bodyObj);
        $headers     = [
            "Host"             => $host,
            "Content-Type"     => 'application/json',
            "X-Date"           => $format_date,
            "X-Content-Sha256" => $bodyHash256
        ];
        ksort($headers);
        $signed_headersStr = "";
        $signed_str        = "";
        foreach ($headers as $x => $x_value) {
            $signed_headersStr = $signed_headersStr.";".strtolower($x);
            $signed_str        = $signed_str.strtolower($x).
                ":".$x_value."\n";
        }
        $signed_headersStr = substr($signed_headersStr, 1);
        $canoncial_request = $method."\n"
            .$path."\n"
            .$query."\n"
            .$signed_str."\n"
            .$signed_headersStr."\n"
            .$bodyHash256;
        $hashed_canon_req  = hash("sha256", $canoncial_request);
        $credential_scope  = $date."/"
            .$region."/"
            .$service."/"
            .$requestStr;
        $signing_str       = $algorithm."\n"
            .$format_date."\n"
            .$credential_scope."\n"
            .$hashed_canon_req;
        $kDate             = hash_hmac("sha256", $date, $secretKey, true);
        $kRegion           = hash_hmac("sha256", $region, $kDate, true);
        $kService          = hash_hmac("sha256", $service, $kRegion, true);
        $signing_key       = (hash_hmac("sha256", $requestStr, $kService, true));
        // 签名结果
        $sign                     = bin2hex(hash_hmac("sha256", $signing_str, $signing_key, true));
        $credential               = $accessKey."/".$credential_scope;
        $Authorization            = $algorithm." Credential=".$credential
            .", SignedHeaders=".$signed_headersStr
            .", Signature=".$sign;
        $headers['Authorization'] = $Authorization;
        try {
            $client   = new Client([
                'verify' => false,
            ]);
            $response = $client->request('POST', $url, [
                'headers' => $headers,
                'json'    => $body,
            ]);
            if ($response->getStatusCode() == 200) {
                $data = json_decode($response->getBody()->getContents(), true);
                if ($data['status_code'] == 20000000) {
                    $data['appkey'] = $appKey;
                    Cache::put('volcengineAudioToken_'.$appKey, $data, $expiration);
                    return $request->kernel->success($data);
                } else {
                    return $request->kernel->error($data['status_text']);
                }
            } else {
                return $request->kernel->error($response->getBody()->getContents());
            }
        } catch (Exception $exception) {
            return $request->kernel->error($exception->getMessage());
        }
    }
}