<?php

namespace App\Http\Controllers;

use App\Models\Pclogin;
use App\Models\User;
use Barryvdh\Debugbar\Facades\Debugbar;
use Closure;
use EasyWeChat\OfficialAccount\Message;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Modules\Socialite\Http\Controllers\Api\Wechat\Handles\EventMessage;
use Modules\Socialite\Http\Controllers\Api\Wechat\Handles\SubscribeMessage;
use Modules\Socialite\Http\Controllers\Api\Wechat\Handles\TextMessage;
use Modules\Socialite\Models\Wechat;
use Modules\Socialite\Models\WechatMessage;

class WechatController extends Controller
{
    public function serve(Request $request)
    {
        if (class_exists('Barryvdh\Debugbar\Facades\Debugbar')) {
            Debugbar::disable();
        }
        $app    = app('wechat.official_account');
        $server = $app->getServer();
        $server->with(function (Message $message, Closure $next) {
            WechatMessage::create([
                'message' => $message,
            ]);
            return $next($message);
        });

        $server->addEventListener('CLICK', function (Message $message) {
            return new EventMessage($message);
        });
        $server->addEventListener('subscribe', function (Message $message) {
            return new SubscribeMessage($message);
        });
        $server->addEventListener('SCAN', function (Message $message) use ($app) {
            $code     = $message['EventKey'];
            $openid   = $message['FromUserName'];
            $client   = $app->getClient();
            $response = $client->get('cgi-bin/user/info', [
                'query' => [
                    'openid' => $openid,
                    'lang'   => 'zh_CN',
                ]
            ]);
            if ($response->isSuccessful()) {
                $wechatUser = $response->toArray();
                $openid     = $wechatUser['openid'] ?? '';
                $unionid    = $wechatUser['unionid'] ?? '';
                if ($unionid) {
                    $wechat = Wechat::where('union_id', $unionid)->first();
                    if ($wechat) {
                        $wechat->wx_openid = $openid;
                        $wechat->save();
                    } else {
                        $wechat = Wechat::create([
                            'union_id'  => $unionid,
                            'wx_openid' => $openid,
                            'user_id'   => 0,
                        ]);
                    }
                } else {
                    $wechat = Wechat::updateOrCreate([
                        'wx_openid' => $openid,
                    ]);
                }
            }
            if (! $wechat) {
                return '创建微信用户信息失败';
            }
            if (Str::startsWith($code, 'bind')) {
                Pclogin::create([
                    'site_id' => 1,
                    'openid'  => $openid,
                    'code'    => $code,
                    'remark'  => '扫码成功'
                ]);
                return '账号绑定微信成功';
            } else {
                $user = $wechat->user;
                if (! $user->id) {
                    $user            = User::create([
                        'username' => $openid,
                    ]);
                    $wechat->user_id = $user->id;
                    $wechat->save();
                }
                Pclogin::create([
                    'site_id' => 1,
                    'user_id' => $user->id,
                    'openid'  => $openid,
                    'code'    => $code,
                    'remark'  => '登录成功'
                ]);
                return '恭喜您，登录成功！请回到网页继续使用';
            }
        });

        $server->addMessageListener('text', function (Message $message) {
            $text = new TextMessage($message);

            return $text;
        });
        $server->addMessageListener('image', function (Message $message) {
            return 'image';
        });
        $server->addMessageListener('voice', function (Message $message) {
            return 'voice';
        });
        $server->addMessageListener('video', function (Message $message) {
            return 'video';
        });
        $server->addMessageListener('shortvideo', function (Message $message) {
            return 'shortvideo';
        });
        $server->addMessageListener('location', function (Message $message) {
            return 'location';
        });
        $server->addMessageListener('link', function (Message $message) {
            return 'link';
        });
        $server->addMessageListener('file', function (Message $message) {
            return '文件';
        });
        $response = $server->serve();
        return $response;
    }
}