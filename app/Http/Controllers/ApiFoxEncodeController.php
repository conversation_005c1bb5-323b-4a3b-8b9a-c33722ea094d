<?php

namespace App\Http\Controllers;

use app\library\Aes;
use Exception;
use Illuminate\Http\Request;
use <PERSON>wan\Jwt\JwtToken;

class ApiFoxEncodeController extends Controller
{

    public function index(Request $request)
    {
        $appKey = env('APP_DEFAULT_KEY', '');
        try {
            $appToken = JwtToken::getExtend();
            $appKey   = $appToken['key'];
        } catch (Exception $e) {
        }
        $data = $request->all();
        $cmd  = $data['CMD'];
        unset($data['CMD']);
        $params['RequestBody'] = [
            'CMD' => $cmd,
            'DA'  => $data,
        ];
        $sign                  = md5(json_encode($params));
        $r                     = $this->encrypt(json_encode($params), $appKey);
        return [
            "rr"  => ($r),
            "rs"  => $sign,
            "TK"  => $request->header('authorization'),
            "key" => $appKey
        ];
        return json_encode($request->all());
    }

    protected function encrypt(string $string, string $appKey): string
    {
        // openssl_encrypt
        return base64_encode(openssl_encrypt($string, 'AES-256-ECB', $appKey, OPENSSL_RAW_DATA));
    }

    public function decode(Request $request)
    {
        $content = $request->input('Request');
        $appKey  = env('APP_DEFAULT_KEY', '');
        try {
            $appToken = JwtToken::getExtend();
            $appKey   = $appToken['key'];
        } catch (Exception $e) {
        }
        dd($this->decrypt($content, $appKey));
    }

    protected function decrypt(string $string, $appKey): string|false
    {
        $string = base64_decode($string);
        return openssl_decrypt($string, 'AES-256-ECB', $appKey, OPENSSL_RAW_DATA);
    }

}