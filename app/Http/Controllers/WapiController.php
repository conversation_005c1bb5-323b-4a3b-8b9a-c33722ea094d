<?php

namespace App\Http\Controllers;

use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Tinywan\Jwt\JwtToken;

class WapiController extends Controller
{
    public function index()
    {
        $version  = request()->get("version");
        $versions = ["v1"];
        if (! in_array($version, $versions)) {
            $version = "v1";
        }
        return view('version.v1');
    }

    public function save_generate(Request $request)
    {
        $post = $request->post();
        if (! empty($post['name'])) {
            return json(['code' => 1001, 'message' => "Upload parameters 格式不正确！"]);
        }
        DB::table("app_api_list")->insert([
            "name" => $post['apiname'],
            "json" => json_encode($post)
        ]);
        return json(['code' => 0, 'message' => "save Successes!"]);
    }

    public function get_generate()
    {
        $list = Db::table("app_api_list")->select("id","name")->get();
        if (empty($list)) {
            return json(['code' => 0, 'message' => "list empty!", 'data' => []]);
        }
        header('Content-Type: application/json; charset=utf-8');
        echo json_encode(['code' => 0, 'message' => "successes!", 'data' => $list]);
    }

    public function delete_generate(Request $request)
    {
        $id = request()->post("id", 0);
        DB::table("app_api_list")->where("id", $id)->delete();
    }

    public function get_generate_detail(Request $request)
    {
        $id   = $request->post("id", 0);
        $info = Db::table("app_api_list")->where("id", $id)->first();
        if (empty($info)) {
            echo json_encode(['code' => 0, 'message' => "list empty!", 'data' => []]);
        }

        try {
            $j = json_decode($info->json, true);
        } catch (Exception $e) {
            echo json_encode(['code' => 0, 'message' => "list empty2!", 'data' => []]);
        }
        echo json_encode(['code' => 0, 'message' => "list empty3!", 'data' => $j]);
    }

    public function generate(Request $request)
    {
        if ($request->method() == "GET") {
            exit("Please to login page.");
        }
        $post = $request->post();
        // 加密方法
        // 1.组装需要的对象,将对象话RequestBody里,这里需要JSON格式;
        // 2.DATA 为一个JSON对象
        $data["RequestBody"] = [
            "TimeSpan"   => time(),
            "Platform"   => $request->platform,
            "Device"     => $request->device,
            "IM"         => $request->imnumber,
            "APPVersion" => $request->appversion,
            "os"         => $request->os,
            "CMD"        => $request->cmd,
            "DA"         => $request->uploadparam,
        ];

        try {
            json_decode($data["RequestBody"]['DA'], true);
        } catch (Exception $e) {
            echo json_encode(['code' => 1001, 'message' => "Upload parameters 格式不正确！"]);
        }
        try {
            $appKey = env("APP_DEFAULT_KEY");
            $sign   = md5(json_encode($data));
            $tk     = request()->header('authorization');
            if (! empty($tk) && $tk != "Bearer") {
                $appToken = JwtToken::getExtend();
                $appKey   = $appToken['key'];
            }
            $request = $this->encrypt(json_encode($data), $appKey);
            echo json_encode([
                'code' => 0, 'message' => "", "data" => [
                    "Request" => ($request),
                    "Sign"    => $sign,
                    "TK"      => $post['token'],
                    "key"     => $appKey
                ]
            ]);
        } catch (Exception $e) {
            return ['code' => 1001, 'message' => $e->getMessage()];
        }
    }

    public function encrypt($data, $appKey)
    {
        return base64_encode(openssl_encrypt($data, 'AES-256-ECB', $appKey, OPENSSL_RAW_DATA));
    }

}
