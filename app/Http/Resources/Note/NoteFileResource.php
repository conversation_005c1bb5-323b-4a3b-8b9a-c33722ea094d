<?php

namespace App\Http\Resources\Note;

use App\Http\Resources\InteractionResourceTrait;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class NoteFileResource extends JsonResource
{
    use InteractionResourceTrait;

    public function toArray(Request $request)
    {
        return [
            'note_file_id' => $this->id,
            'hash'         => $this->storage->hash,
            'size'         => $this->storage->size,
            'type'         => $this->storage->type,
            'original'     => $this->storage->original,
            'path'         => $this->storage->path,
            'url'          => $this->storage->path_url,
            'knowledge'    => $this->when($this->knowledgeSet, function () {
                return [
                    'name'             => $this->knowledgeSet->knowledge->name,
                    'knowledge_id'     => $this->knowledgeSet->knowledge->id,
                    'knowledge_set_id' => $this->knowledgeSet->id,
                ];
            }, []),
            'created_at'   => (string) $this->created_at,
        ];
    }
}