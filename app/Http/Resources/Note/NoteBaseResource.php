<?php

namespace App\Http\Resources\Note;

use App\Http\Resources\InteractionResourceTrait;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class NoteBaseResource extends JsonResource
{
    use InteractionResourceTrait;

    public function toArray(Request $request)
    {
        return [
            'id'          => $this->id,
            'title'       => $this->getTitle(),
            'content'     => $this->content,
            'type'        => [
                'value' => $this->type,
                'text'  => $this->type_text
            ],
            'is_top'      => $this->is_top,
            'is_archived' => $this->is_archived,
            'tags'        => NoteTagBaseResource::collection($this->tags),
            'can'         => $this->getCan($request->kernel->user()),
            'created_at'  => (string) $this->created_at,
            'updated_at'  => (string) $this->updated_at,
        ];
    }
}