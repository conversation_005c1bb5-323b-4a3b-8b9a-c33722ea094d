<?php

namespace App\Http\Resources\Like;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class LikeBusinessResource extends JsonResource
{
    public function toArray(Request $request)
    {
        $like = $this->additional['like'];
        return [
            'id'           => $this->id,
            'cans'         => [
                'exchange' => true,
            ],
            'has'          => [
                'exchange' => true,
            ],
            'avatar'       => $this->avatar,
            'nickname'     => $this->nickname,
            'is_work'      => $this->is_work,
            'is_company'   => $this->is_company,
            'company_name' => $this->company_name,
            'create_time'  => $this->created_at->toDateTimeString(),
            'position'     => $this->position,
            'annex_name'   => $this->annex_name,
            'annex'        => $this->annex,
            'like_time'    => $like->created_at->toDateTimeString(),
        ];
    }
}