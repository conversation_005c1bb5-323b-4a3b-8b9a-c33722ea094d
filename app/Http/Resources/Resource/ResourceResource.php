<?php

namespace App\Http\Resources\Resource;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ResourceResource extends JsonResource
{
    public function toArray(Request $request)
    {
        return [
            'id'         => $this->id,
            'name'       => $this->name,
            'exp_time'   => $this->exp_time,
            'is_free'    => $this->is_free,
            'amount'     => $this->amount,
            'url'        => $this->cover_url,
            'is_open'    => $this->status,
            'created_at' => $this->created_at->toDateTimeString(),
        ];
    }
}