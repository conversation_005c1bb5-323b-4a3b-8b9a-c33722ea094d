<?php

namespace App\Http\Resources\Interaction;

use App\Http\Resources\BaseWateCollection;
use Modules\Interaction\Models\Follow;

class FansCollection extends BaseWateCollection
{
    public function toArray($request): array
    {
        return array_merge([
            'data' => $this->collection->map(function ($item) {
                return [
                    'user_id'         => $item->user->id,
                    'nickname'        => $item->user->info->nickname,
                    'avatar'          => $item->user->info->avatar_url,
                    'created_at'      => $item->created_at->toDateTimeString(),
                    'mutual_followed' => Follow::where('user_id', $item->followable_id)
                        ->ofItem($item->user)
                        ->exists(),
                    'interaction'     => [
                        'interaction_id'   => $item->user->getKey(),
                        'interaction_type' => $item->user->getMorphClass(),
                        'followed'         => Follow::where('user_id', $item->followable_id)
                            ->ofItem($item->user)
                            ->exists(),
                        'fans'             => 0,
                        'follow'           => 0,
                        'used'             => 0,
                        'like'             => 0,
                    ],
                ];
            })
        ], $this->page());
    }
}