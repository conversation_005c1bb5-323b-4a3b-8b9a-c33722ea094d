<?php

namespace App\Http\Resources\Account;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class OrderResource extends JsonResource
{
    public function toArray(Request $request)
    {
        return [
            "order_id"     => $this->paymentable_id,
            "total_fee"    => $this->amount,
            "out_trade_no" => $this->paymentable->getNo(),
            "create_time"  => $this->paymentable->created_at->toDateTimeString(),
            "pay_time"     => $this->paid_at?->toDateTimeString(),
            "nickname"     => $this->user->info->nickname,
            "num"          => $this->paymentable->getOrderNumber(),
            "type_name"    => $this->paymentable->getTitle()
        ];
    }
}