<?php

namespace App\Http\Resources\Bailian\Item;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class BailianKnowledgeItemNoAuthResource extends JsonResource
{
    /**
     * 将资源转换为数组
     *
     * @param  Request  $request
     * @return array
     */
    public function toArray(Request $request)
    {
        $user = '';
        return [
            'id'             => $this->id,
            'title'          => $this->title,
            'parent_id'      => $this->parent_id,
            'description'    => $this->description,
            'itemable'       => $this->getItemable(),
            'children_count' => $this->children_count,
            'download_url'   => $this->getDownloadUrl(),
            'can'            => $this->getCan($user),
            'order'          => $this->order,
            'created_at'     => (string) $this->created_at,
        ];
    }

    public function getType()
    {
        if ($this->itemable_type == 'bailian_article') {
            return [
                'value' => $this->itemable->type->value,
                'text'  => $this->itemable->type->toString(),
            ];
        }
        if ($this->itemable_type == 'bailian_file') {
            return [
                'value' => 'file',
                'text'  => '文档',
            ];
        }
        if ($this->itemable_type == 'bailian_directory') {
            return [
                'value' => 'directory',
                'text'  => '目录',
            ];
        }
        return [
            'value' => null,
            'text'  => null,
        ];
    }

    public function getItemable()
    {
        $data = [
            'title'         => $this->itemable->getTitle(),
            'itemable_type' => $this->itemable_type,
            'itemable_id'   => $this->itemable_id,
            'type'          => $this->getType(),
            'created_at'    => (string) $this->itemable->created_at,
            'size'          => $this->itemable->readable_size,
            'file_types'    => $this->itemable->getFileTypes(),
        ];

        return $data;
    }
}
