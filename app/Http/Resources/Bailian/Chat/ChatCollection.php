<?php

namespace App\Http\Resources\Bailian\Chat;

use App\Http\Resources\BaseWateCollection;
use Illuminate\Http\Request;

class ChatCollection extends BaseWateCollection
{
    public function toArray(Request $request)
    {
        return array_merge([
            'data' => $this->collection->map(function ($info) {
                return new ChatResource($info);
            }),
        ], $this->page());
    }
}
