<?php

namespace App\Http\Resources\Bailian\Article;

use App\Http\Resources\InteractionResourceTrait;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ChildrenResource extends JsonResource
{
    use InteractionResourceTrait;

    /**
     * Transform the resource into an array.
     *
     * @param  Request  $request
     * @return array
     */
    public function toArray(Request $request): array
    {
        return [
            'id'         => $this->id,
            'title'      => $this->title,
            'content'    => $this->content,
            'type'       => [
                'value' => $this->type->value,
                'text'  => $this->type->toString(),
            ],
            'created_at' => (string) $this->created_at,
        ];
    }
}
