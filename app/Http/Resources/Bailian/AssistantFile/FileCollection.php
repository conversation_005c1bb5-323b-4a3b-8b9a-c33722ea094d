<?php

namespace App\Http\Resources\Bailian\AssistantFile;

use App\Http\Resources\BaseWateCollection;
use Illuminate\Http\Request;

class FileCollection extends BaseWateCollection
{
    public function toArray(Request $request)
    {
        return array_merge([
            'data' => $this->collection->map(function ($info) {
                return new FileResource($info);
            }),
        ], $this->page());
    }
}
