<?php

namespace App\Http\Resources\Bailian\AssistantFile;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Modules\User\Http\Resources\UserBaseInfoResource;

class FileResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  Request  $request
     * @return array
     */
    public function toArray(Request $request): array
    {
        return [
            'id'          => $this->id,
            'name'        => $this->storage->original ?? $this->name,
            'file_id'     => $this->file_id,
            'group_id'    => $this->group_id,
            'user'        => $this->user ? new UserBaseInfoResource($this->user) : null,
            'status'      => [
                'value' => $this->status,
                'text'  => $this->status_text,
            ],
            'is_finished' => $this->isFinished(),
            'created_at'  => (string) $this->created_at,
        ];
    }
}
