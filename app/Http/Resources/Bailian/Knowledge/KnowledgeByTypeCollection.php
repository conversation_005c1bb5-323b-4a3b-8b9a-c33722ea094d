<?php

namespace App\Http\Resources\Bailian\Knowledge;

use App\Http\Resources\BaseWateCollection;
use Illuminate\Http\Request;

class KnowledgeByTypeCollection extends BaseWateCollection
{
    /**
     * 将资源集合转换为数组
     *
     * @param  Request  $request
     * @return array
     */
    public function toArray(Request $request)
    {
        $user = $request->kernel->user();
        return array_merge([
            'data' => $this->collection->map(function ($knowledge) use ($user) {
                return [
                    'id'                   => $knowledge->id,
                    'name'                 => $knowledge->name,
                    'bailian_knowledge_id' => $knowledge->knowledge_id,
                    'is_selected'          => $knowledge->isSelected($user),
                ];
            }),
        ], $this->page());
    }

}
