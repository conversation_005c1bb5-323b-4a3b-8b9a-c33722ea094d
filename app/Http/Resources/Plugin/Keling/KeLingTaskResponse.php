<?php

namespace App\Http\Resources\Plugin\Keling;

use App\Http\Resources\InteractionResourceTrait;
use App\Http\Resources\User\OtherBaseInfoResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class KeLingTaskResponse extends JsonResource
{
    use InteractionResourceTrait;

    public function toArray(Request $request)
    {
        return [
            'id'            => $this->id,
            'author'        => new OtherBaseInfoResource($this->user),
            'channel'       => $this->channel,
            'no'            => $this->no,
            'user_id'       => $this->user_id,
            'prompt'        => $this->prompt,
            'image'         => $this->imageUrlAttr,
            'task_id'       => $this->task_id,
            'cover'         => $this->coverUrlAttr,
            'video'         => $this->videoUrlAttr,
            'type'          => $this->type,
            'type_text'     => $this->type_text,
            'status'        => [
                'code' => $this->status,
                'text' => $this->status_text,
            ],
            'error_message' => $this->error_message,
            'cans'          => $this->cans(),
            'created_at'    => $this->created_at->toDateTimeString(),
            'overed_at'     => $this->over_at?->toDateTimeString() ?: '',
            'interaction'   => $this->getInteraction(),
        ];
    }
}