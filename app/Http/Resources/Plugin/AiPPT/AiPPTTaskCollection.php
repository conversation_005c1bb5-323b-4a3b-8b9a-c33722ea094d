<?php

namespace App\Http\Resources\Plugin\AiPPT;

use App\Http\Resources\BaseWateCollection;
use Illuminate\Http\Request;

class AiPPTTaskCollection extends BaseWateCollection
{
    public function toArray(Request $request)
    {
        return [
            'data' => $this->collection->map(function ($item) {
                return new AiPPtTaskResource($item);
            }),
            'page' => $this->page()
        ];
    }
}