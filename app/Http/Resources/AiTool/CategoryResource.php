<?php

namespace App\Http\Resources\AiTool;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class CategoryResource extends JsonResource
{
    public function toArray(Request $request)
    {
        return [
            'id'          => $this->id,
            'name'        => $this->name,
            'cover'       => $this->cover_url,
            'description' => $this->description,
            'created_at'  => (string) $this->created_at,
        ];
    }
}