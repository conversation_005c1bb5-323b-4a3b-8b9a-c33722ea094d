<?php

namespace App\Http\Resources\Assistant;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class AssistantActionResource extends JsonResource
{
    public function toArray(Request $request)
    {
        $data = json_encode($this->observation, JSON_UNESCAPED_UNICODE);
        $msg  = json_encode($this->arguments, JSON_UNESCAPED_UNICODE);
        if (in_array($this->action, ['rag', 'memory'])) {
            $data = [
                'arguments'   => $msg,
                'observation' => $data,
            ];
            $msg  = '';
        }
        return [
            'step'        => $this->step,
            'type'        => match ($this->action) {
                'rag', 'memory' => 'rag',
                'reasoning' => 'reasoning',
                default => 'mcp'
            },
            'action'      => $this->action,
            'action_name' => $this->action_name,
            'action_type' => $this->action_type,
            'msg'         => $this->message ?: $msg,
            'data'        => $data,
        ];
    }
}