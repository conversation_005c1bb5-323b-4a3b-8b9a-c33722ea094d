<?php

namespace App\Http\Resources\Assistant;

use App\Http\Resources\BaseWateCollection;
use Illuminate\Http\Request;

class MemoryNodeCollection extends BaseWateCollection
{
    public function toArray(Request $request)
    {
        return [
            'data' => $this->collection->map(function ($item) {
                return new MemoryNodeResource($item);
            }),
            'page' => $this->page(),
        ];
    }
}