<?php

namespace App\Http\Resources\User;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class AccountLogResource extends JsonResource
{
    public function toArray(Request $request)
    {
        return [
            'id'         => $this->id,
            'score'      => $this->amount > 0 ? '+ '.$this->amount : '- '.abs($this->amount),
            'remark'     => $this->source['remark'] ?? $this->rule->name,
            'created_at' => $this->created_at->toDateTimeString(),
            'expired_at' => $this->expired_at?->toDateTimeString() ?: '',
        ];
    }
}