<?php

namespace App\Http\Resources\OfflineTask;

use App\Http\Resources\BaseWateCollection;
use Illuminate\Http\Request;

class OfflineTaskListCollection extends BaseWateCollection
{
    public function toArray(Request $request)
    {
        return [
            'data' => $this->collection->map(function ($item) {
                return OfflineTaskResource::make($item);
            }),
            'page' => $this->page(),
        ];
    }
}