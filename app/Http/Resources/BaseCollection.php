<?php

namespace App\Http\Resources;

use Illuminate\Http\Resources\Json\ResourceCollection;

class BaseCollection extends ResourceCollection
{
    protected function page(): array
    {
        return [
            'current'    => $this->currentPage(),
            'total_page' => $this->lastPage(),
            'per_page'   => $this->perPage(),
            'has_more'   => $this->hasMorePages(),
            'total'      => $this->total(),
        ];
    }
}
