<?php

namespace App\Http\Resources\VolcengineRtc;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class VolcengineRoleResource extends JsonResource
{
    /**
     * Transform the resource into an array.
     *
     * @param  \Illuminate\Http\Request  $request
     * @return array
     */
    public function toArray(Request $request)
    {
        return [
            'id'                    => $this->id,
            'voice_type'            => $this->voice_id,
            'voice_name'            => $this->voice?->name ?: '',
            'voice_audio_url'       => $this->voice?->audio_url ?: '',
            'role_name'             => $this->role_name,
            'logo'                  => $this->logo,
            'description'           => $this->description,
            'pitch_rate'            => $this->pitch_rate,
            'speech_rate'           => $this->speech_rate,
            'prompt'                => $this->prompt,
            'prologue'              => $this->prologue,
            'is_websocket'          => $this->is_websocket,
            'default_role_audio'    => $this->default_role_audio,
            'status'                => (int) $this->status,
        ];
    }
}
