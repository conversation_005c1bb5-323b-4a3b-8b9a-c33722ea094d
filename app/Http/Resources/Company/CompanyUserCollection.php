<?php

namespace App\Http\Resources\Company;

use App\Http\Resources\BaseWateCollection;
use Illuminate\Http\Request;

class CompanyUserCollection extends BaseWateCollection
{
    public function toArray(Request $request)
    {
        return array_merge($this->page(), [
            'data' => $this->collection->map(function ($companyUser) {
                return [
                    'id'           => $companyUser->id,
                    'uid'          => $companyUser->uid,
                    'nickname'     => $companyUser->nickname,
                    'position'     => $companyUser->position,
                    'phone'        => $companyUser->business->phone,
                    'wechat'       => $companyUser->business->wechat,
                    'created_time' => $companyUser->created_at,
                    'type_name'    => $companyUser->type_name,
                    'business'     => $companyUser->business,
                ];
            }),
        ]);
    }
}