<?php

namespace App\Http\Resources\FastGpt\Article;

use App\Http\Resources\FastGpt\KnowledgeSetBaseInfoResource;
use App\Http\Resources\InteractionResourceTrait;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class ArticleBaseResource extends JsonResource
{
    use InteractionResourceTrait;

    public function toArray(Request $request)
    {
        return [
            'id'         => $this->id,
            'title'      => $this->title,
            'set'        => new KnowledgeSetBaseInfoResource($this->knowledgeSet),
            'can'        => $this->getCan($request->kernel->user()),
            'created_at' => (string) $this->created_at,
        ];
    }
}