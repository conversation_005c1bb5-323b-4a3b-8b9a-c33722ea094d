<?php

namespace App\Http\Resources\FastGpt;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class KnowledgeChatResource extends JsonResource
{
    public function toArray(Request $request)
    {
        return [
            'chatId'      => $this->chat_id,
            'appId'       => $this->app->app_id,
            'title'       => $this->title,
            'customTitle' => $this->custom_title,
            'top'         => (bool) $this->is_top,
            'updateTime'  => (string) $this->updated_at,
        ];
    }
}