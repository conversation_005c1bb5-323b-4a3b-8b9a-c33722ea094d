<?php

namespace App\Http\Resources\FastGpt;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class KnowledgeSetResource extends JsonResource
{
    public function toArray(Request $request)
    {
        return [
            'id'              => $this->id,
            'name'            => $this->name,
            'collection_id'   => $this->collection_id,
            'dataset_id'      => $this->knowledge->dataset_id,
            'type'            => [
                'value' => $this->type->value,
                'text'  => $this->type->toString(),
            ],
            'input_data'      => $this->input_data,
            'training_amount' => $this->training_amount,
            'created_at'      => (string) $this->created_at,
        ];
    }
}