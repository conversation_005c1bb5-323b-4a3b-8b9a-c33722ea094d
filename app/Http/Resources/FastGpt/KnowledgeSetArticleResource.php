<?php

namespace App\Http\Resources\FastGpt;

use App\Http\Resources\FastGpt\Article\ArticleBaseInteractionResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Modules\User\Http\Resources\UserBaseInfoResource;

class KnowledgeSetArticleResource extends JsonResource
{
    public function toArray(Request $request)
    {
        return [
            'knowledge_set_id' => $this->id,
            'name'             => $this->name,
            'knowledge'        => [
                'knowledge_id' => $this->knowledge->id ?? '',
                'name'         => $this->knowledge->name ?? '',
                'user'         => new UserBaseInfoResource($this->knowledge->user),
            ],
            'article'          => new ArticleBaseInteractionResource($this->knowledgeArticle),
            'created_at'       => (string) $this->created_at,
        ];
    }
}