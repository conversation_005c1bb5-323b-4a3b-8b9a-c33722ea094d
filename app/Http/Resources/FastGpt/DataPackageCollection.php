<?php

namespace App\Http\Resources\FastGpt;

use App\Http\Resources\BaseWateCollection;

class DataPackageCollection extends BaseWateCollection
{
    public function toArray($request): array
    {
        return array_merge([
            'data' => $this->collection->map(function ($item) {
                return [
                    'data_id'      => $item['_id'],
                    'datasetId'    => $item['datasetId'],
                    'collectionId' => $item['collectionId'],
                    'q'            => $item['q'],
                    'a'            => $item['a'],
                ];
            })
        ], $this->page());
    }
}