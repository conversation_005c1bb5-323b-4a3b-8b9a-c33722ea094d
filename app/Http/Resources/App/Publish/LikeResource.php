<?php

namespace App\Http\Resources\App\Publish;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class LikeResource extends JsonResource
{
    public function toArray(Request $request)
    {
        return [
            'id'         => $this->id,
            'title'      => $this->likeable->getInteractionTitle(),
            'cover'      => $this->likeable->getInteractionCover(),
            'target'     => $this->likeable->getInteractionTarget(),
            'created_at' => $this->created_at->toDateTimeString(),
        ];
    }
}