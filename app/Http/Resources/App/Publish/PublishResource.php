<?php

namespace App\Http\Resources\App\Publish;

use App\Http\Resources\InteractionResourceTrait;
use App\Http\Resources\User\OtherBaseInfoResource;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class PublishResource extends JsonResource
{
    use InteractionResourceTrait;

    public function toArray(Request $request)
    {
        return [
            'id'          => $this->id,// id
            'author'      => new OtherBaseInfoResource($this->user),// 作者
            'category'    => $this->category->name,// 分类
            'tags'        => $this->tags ?: [],// 标签
            'title'       => $this->title,// 标题
            'description' => $this->description ?: '',// 描述
            'cover'       => $this->coverUrlAttr,// 封面
            'video'       => $this->videoUrlAttr,// 视频
            'pictures'    => $this->pictureUrlAttr,// 图集 数组
            'audio'       => $this->audioUrlAttr,// 音频
            'type'        => $this->type,// 类型
            'type_text'   => $this->type_text,// 类型
            'ext'         => $this->ext ?: (object) [],// 扩展字段
            'publish_at'  => $this->created_at->toDateTimeString(),// 发布时间
            'interaction' => $this->getInteraction(),// 互动
            'task'        => [
                'type'      => $this->item->type,
                'type_text' => $this->item->type_text,
                'prompt'    => $this->item->prompt,
                'size'      => $this->item->getSize(),
            ],
            'share'       => [
                'url' => route('app.Publish.Share', $this),
            ]
        ];
    }
}