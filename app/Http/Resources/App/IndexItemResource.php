<?php

namespace App\Http\Resources\App;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class IndexItemResource extends JsonResource
{
    public function toArray(Request $request)
    {
        return [
            'id'      => $this->id,
            'user_id' => $this->user_id,
            'type'    => $this->target->getLogType(),
            'message' => [
                'user'   => $this->target->getUserMessage(),
                'system' => $this->target->getSystemMessage(),
            ],
            'cans'    => $this->target->cans(),
            'extend'  => $this->target->getExtend(),
        ];
    }
}