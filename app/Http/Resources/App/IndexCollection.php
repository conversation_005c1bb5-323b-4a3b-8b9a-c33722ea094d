<?php

namespace App\Http\Resources\App;

use App\Http\Resources\BaseWateCollection;
use Illuminate\Http\Request;

class IndexCollection extends BaseWateCollection
{
    public function toArray(Request $request)
    {
        return [
            'data' => $this->collection->map(function ($item) {
                return $item->getAssetResource();
            })->toArray(),
            'page' => $this->page(),
        ];
    }
}