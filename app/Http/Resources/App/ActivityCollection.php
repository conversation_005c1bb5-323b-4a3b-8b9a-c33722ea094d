<?php

namespace App\Http\Resources\App;

use App\Http\Resources\BaseWateCollection;
use Illuminate\Http\Request;

class ActivityCollection extends BaseWateCollection
{
    public function toArray(Request $request)
    {
        return [
            'data' => $this->collection->map(function ($item) {
                return new ActivityListResource($item);
            }),
            'page' => $this->page(),
        ];
    }
}