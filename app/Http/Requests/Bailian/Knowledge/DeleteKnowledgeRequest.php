<?php

namespace App\Http\Requests\Bailian\Knowledge;

use App\Http\Requests\BaseFormRequest;

class DeleteKnowledgeRequest extends BaseFormRequest
{
    /**
     * 验证规则
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            'knowledge_id' => 'required|exists:bailian_knowledge,id',
        ];
    }

    /**
     * 错误消息
     *
     * @return array
     */
    public function messages(): array
    {
        return [
            'knowledge_id.required' => '知识库ID不能为空',
            'knowledge_id.exists'   => '知识库不存在',
        ];
    }
}
