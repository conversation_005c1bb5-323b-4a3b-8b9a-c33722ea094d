<?php

namespace App\Http\Requests\Bailian\Article;

use Illuminate\Foundation\Http\FormRequest;

class AddToKnowledgeRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'knowledge_id' => 'required|exists:bailian_knowledge,id',
            'article_id'   => 'required|exists:bailian_articles,id',
        ];
    }

    public function messages(): array
    {
        return [
            'knowledge_id.required' => '知识库id不能为空',
            'knowledge_id.exists'   => '知识库不存在',
            'article_id.required'   => '笔记id不能为空',
            'article_id.exists'     => '笔记不存在',
        ];
    }

}
