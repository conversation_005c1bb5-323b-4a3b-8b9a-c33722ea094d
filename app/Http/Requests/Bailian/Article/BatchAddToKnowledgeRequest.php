<?php

namespace App\Http\Requests\Bailian\Article;

use Illuminate\Foundation\Http\FormRequest;

class BatchAddToKnowledgeRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     *
     * @return bool
     */

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array
     */
    public function rules(): array
    {
        return [
            'article_ids'       => ['required', 'array'],
            'article_ids.*'     => ['required', 'exists:bailian_articles,id'],
            'knowledge_id'      => ['required', 'exists:bailian_knowledge,id'],
            'knowledge_item_id' => ['nullable', 'exists:bailian_knowledge_items,id'],
        ];
    }

    /**
     * Get custom messages for validator errors.
     *
     * @return array
     */
    public function messages(): array
    {
        return [
            'article_ids.required'     => '笔记ID列表不能为空',
            'article_ids.array'        => '笔记ID列表格式不正确',
            'article_ids.*.required'   => '笔记ID不能为空',
            'article_ids.*.exists'     => '笔记不存在',
            'knowledge_id.required'    => '知识库ID不能为空',
            'knowledge_id.exists'      => '知识库不存在',
            'knowledge_item_id.exists' => '知识库条目不存在',
        ];
    }
} 