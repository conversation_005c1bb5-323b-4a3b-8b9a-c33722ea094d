<?php

namespace App\Http\Requests\Bailian;

use Illuminate\Foundation\Http\FormRequest;

class UrlToTextRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'url' => 'required|string|url',
        ];
    }

    public function messages(): array
    {
        return [
            'url.required' => 'url地址不能为空',
            'url.string'   => 'url地址必须为字符串',
            'url.url'      => 'url地址必须为url地址',
        ];
    }

}
