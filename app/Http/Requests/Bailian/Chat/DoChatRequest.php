<?php

namespace App\Http\Requests\Bailian\Chat;

use App\Models\SystemConfig;
use Illuminate\Foundation\Http\FormRequest;

class DoChatRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'message'               => [
                'required',
                'string',
                'max:255',
            ],
            'bailian_knowledge_ids' => [
                'nullable',
                'string',
                function ($attribute, $value, $fail) {
                    $ids                              = explode(',', $value);
                    $max_bailian_knowledge_chat_count = SystemConfig::getValue('max_bailian_knowledge_chat_count', 5);
                    if (count($ids) > $max_bailian_knowledge_chat_count) {
                        $fail("最多只能选择{$max_bailian_knowledge_chat_count}个知识库");
                    }
                }
            ],
            'session_file_ids'      => [
                'nullable',
                'string',
            ],

        ];
    }

    public function messages(): array
    {
        return [
            'message.required'               => '请输入消息内容',
            'message.string'                 => '消息内容必须是字符串',
            'message.max'                    => '消息内容不能超过255个字符',
            'bailian_knowledge_ids.required' => '请选择知识库',
            'bailian_knowledge_ids.string'   => '知识库必须是字符串',
            'session_file_ids.string'        => '请选择文件',
        ];
    }

}
