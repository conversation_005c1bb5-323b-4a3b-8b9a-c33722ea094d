<?php

namespace App\Http\Requests\Note;

use Illuminate\Foundation\Http\FormRequest;

class FileMoveKnowledgeRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'knowledge_id' => 'required|integer|exists:fastgpt_knowledge,id',
            'note_file_id' => 'required|integer|exists:note_files,id',
            'name'         => 'required',
        ];
    }

    public function messages(): array
    {
        return [
            'knowledge_id.required' => '缺少知识库id',
            'knowledge_id.integer'  => '知识库id必须是数字',
            'knowledge_id.exists'   => '知识库不存在',
            'note_id.required'      => '缺少附件id',
            'note_id.integer'       => '附件id必须是数字',
            'note_id.exists'        => '附件不存在',
            'name.required'         => '缺少标题',
        ];
    }

}
