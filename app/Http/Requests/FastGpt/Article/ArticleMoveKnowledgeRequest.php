<?php

namespace App\Http\Requests\FastGpt\Article;

use Illuminate\Foundation\Http\FormRequest;

class ArticleMoveKnowledgeRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'knowledge_id'       => 'required|integer|exists:fastgpt_knowledge,id',
            'fastgpt_article_id' => 'required|integer|exists:fastgpt_knowledge_articles,id'
        ];
    }

    public function messages(): array
    {
        return [
            'knowledge_id.required'       => '缺少知识库id',
            'knowledge_id.integer'        => '知识库id必须是数字',
            'knowledge_id.exists'         => '知识库不存在',
            'fastgpt_article_id.required' => '缺少文档id',
            'fastgpt_article_id.integer'  => '文档id必须是数字',
            'fastgpt_article_id.exists'   => '文档不存在',
        ];
    }

}
