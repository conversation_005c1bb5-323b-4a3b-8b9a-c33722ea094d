<?php

namespace App\Http\Requests\Department;

use Illuminate\Foundation\Http\FormRequest;

class AddDepartmentRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'parent_id'           => 'nullable|integer',
            'name'                => 'required|bail|min:2|max:32',
            'is_business'         => 'required|boolean',
            'is_assistant'        => 'required|boolean',
            'is_update_knowledge' => 'required|boolean',
            'is_watermark'        => 'required|boolean',
            'is_download'         => 'required|boolean',
            'is_password'         => 'required|boolean',
        ];
    }

    public function messages(): array
    {
        return [
            'name.required'                => '部门名称必须填写',
            'name.min'                     => '部门名称最少:min字符',
            'name.max'                     => '部门名称最大:max字符',
            'is_business.required'         => '缺少是否开启名片',
            'is_assistant.required'        => '缺少是否开启私人助理',
            'is_update_knowledge.required' => '缺少是否部门内所有成员可更新知识库',
            'is_watermark.required'        => '缺少是否开启水印预览',
            'is_download.required'         => '缺少否允许下载、打印知识库文档',
            'is_password.required'         => '缺少是否查看知识库文档需要密码',
        ];
    }

}
