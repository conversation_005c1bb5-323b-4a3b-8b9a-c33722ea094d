<?php

namespace App\Events;

use App\Models\BailianKnowledge;
use App\Models\User;
use Illuminate\Foundation\Events\Dispatchable;
use Illuminate\Queue\SerializesModels;

class KnowledgeContentUpdatedEvent
{
    use Dispatchable, SerializesModels;

    public BailianKnowledge $knowledge;
    public string           $updateType;
    public string           $itemTitle;
    public User             $updater;

    /**
     * 创建知识库内容更新事件
     *
     * @param  BailianKnowledge  $knowledge  知识库
     * @param  string  $updateType  更新类型：created, updated, deleted
     * @param  string  $itemTitle  更新项目的标题
     * @param  User  $updater  更新者
     */
    public function __construct(BailianKnowledge $knowledge, string $updateType, string $itemTitle)
    {
        $this->knowledge  = $knowledge;
        $this->updateType = $updateType;
        $this->itemTitle  = $itemTitle;
    }
}
