<?php

namespace App\Notifications;

use App\Models\PluginJmDraw;
use App\Traits\NoticeTrait;
use Modules\Notification\Contracts\BaseNotification;
use Modules\Notification\Messages\IMMessage;

class PluginJmDrawSuccessNotification extends BaseNotification
{
    use NoticeTrait;

    protected PluginJmDraw $draw;

    public function __construct(PluginJmDraw $draw)
    {
        $this->draw = $draw;
    }

    public static function getTitle(): string
    {
        return '生成产品';
    }

    public static function getData($item)
    {
        $data  = $item->data;
        $model = $data['model'] ?? '';

        $resource = self::getResource($model);
        return array_merge($data, [
            'resource' => $resource
        ]);
    }

    public function toIM($notifiable): IMMessage
    {
        $imUser = $notifiable->imUser;
        if ($imUser) {
            return new IMMessage(
                self::getTitle(),
                sprintf('您的%s已生成成功', $this->draw->type_text),
                [$imUser->im_user_id]
            );
        }
    }

    public function toDatabase($notifiable): array
    {
        $info = $this->draw;
        return [
            'model'   => [
                'id'    => $info->id,
                'type'  => $info->getMorphClass(),
                'cover' => $info->cover_url,
            ],
            'type'    => 'draw_success',
            'title'   => self::getTitle(),
            'target'  => [
                'id'   => $info->id,
                'type' => $info->getMorphClass(),
            ],
            'user'    => [
                'id'       => $info->user->id,
                'nickname' => $info->user->info->nickname,
                'avatar'   => $info->user->info->avatar_url,
            ],
            'content' => $this->draw->type_text.'成功',
        ];
    }
}
