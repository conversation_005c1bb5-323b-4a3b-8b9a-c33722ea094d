<?php

namespace App\Notifications;

use Illuminate\Database\Eloquent\Relations\Relation;
use Modules\Interaction\Models\Follow;
use Modules\Notification\Contracts\BaseNotification;
use Modules\Notification\Messages\IMMessage;

class UserFollowNotice extends BaseNotification
{

    protected Follow $follow;

    public function __construct(Follow $follow)
    {
        $follow->load('user', 'followable.info');
        $this->follow = $follow;
    }

    public static function getTitle(): string
    {
        return '用户关注';
    }

    public static function getData($item)
    {
        $data      = $item->data;
        $model     = $data['model'] ?? '';
        $is_follow = false;
        if (isset($model['id']) && isset($model['type'])) {
            if ($model['type'] === 'Modules\Interaction\Models\Follow') {
                $modelInfo = $model['type']::find($model['id']);
            } else {
                $modelInfo = Relation::getMorphedModel($model['type'])::find($model['id']);
            }
            if ($modelInfo) {
                $is_follow = $modelInfo->isReverseFollowing();
            }
        } else {
            return $data;
        }
        return array_merge($data, [
            'is_reverse_following' => $is_follow
        ]);
    }

    public function toIM($notifiable): IMMessage
    {
        $user   = $this->follow->user;
        $ImUser = $notifiable->imUser;
        if ($ImUser) {
            return new IMMessage(
                static::getTitle(),
                sprintf('%s关注了你', $user->info->nickname),
                [$ImUser->im_user_id]
            );
        }
    }

    public function toDatabase($notifiable): array
    {
        $follow = $this->follow;

        return [
            'model'   => [
                'id'   => $this->follow->id,
                'type' => $this->follow->getMorphClass(),
            ],
            'type'    => 'follow',
            'title'   => '用户关注',
            'user'    => [
                'id'       => $follow->user->id,
                'nickname' => $follow->user->info->nickname,
                'avatar'   => $follow->user->info->avatar_url,
            ],
            'content' => '开始关注你',
        ];
    }
}
