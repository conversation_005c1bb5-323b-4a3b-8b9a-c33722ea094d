<?php

namespace App\Notifications;

use App\Traits\NoticeTrait;
use Modules\Interaction\Models\Favorite;
use Modules\Notification\Contracts\BaseNotification;
use Modules\Notification\Messages\IMMessage;

class UserFavoriteUnifyAssetNotice extends BaseNotification
{
    use NoticeTrait;

    protected Favorite $favorite;

    public function __construct(Favorite $favorite)
    {
        $favorite->load(['favoriteable.user.info']);
        $this->favorite = $favorite;
    }

    public static function getTitle(): string
    {
        return '作品收藏';
    }

    public static function getData($item)
    {
        $data     = $item->data;
        $model    = $data['model'] ?? '';
        $resource = self::getResource($model);
        return array_merge($data, [
            'resource' => $resource
        ]);
    }

    public function toIM($notifiable): IMMessage
    {
        $user   = $this->favorite->user;
        $ImUser = $notifiable->imUser;
        if ($ImUser) {
            return new IMMessage(
                static::getTitle(),
                sprintf('%s收藏了你的作品', $user->info->nickname),
                [$ImUser->im_user_id]
            );
        }
    }

    public function toDatabase($notifiable): array
    {
        $info = $this->favorite;

        return [
            'model'   => [
                'id'    => $info->favoriteable->assetable->id,
                'type'  => $info->favoriteable->assetable_type,
                'cover' => $info->favoriteable->assetable->cover_url,
            ],
            'type'    => 'favorite_unify_asset',
            'title'   => self::getTitle(),
            'target'  => [
                'id'   => $info->favoriteable_id,
                'type' => $info->favoriteable_type,
            ],
            'user'    => [
                'id'       => $info->user->id,
                'nickname' => $info->user->info->nickname,
                'avatar'   => $info->user->info->avatar_url,
            ],
            'content' => '收藏了你的作品',
        ];
    }
}
