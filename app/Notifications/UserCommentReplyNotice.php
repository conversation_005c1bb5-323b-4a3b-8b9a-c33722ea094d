<?php

namespace App\Notifications;

use App\Traits\NoticeTrait;
use Modules\Interaction\Models\Comment;
use Modules\Notification\Contracts\BaseNotification;
use Modules\Notification\Messages\IMMessage;

class UserCommentReplyNotice extends BaseNotification
{
    use NoticeTrait;

    protected Comment $comment;

    public function __construct(Comment $comment)
    {
        $comment->load(['commentable.user.info', 'commentable.commentable.assetable']);
        $this->comment = $comment;
    }

    public static function getTitle(): string
    {
        return '评论回复';
    }

    public static function getData($item)
    {
        $data  = $item->data;
        $model = $data['model'] ?? '';

        $resource = self::getResource($model);
        return array_merge($data, [
            'resource' => $resource
        ]);
    }

    public function toIM($notifiable): IMMessage
    {
        $user   = $this->comment->user;
        $ImUser = $notifiable->imUser;
        if ($ImUser) {
            return new IMMessage(
                static::getTitle(),
                sprintf('%s回复了你的评论', $user->info->nickname),
                [$ImUser->im_user_id]
            );
        }
    }

    public function toDatabase($notifiable): array
    {
        $info = $this->comment;

        return [
            'model'   => [
                'id'    => $info->commentable->commentable->assetable->id,
                'type'  => $info->commentable->commentable->assetable_type,
                'cover' => $info->commentable->commentable->assetable->cover_url,
            ],
            'type'    => 'comment_reply',
            'title'   => self::getTitle(),
            'target'  => [
                'id'   => $info->commentable_id,
                'type' => $info->commentable_type,
            ],
            'user'    => [
                'id'       => $info->user->id,
                'nickname' => $info->user->info->nickname,
                'avatar'   => $info->user->info->avatar_url,
            ],
            'content' => '回复了您的评论',
        ];
    }
}
