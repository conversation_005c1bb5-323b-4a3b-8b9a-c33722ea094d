<?php

namespace App\Notifications;

use App\Traits\NoticeTrait;
use Modules\Interaction\Models\CommentLike;
use Modules\Notification\Contracts\BaseNotification;
use Modules\Notification\Messages\IMMessage;

class UserCommentLikeNotice extends BaseNotification
{
    use NoticeTrait;

    protected CommentLike $like;

    public function __construct(CommentLike $like)
    {
        $like->load(['target.commentable.assetable', 'target.user.info']);
        $this->like = $like;
    }

    public static function getTitle(): string
    {
        return '评论点赞';
    }

    public static function getData($item)
    {
        $data  = $item->data;
        $model = $data['model'] ?? '';

        $resource = self::getResource($model);
        return array_merge($data, [
            'resource' => $resource
        ]);
    }

    public function toIM($notifiable): IMMessage
    {
        $user   = $this->like->user;
        $ImUser = $notifiable->imUser;
        if ($ImUser) {
            return new IMMessage(
                static::getTitle(),
                sprintf('%s赞了你的评论', $user->info->nickname),
                [$ImUser->im_user_id]
            );
        }
    }

    public function toDatabase($notifiable): array
    {
        $info   = $this->like;
        $target = $info->target;

        return [
            'model'   => [
                'id'    => $target->commentable->id,
                'type'  => $target->commentable->assetable_type,
                'cover' => $target->commentable->assetable->cover_url,
            ],
            'type'    => 'interaction_comment_like',
            'title'   => self::getTitle(),
            'target'  => [
                'id'   => $info->target_id,
                'type' => $info->target_type,
            ],
            'user'    => [
                'id'       => $info->user->id,
                'nickname' => $info->user->info->nickname,
                'avatar'   => $info->user->info->avatar_url,
            ],
            'content' => '点赞了您的评论',
        ];
    }
}
