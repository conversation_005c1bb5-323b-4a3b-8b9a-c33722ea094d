<?php

namespace App\Console\Commands\IM;

use App\Models\ImUser;
use App\Packages\ImPush\ImPush;
use Illuminate\Console\Command;
use Throwable;

class ImCheckUsers extends Command
{
    protected $signature   = 'skyxu:im-check-users {--batch-size=100}';
    protected $description = '检查 IM 系统中的用户是否存在，并删除不存在的用户';

    public function handle(): void
    {
        $batchSize = (int) $this->option('batch-size'); // 从命令行选项获取批处理大小

        try {
            $totalUsers = ImUser::count();

            if ($totalUsers === 0) {
                $this->info("没有需要检查的用户");
                return;
            }

            $this->info("开始检查 {$totalUsers} 个用户");

            $progressBar = $this->output->createProgressBar($totalUsers);
            $progressBar->start();

            ImUser::query()
                ->select('id', 'im_user_id') // 只选择需要的字段
                ->chunkById($batchSize, function ($users) use ($progressBar, $batchSize) {
                    $userIds = $users->pluck('im_user_id')
                        ->map(function ($id) {
                            return ['UserID' => $id];
                        })
                        ->all();
                    try {
                        $client     = ImPush::user(); // 移动到循环内，更安全。
                        $res        = $client->checkUser($userIds);
                        $resultItem = $res['ResultItem'];

                        // 提取失败的用户 ID
                        $failUserIds = collect($resultItem)
                            ->where('AccountStatus', 'NotImported')
                            ->pluck('UserID')
                            ->all();
                        $failCount   = count($failUserIds);
                        if ($failCount > 0) {
                            $this->info("\n处理 IM 不存在的用户，一共 {$failCount} 个");
                            ImUser::whereIn('im_user_id', $failUserIds)->delete();
                        }
                        $this->info("\n检查完成");
                    } catch (Throwable $e) {
                        // 捕获更具体的异常，并记录详细信息
                        $this->error("处理用户时出错: ".$e->getMessage());
                    }

                    $progressBar->advance($users->count());
                }, 'id'); //指定主键列名

            $progressBar->finish();
            $this->newLine(); // 换行 (Laravel 8+)

            $this->info("检查完成");
        } catch (Throwable $e) {
            $this->error("发生未捕获的异常: ".$e->getMessage());
        }
    }
}
