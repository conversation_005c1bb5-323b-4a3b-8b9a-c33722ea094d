<?php

namespace App\Console\Commands\IM;

use App\Models\User;
use App\Packages\ImPush\ImPush;
use Illuminate\Console\Command;

class ImportUsers extends Command
{
    protected     $signature   = 'skyxu:im-import-users';
    protected     $description = '批量导入用户到IM';
    protected int $batchSize   = 100;

    public function handle(): void
    {
        $client = ImPush::user();

        try {
            $query      = User::query()->whereDoesntHave('imUser');
            $totalUsers = (clone $query)->count();

            if ($totalUsers == 0) {
                $this->info("没有需要导入的用户");
                return;
            }
            $this->info("开始导入 {$totalUsers} 个用户");

            $progressBar = $this->output->createProgressBar($totalUsers);
            $progressBar->start();
            User::query()
                ->whereDoesntHave('imUser')
                ->chunkById($this->batchSize, function ($users) use ($client, $progressBar) {
                    $client->importUsers($users);
                    $progressBar->advance($users->count());
                    usleep(200000);
                });

            $progressBar->finish();
            $this->line(''); // 换行

            $this->info("导入完成");
        } catch (\Exception $e) {
            $this->error($e->getMessage());
        }
    }

}