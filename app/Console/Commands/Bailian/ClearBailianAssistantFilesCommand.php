<?php

namespace App\Console\Commands\Bailian;

use App\Models\BailianAssistantFile;
use Carbon\Carbon;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;

class ClearBailianAssistantFilesCommand extends Command
{
    /**
     * 命令名称和参数
     *
     * @var string
     */
    protected $signature = 'bailian:clear-assistant-files
                            {--days=1 : 清理多少天前的文件}
                            {--dry-run : 预览模式，不实际删除文件}
                            {--chunk=100 : 每批处理的文件数量}
                            {--force-local : 强制删除本地文件，即使阿里云删除失败}';

    /**
     * 命令描述
     *
     * @var string
     */
    protected $description = '清理过期的智能体文件，释放OSS存储空间';

    /**
     * 统计信息
     */
    protected array $stats = [
        'scanned'               => 0,
        'deleted'               => 0,
        'failed'                => 0,
        'freed_bytes'           => 0,
        'deleted_alibaba_files' => 0,
        'alibaba_delete_errors' => 0,
        'errors'                => []
    ];

    /**
     * 执行命令
     *
     * @return int
     */
    public function handle(): int
    {
        try {
            $days       = (int) $this->option('days');
            $dryRun     = $this->option('dry-run');
            $chunkSize  = (int) $this->option('chunk');
            $forceLocal = $this->option('force-local');

            // 验证参数
            if ($days < 1) {
                $this->error('天数必须大于0');
                return 1;
            }

            $cutoffDate = Carbon::now()->subDays($days);

            $this->info("🧹 开始清理智能体文件...");
            $this->info("📅 清理时间：{$cutoffDate->format('Y-m-d H:i:s')} 之前的文件");
            $this->info("🔧 模式：".($dryRun ? '预览模式（不会实际删除）' : '实际删除模式'));
            if ($forceLocal) {
                $this->warn("⚠️  强制模式：即使阿里云删除失败也会删除本地文件");
            }
            $this->line('');

            // 获取需要清理的文件总数
            $totalFiles = BailianAssistantFile::where('created_at', '<', $cutoffDate)->count();

            if ($totalFiles === 0) {
                $this->info('✅ 没有找到需要清理的文件');
                return 0;
            }

            $this->info("📊 找到 {$totalFiles} 个文件需要处理");
            $this->line('');

            // 创建进度条
            $progressBar = $this->output->createProgressBar($totalFiles);
            $progressBar->setFormat(' %current%/%max% [%bar%] %percent:3s%% %elapsed:6s%/%estimated:-6s% %memory:6s%');
            $progressBar->start();

            // 分批处理文件
            BailianAssistantFile::where('created_at', '<', $cutoffDate)
                ->with(['storage'])
                ->chunk($chunkSize, function ($files) use ($dryRun, $forceLocal, $progressBar) {
                    foreach ($files as $file) {
                        $this->processFile($file, $dryRun, $forceLocal);
                        $progressBar->advance();
                    }
                });

            $progressBar->finish();
            $this->line('');
            $this->line('');

            // 显示统计结果
            $this->displayStats($dryRun);

            return 0;
        } catch (\Exception $e) {
            $this->error('清理过程中发生错误：'.$e->getMessage());
            Log::error('智能体文件清理失败', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return 1;
        }
    }

    /**
     * 处理单个文件
     */
    protected function processFile(BailianAssistantFile $file, bool $dryRun, bool $forceLocal = false): void
    {
        $this->stats['scanned']++;

        try {
            $storage = $file->storage;

            if (! $storage) {
                // 没有存储记录的文件，只删除阿里云文件和本地记录
                $this->newLine();
                $this->warn("文件 ID {$file->id} 没有关联的存储记录，将只删除阿里云文件和本地记录");
            }

            // 记录文件大小用于统计
            $fileSize = $storage->size ?? 0;

            if ($dryRun) {
                // 预览模式：只统计，不删除
                $this->stats['deleted']++;
                $this->stats['freed_bytes'] += $fileSize;

                // 统计有阿里云文件ID的文件
                if ($file->file_id) {
                    $this->stats['deleted_alibaba_files']++;
                }
                return;
            }

            // 实际删除模式
            DB::transaction(function () use ($file, $storage, $fileSize, $forceLocal) {
                if ($storage) {
                    if ($storage->path && Storage::disk($storage->disk ?? 'oss')->exists($storage->path)) {
                        Storage::disk($storage->disk ?? 'oss')->delete($storage->path);
                    }
                    $storage->delete();
                }
                // 3. 删除智能体文件记录
                $file->delete();

                $this->stats['deleted']++;
                $this->stats['freed_bytes'] += $fileSize;
            });
        } catch (\Exception $e) {
            $this->stats['failed']++;
            $this->stats['errors'][] = "文件 ID {$file->id} 删除失败：{$e->getMessage()}";

            Log::error('智能体文件删除失败', [
                'file_id' => $file->id,
                'error'   => $e->getMessage()
            ]);
        }
    }

    /**
     * 显示统计结果
     */
    protected function displayStats(bool $dryRun): void
    {
        $this->info('📈 清理统计结果：');
        $this->table(
            ['项目', '数量'],
            [
                ['扫描文件数', number_format($this->stats['scanned'])],
                [($dryRun ? '预计删除' : '成功删除'), number_format($this->stats['deleted'])],
                ['删除失败', number_format($this->stats['failed'])],
                [($dryRun ? '预计释放空间' : '释放空间'), $this->formatBytes($this->stats['freed_bytes'])],
                [
                    ($dryRun ? '预计删除阿里云文件' : '阿里云文件删除成功'),
                    number_format($this->stats['deleted_alibaba_files'])
                ],
                ['阿里云文件删除失败', number_format($this->stats['alibaba_delete_errors'])],
            ]
        );

        if (! empty($this->stats['errors'])) {
            $this->line('');
            $this->warn('⚠️  错误详情：');
            foreach (array_slice($this->stats['errors'], 0, 10) as $error) {
                $this->line("   • {$error}");
            }

            if (count($this->stats['errors']) > 10) {
                $remaining = count($this->stats['errors']) - 10;
                $this->line("   ... 还有 {$remaining} 个错误（详见日志）");
            }
        }

        if ($dryRun) {
            $this->line('');
            $this->comment('💡 这是预览模式，没有实际删除文件。要执行实际删除，请移除 --dry-run 参数。');
        } else {
            $this->line('');
            $this->info('✅ 清理完成！OSS存储空间已释放。');
        }
    }

    /**
     * 格式化字节数为可读格式
     */
    protected function formatBytes(int $bytes): string
    {
        if ($bytes === 0) {
            return '0 B';
        }

        $units = ['B', 'KB', 'MB', 'GB', 'TB'];
        $base  = log($bytes, 1024);
        $index = floor($base);
        $size  = round(pow(1024, $base - $index), 2);

        return $size.' '.$units[$index];
    }
}
