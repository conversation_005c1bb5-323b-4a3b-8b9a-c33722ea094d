<?php

namespace App\Console\Commands\Order;

use App\Models\RechargeOrder;
use Illuminate\Console\Command;
use Modules\Payment\Enums\PaymentStatus;
use Modules\Payment\Models\Payment;
use Modules\User\Enums\IdentityOrderStatus;
use Modules\User\Models\IdentityOrder;

class ClearCommand extends Command
{
    protected $signature   = 'order:clear';
    protected $description = '清除未支付订单';

    public function handle(): void
    {
        $expTime = now()->subHours(2)->toDateTimeString();
        Payment::where('status', PaymentStatus::UNPAY)
            ->where('created_at', '<', $expTime)
            ->delete();

        IdentityOrder::where('status', IdentityOrderStatus::UNPAY)
            ->where('created_at', '<', $expTime)
            ->delete();

        RechargeOrder::where('status', RechargeOrder::STATUS_INIT)
            ->where('created_at', '<', $expTime)
            ->delete();
    }
}