<?php

namespace App\Console\Commands\FastGpt;

use App\Models\Enums\FastgptKnowledgeSetEnum;
use App\Models\FastgptApp;
use App\Models\FastgptKnowledge;
use App\Packages\FastGpt\FastGpt;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class ChangeKnowledgeNewAddress extends Command
{
    protected $signature = 'skyxu:change_knowledge_new_address {knowledge_id : The ID of the knowledge base} {--delete-old=false : 是否删除旧的知识库}';

    protected $description = '知识库迁移至新fastgpt地址并创建新版本记录';

    const FASTGPT_IS_NEW = true;
    const FASTGPT_IS_OLD = false;

    public function handle(): void
    {
        $knowledgeId = (int) $this->argument('knowledge_id');
        $deleteOld   = $this->option('delete-old') === 'true';

        if (! $knowledgeId) {
            $this->error('知识库id不能为空');
            return;
        }

        $knowledge = FastgptKnowledge::find($knowledgeId);

        if (! $knowledge) {
            $this->error('知识库不存在');
            return;
        }

        DB::beginTransaction();
        try {
            // 检查基础数据
            FastGpt::knowledge()->setFastgptIsNew(self::FASTGPT_IS_NEW)->checkBaseData();
            // 检查基础数据
            FastGpt::knowledge()->setFastgptIsNew(self::FASTGPT_IS_OLD)->checkBaseData();

            $newKnowledge = $this->do($knowledge, $deleteOld);
            DB::commit();

            $this->info('知识库迁移成功！');
            $this->info('原知识库ID: '.$knowledge->id);
            $this->info('新知识库ID: '.$newKnowledge->id);
            $this->info('新FastGPT知识库ID: '.$newKnowledge->dataset_id);
        } catch (\Exception $e) {
            DB::rollBack();
            $this->error($e->getMessage());
            Log::error('知识库迁移失败: '.$e->getMessage(), ['knowledge_id' => $knowledgeId]);
        }
    }

    public function do($knowledge, $deleteOld = false)
    {
        // 创建新的知识库记录
        $newKnowledge                 = $knowledge->replicate();
        $newKnowledge->is_new_version = true;           // 添加新版本标识

        // 创建新的FastGPT知识库
        $result = FastGpt::knowledge()
            ->setFastgptIsNew(self::FASTGPT_IS_NEW)
            ->create(
                $knowledge->name,
                $knowledge->type,
                $knowledge->description,
            );
        $detail = FastGpt::knowledge()
            ->setFastgptIsNew(self::FASTGPT_IS_NEW)
            ->knowledgeDetail($result['data']);

        $newKnowledge->dataset_id = $result['data'];
        $newKnowledge->source     = $detail['data'];
        $newKnowledge->save();

        $this->info("新知识库 [{$newKnowledge->name}] 已创建，ID: {$newKnowledge->id}, FastGPT ID: {$result['data']}");

        $sets = $knowledge->knowledgeSets()->whereNotNull('collection_id')->get();
        $this->doSets($sets, $newKnowledge, $deleteOld);
        // 如果需要删除旧的知识库
        if ($deleteOld && $knowledge->dataset_id) {
            try {
                $this->deleteKnowledge($knowledge->dataset_id);
                //删除协作者
                $knowledge->fastgptCollaborators()->delete();
                //删除应用
                $apps = FastgptApp::where('dataset_ids', 'like', "%{$knowledge->dataset_id}%")->get();
                if ($apps->isNotEmpty()) {
                    $apps->each(function ($app) use ($knowledge) {
                        $res = FastGpt::app()->appDelete($app->app_id);
                        $app->chats()->delete();
                        $app->delete();
                    });
                }
                $this->info("旧知识库已删除，FastGPT ID: {$knowledge->dataset_id}");
            } catch (\Exception $exception) {
                Log::warning('删除旧知识库失败: '.$exception->getMessage(), ['dataset_id' => $knowledge->dataset_id]);
                $this->info('删除旧知识库失败: '.$exception->getMessage());
            }
        }
        return $newKnowledge;
    }

    private function deleteKnowledge(string $datasetId): void
    {
        // 保留删除功能，但只在明确指定时才执行
        FastGpt::knowledge()
            ->setFastgptIsNew(self::FASTGPT_IS_OLD)
            ->deleteKnowledge($datasetId);
    }

    public function doSets($sets, $newKnowledge, $deleteOld = false)
    {
        $this->info("开始迁移 {$sets->count()} 个知识集");

        $sets->each(function ($set, $index) use ($newKnowledge, $deleteOld) {
            $this->info("处理知识集 : {$set->name}");
            $this->processKnowledgeSet($set, $newKnowledge, $deleteOld);
        });

        $this->info("知识集迁移完成");
    }

    private function processKnowledgeSet($set, $newKnowledge, $deleteOld = false): void
    {
        // 创建新的知识集记录
        $newSet                = $set->replicate();
        $newSet->knowledge_id  = $newKnowledge->id;
        $newSet->collection_id = null;     // 清除旧的集合ID，后面会重新创建
        $newSet->save();

        $this->createAndAssociateSet($newSet, $newKnowledge);

        // 如果需要删除旧的知识集
        if ($deleteOld && $set->collection_id) {
            try {
                $this->deleteSet($set->collection_id);
                $set->delete();
                $this->info("旧知识集已删除，集合ID: {$set->collection_id}");
            } catch (\Exception $exception) {
                Log::warning('删除旧知识集失败: '.$exception->getMessage(),
                    ['set_id' => $set->id, 'collection_id' => $set->collection_id]);
                $this->info("Set ID: {$set->id} - 删除旧知识集失败: ".$exception->getMessage());
            }
        }
    }

    private function deleteSet(string $collectionId): void
    {
        // 保留删除功能，但只在明确指定时才执行
        FastGpt::set()->setFastgptIsNew(self::FASTGPT_IS_OLD)->deleteSet($collectionId);
    }

    private function createAndAssociateSet($newSet, $newKnowledge): void
    {
        try {
            $result       = null;
            $trainingType = $newSet->input_data['trainingType'] ?? null; // 提取 trainingType

            switch ((string) $newSet->type->value) {
                case FastgptKnowledgeSetEnum::EMPTY->value:
                    $result = FastGpt::set()
                        ->setFastgptIsNew(self::FASTGPT_IS_NEW)
                        ->setEmpty(
                            $newKnowledge->dataset_id,
                            $newSet->name,
                            $newSet->input_data['type']
                        );
                    break;
                case FastgptKnowledgeSetEnum::NOTE->value:
                case FastgptKnowledgeSetEnum::ARTICLE->value:
                case FastgptKnowledgeSetEnum::TEXT->value:
                    $result = FastGpt::set()
                        ->setFastgptIsNew(self::FASTGPT_IS_NEW)
                        ->setText(
                            $newKnowledge->dataset_id,
                            $newSet->name,
                            $newSet->input_data['text'],
                            $trainingType
                        );
                    break;
                case FastgptKnowledgeSetEnum::LINK->value:
                    $result = FastGpt::set()
                        ->setFastgptIsNew(self::FASTGPT_IS_NEW)
                        ->setLink(
                            $newKnowledge->dataset_id,
                            $newSet->input_data['link'],
                            $trainingType
                        );
                    break;
                case FastgptKnowledgeSetEnum::FILE->value:
                    $result = FastGpt::set()
                        ->setFastgptIsNew(self::FASTGPT_IS_NEW)
                        ->setFileData(
                            $newKnowledge->dataset_id,
                            $newSet->input_data['file'],
                            $trainingType
                        );
                    break;

                case FastgptKnowledgeSetEnum::DIRECTORY->value:
                default:
                    // DIRECTORY 类型的处理逻辑
                    $newSet->save();
                    return;
            }

            // 如果 result 为 null，说明没有进行任何操作，直接返回
            if (null === $result) {
                return;
            }

            $collectionId = $result['data']['collectionId'] ?? $result['data'];

            $this->info("知识集 [{$newSet->name}] 迁移成功，新ID: {$newSet->id}，新集合ID: {$collectionId}");

            $newSet->collection_id = $collectionId;
            $newSet->save();
        } catch (\Exception $exception) {
            Log::error('创建或更新知识集失败: '.$exception->getMessage(), ['set' => $newSet]);
            $this->error("Set ID: {$newSet->id} - 创建或更新知识集失败: ".$exception->getMessage());
        }
    }
}