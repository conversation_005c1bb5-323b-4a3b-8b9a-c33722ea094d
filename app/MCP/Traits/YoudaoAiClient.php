<?php

namespace App\MCP\Traits;

use Exception;
use GuzzleHttp\Client;
use GuzzleHttp\Exception\RequestException;
use Illuminate\Support\Str;

class YoudaoAiClient
{
    protected Client $client;
    protected int    $currentTime;
    protected string $saltV3;
    protected string $inputKey;
    protected string $appKey    = '34dd45fc1176f1be';
    protected string $appSecret = 'zs2ThFNrumWHXDRMzLgkpy7Ow4iTuK7b';

    public function __construct(protected string $baseUri)
    {
        $this->client      = new Client([
            'verify'   => false,
            'base_uri' => Str::finish($this->baseUri, '/'),
        ]);
        $this->currentTime = time();
        $this->saltV3      = Str::uuid()->toString();
    }

    public function OrcWritingErase(string $imageUrl)
    {
        $url    = 'ocr_writing_erase';
        $q      = base64_encode(file_get_contents($imageUrl));
        $params = [
            'appKey'   => $this->appKey,
            'curtime'  => $this->currentTime,
            'q'        => $q,
            'salt'     => $this->saltV3,
            'sign'     => $this->signV3($q),
            'signType' => 'v3',
        ];
        try {
            $data = $this->post($url, $params);
            if ($data['errorCode'] === '0') {
                return $this->success([
                    'originalImg'     => $data['originalImg'],
                    'eraseEnhanceImg' => $data['eraseEnhanceImg'],
                ]);
            } else {
                return $this->error($data['msg']);
            }
        } catch (Exception $exception) {
            return $this->error($exception->getMessage());
        }
    }

    private function signV3(string $input)
    {
        $len = mb_strlen($input, 'utf-8');

        if ($len > 20) {
            $input = sprintf("%s%s%s",
                mb_substr($input, 0, 10),
                $len,
                mb_substr($input, $len - 10, $len)
            );
        }
        return hash("sha256", sprintf("%s%s%s%s%s",
            $this->appKey,
            $input,
            $this->saltV3,
            $this->currentTime,
            $this->appSecret
        ));
    }

    protected function post(string $url, array $data = [])
    {
        return $this->request('POST', $url, $data, 'form_params');
    }

    private function request(string $method, string $url, array $data = [], string $paramsType = 'json')
    {
        try {
            $response = $this->client->request($method, $url, [
                $paramsType => $data,
            ]);
            if ($response->getStatusCode() === 200) {
                return json_decode($response->getBody()->getContents(), true);
            } else {
                throw new Exception($response->getBody()->getContents().'--CODE:'.$response->getStatusCode());
            }
        } catch (RequestException $exception) {
            $content = $exception->getResponse()->getBody()->getContents();
            if (Str::isJson($content)) {
                $errData = json_decode($content, true);
                throw new Exception($errData['msg'] ?? '未知错误');
            }
            throw new Exception($content);
        } catch (Exception $exception) {
            throw new Exception($exception->getMessage());
        }
    }

    protected function success(array $data, string $message = 'success')
    {
        return [
            'code'    => 200,
            'message' => $message,
            'data'    => $data
        ];
    }

    protected function error(string $message = 'success')
    {
        return [
            'code'    => 400,
            'message' => $message,
            'data'    => []
        ];
    }

    /**
     * @param  string  $url
     * @param  string  $q
     * @param  string  $key
     * @return array
     */
    public function composition(string $url, string $q): array
    {
        $q    = base64_encode($q);
        $data = [
            'appKey'   => $this->appKey,
            'curtime'  => $this->currentTime,
            'q'        => $q,
            'salt'     => $this->saltV3,
            'sign'     => $this->signV3($q),
            'signType' => 'v3',
        ];
        try {
            $data = $this->post($url, $data);
            if ($data['errorCode'] === '0') {
                return $this->success($data['Result']);
            } else {
                return $this->error($data['errorCode']);
            }
        } catch (Exception $exception) {
            return $this->error($exception->getMessage());
        }
    }

    public function json(string $url, array $data = [])
    {
        return $this->request('POST', $url, $data, 'json');
    }

    protected function setInputKey(string $inputKey): self
    {
        $this->inputKey = $inputKey;
        return $this;
    }
}