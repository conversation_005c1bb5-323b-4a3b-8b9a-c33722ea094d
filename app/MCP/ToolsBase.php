<?php

namespace App\MCP;

use App\Models\User;
use Exception;
use OPGG\LaravelMcpServer\Enums\ProcessMessageType;

class ToolsBase
{
    public function messageType(): ProcessMessageType
    {
        return ProcessMessageType::SSE;
    }

    public function getTtsContent(array $params): string
    {
        return '';
    }

    public function getTtsError(string $message): string
    {
        return '查询失败';
    }

    protected function getUser(array $params)
    {
        $auth   = $params['wateauth'] ?? '';
        $invite = app('user.hashids')->decode($auth);
        if (empty($invite)) {
            throw new Exception('用户鉴权失败');
        }
        $userId = $invite[0];
        $user   = User::find($userId);
        return $user;
    }

    protected function error(string $message, array $data = [])
    {
        return json_encode([
            'status'        => false,
            'message'       => $message,
            'result'        => $data,
            'notice_to_llm' => '失败后不要重复调用此工具',
        ], JSON_UNESCAPED_UNICODE);
    }

    protected function success(array $data, string $message = '')
    {
        return json_encode([
            'status'        => true,
            'message'       => 'success',
            'result'        => $data,
            'notice_to_llm' => $message,
        ], JSON_UNESCAPED_UNICODE);
    }
}