<?php

namespace App\MCP\Tools;

use App\MCP\ToolsBase;
use App\Packages\Coze\Coze;
use Exception;
use OPGG\LaravelMcpServer\Services\ToolService\ToolInterface;

class FoodOrCalorieTool extends ToolsBase implements ToolInterface
{
    public function name(): string
    {
        return 'food_or_calorie';
    }

    public function description(): string
    {
        return '查询食物的热量，或者根据输入的热量搜索对应的食物';
    }

    public function inputSchema(): array
    {
        return [
            'type'       => 'object',
            'properties' => [
                'food'   => [
                    'type'        => 'string',
                    'description' => '食物名称',
                ],
                'calory' => [
                    'type'        => 'integer',
                    'description' => '热量单位卡路里',
                ],
            ],
        ];
    }

    public function annotations(): array
    {
        return [
            'title'           => 'FoodOrCalorieTool',
            'readOnlyHint'    => false,
            'destructiveHint' => false,
            'idempotentHint'  => false,
            'openWorldHint'   => false,
        ];
    }

    public function execute(array $arguments): mixed
    {
        $food   = $arguments['food'] ?? '';
        $calory = $arguments['calory'] ?? '';
        if (blank($food) && blank($calory)) {
            return $this->error('至少需要食物名称或者热量');
        }
        try {
            $result = [];
            if ($food) {
                $response       = Coze::workFlow()
                    ->setWorkFlowId('7512787602787057664')
                    ->send('run', [
                        'food' => $food,
                    ]);
                $result['info'] = [
                    'unit' => '每100g',
                    'data' => $response->toArray()
                ];
            }
            if ($calory) {
                $response       = Coze::workFlow()
                    ->setWorkFlowId('7512793822410850338')
                    ->send('run', [
                        'calory' => $calory,
                    ]);
                $result['food'] = $response->toArray();
            }
            return $this->success($result);
        } catch (Exception $e) {
            return $this->error($e->getMessage());
        }
    }
}
