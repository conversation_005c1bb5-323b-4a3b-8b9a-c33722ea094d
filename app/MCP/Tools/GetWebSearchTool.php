<?php

namespace App\MCP\Tools;

use App\MCP\ToolsBase;
use Exception;
use OPGG\LaravelMcpServer\Services\ToolService\ToolInterface;

class GetWebSearchTool extends ToolsBase implements ToolInterface
{

    /**
     * Get the tool name.
     *
     * @return string
     */
    public function name(): string
    {
        return 'query_mobile_location';
    }

    /**
     * Get the tool description.
     *
     * @return string
     */
    public function description(): string
    {
        return '用户需要获取实时信息时调用此工具';
    }

    /**
     * Get the input schema for the tool.
     *
     * @return array
     */
    public function inputSchema(): array
    {
        return [
            'type'       => 'object',//不用动
            'properties' => [
                'keyword' => [
                    'type'        => 'string',//类型
                    'description' => '要查询的信息',
                ],
            ],
            'required'   => ['keyword'],//必填字段
        ];
    }

    /**
     * Get the tool annotations.
     *
     * @return array
     */
    public function annotations(): array
    {
        return [
            'title' => '联网查询实时信息'
        ];
    }

    /**
     * Execute the tool.
     *
     * @param  array  $arguments  Tool arguments
     * @return mixed
     */
    public function execute(array $arguments): string
    {
        try {
            $url     = 'https://ark.cn-beijing.volces.com/api/v3/bots/chat/completions';
            $data    = [
                'model'    => 'bot-20250617093950-rrpxl',
                'messages' => [
                    [
                        'content' => [
                            [
                                'text' => $arguments['keyword'],
                                'type' => 'text'
                            ]
                        ],
                        'role'    => 'user'
                    ]

                ]
            ];
            $message = $this->curlStreamRequest($url, $data);
            return $this->success(['message' => $message]);
        } catch (Exception $exception) {
            return $this->error($exception->getMessage());
        }
    }

    private function curlStreamRequest(string $url, $postData)
    {
        ignore_user_abort(true);
        set_time_limit(300);
        session_write_close();
        header('Content-Type: text/event-stream');
        header('Cache-Control: no-cache');
        header('Connection: keep-alive');
        header('X-Accel-Buffering: no');
        ob_implicit_flush(1);

        $ch = curl_init();
        curl_setopt($ch, CURLOPT_URL, $url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_FOLLOWLOCATION, true);
        curl_setopt($ch, CURLOPT_TIMEOUT, 0); // 无限超时
        curl_setopt($ch, CURLOPT_POSTFIELDS, json_encode($postData));
        curl_setopt($ch, CURLOPT_HTTPHEADER, [
            'Content-Type: application/json',
            'Authorization: Bearer d7559878-baec-4a6f-8d76-8d7d04a5582e'
        ]);
        curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, 300); // 增加连接超时时间
        curl_setopt($ch, CURLOPT_BUFFERSIZE, 0);       // 调整缓冲区大小
        $response = curl_exec($ch);
        $data     = json_decode($response);
        if ($response === false) {
            return '查询失败，我们换个话题吧';
        }
        try {
            return $data->choices[0]->message->content;
        } catch (Exception $exception) {
            return '查询失败，我们换个话题吧';
        }
    }

    public function getTtsError(string $message): string
    {
        return $message;
    }

    public function getTtsContent(array $params): string
    {
        return $params['message'];
    }
}
