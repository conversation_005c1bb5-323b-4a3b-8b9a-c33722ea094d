<?php

namespace App\MCP\Tools;

use App\MCP\ToolsBase;
use Exception;
use OPGG\LaravelMcpServer\Services\ToolService\ToolInterface;

class HistoricalStorytellingSessionTool extends ToolsBase implements ToolInterface
{

    public function name(): string
    {
        return 'historical_storytelling_session';
    }

    public function description(): string
    {
        return <<<EOF
# 历史故事会
 用户可以告诉你通过哪个历史创建故事会
 
## 功能
 工具的主要功能告诉模型生成故事会的工作流程

EOF;
    }

    public function inputSchema(): array
    {
        return [
            'type'       => 'object',
            'properties' => [
                'title' => [
                    'type'        => 'string',
                    'description' => '故事会名称',
                ],
            ],
        ];
    }

    public function annotations(): array
    {
        return [
            'title'           => 'HistoricalStorytellingSessionTool',
            'readOnlyHint'    => false,
            'destructiveHint' => false,
            'idempotentHint'  => false,
            'openWorldHint'   => false,
        ];
    }

    public function execute(array $arguments): mixed
    {
        $title = $arguments['title'] ?? '';
        try {
            $data = [
                'title'    => $title,
                'workflow' => <<<EOF
                接下来你要通过 `sequentialthinking` 来编排下面的步骤：
                
                1、如果用户没有指定需要的故事名称，你需要通过`bailian_web_search`来搜做一个最出名的故事名称及内容
                2、如果故事内容你无法获取，可以通过`fetch_web_url`工具来获取网页内容（步骤可跳过）。
                3、将故事内容通过`gen_image`生成一张封面图，图片的内容要于故事紧密相关。
                4、写一篇与故事相关的感言内容并且使用`text_polishing`进行润色。
                5、拆分润色完的文章的段落，使用`gen_image`给每一个段落配图。
                6、将封面、故事名称、故事更改、故事感言、段落配文告诉用户。
                
                以上是整体的工作流程，到此截止。一定要保持任务的流程，不要调用多余的工具。
                EOF,
            ];
            return $this->success($data);
        } catch (Exception $e) {
            return $this->error($e->getMessage());
        }
    }
}
