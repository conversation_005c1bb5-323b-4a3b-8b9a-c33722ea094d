<?php

namespace App\MCP\Tools;

use App\MCP\ToolsBase;
use App\Packages\Xfyun\Xfyun;
use Exception;
use Illuminate\Support\Str;
use OPGG\LaravelMcpServer\Services\ToolService\ToolInterface;

class GenPPTOutLineTool extends ToolsBase implements ToolInterface
{

    /**
     * Get the tool name.
     *
     * @return string
     */
    public function name(): string
    {
        return 'gen_ppt_outline';
    }

    /**
     * Get the tool description.
     *
     * @return string
     */
    public function description(): string
    {
        return '用户需要生成PPT大纲是调用此工具，生成大纲需要1积分，需要判断${score}是否充足。支持一个文档类型的附件';
    }

    /**
     * Get the input schema for the tool.
     *
     * @return array
     */
    public function inputSchema(): array
    {
        return [
            'type'       => 'object',//不用动
            'properties' => [
                'prompt'   => [
                    'type'        => 'string',//类型
                    'description' => '对大纲内容的描述,如果引用了知识库文件则提供知识库文件的内容',
                ],
                'language' => [
                    'type'        => 'string',
                    "enum"        => [
                        '中文', '英文', '日语', '俄语', '韩语', '德语', '法语', '葡萄牙语', '西班牙语', '意大利语',
                        '泰语'
                    ],
                    'default'     => '中文',
                    'description' => '大纲语言',
                ],
                'file_url' => [
                    'type'        => 'string',
                    'format'      => 'uri',
                    'pattern'     => '^(https|http)?://.*\.(pdf|doc|docx|txt|md)$',
                    'description' => '参考文件的URL:[.pdf,.doc,.docx,.txt,.md]格式',
                ],
                'search'   => [
                    'type'        => 'boolean',
                    'default'     => true,
                    'description' => '是否联网搜索',
                ],
                'wateauth' => [
                    'type'        => 'string',
                    'description' => '用户的鉴权信息',
                ],
            ],
            'required'   => ['prompt', 'language', 'wateauth'],//必填字段
        ];
    }

    /**
     * Get the tool annotations.
     *
     * @return array
     */
    public function annotations(): array
    {
        return [
            'title' => '生成PPT大纲'
        ];
    }

    /**
     * Execute the tool.
     *
     * @param  array  $arguments  Tool arguments
     * @return mixed
     */
    public function execute(array $arguments): string
    {
        try {
            $user     = $this->getUser($arguments);//获取参数中的AUTH
            $prompt   = $arguments['prompt'];
            $fileUrl  = $arguments['file_url'] ?? '';
            $language = match ($arguments['language']) {
                '中文' => 'cn',
                '英文' => 'en',
                '日语' => 'ja',
                '俄语' => 'ru',
                '韩语' => 'ko',
                '德语' => 'de',
                '法语' => 'fr',
                '葡萄牙语' => 'pt',
                '西班牙语' => 'es',
                '意大利语' => 'it',
                '泰语' => 'th',
                default => 'cn',
            };
            $search   = $arguments['search'];
        } catch (Exception $exception) {
            return $this->error($exception->getMessage());
        }

        try {
            if ($fileUrl && Str::isUrl($fileUrl)) {
                $res = Xfyun::ppt()->outlineDoc($prompt, $language, $search);
            } else {
                $res = Xfyun::ppt()->outline($prompt, $language, $search);
            }
            $outline = $res['outline'];
            return $this->success([
                'markdown' => $this->getMarkDown($outline),
                'outline'  => $outline,
            ]);
        } catch (Exception $exception) {
            return $this->error($exception->getMessage());
        }
    }

    private function getMarkDown(array $outline)
    {
        $string = sprintf('# %s \n', $outline['title'] ?? '');
        $string .= sprintf('#### %s \n', $outline['subTitle'] ?? '');
        foreach ($outline['chapters'] as $section) {
            $string .= sprintf('## %s \n', $section['chapterTitle'] ?? '');
            foreach ($section['chapterContents'] as $item) {
                $string .= sprintf('- %s \n', $item['chapterTitle'] ?? '');
            }
        }
        return $string;
    }
}
