<?php

namespace App\MCP\Tools;

use App\MCP\ToolsBase;
use App\MCP\Traits\OpenAiToolClient;
use Exception;
use Illuminate\Support\Facades\Validator;
use OPGG\LaravelMcpServer\Services\ToolService\ToolInterface;

class ArticleContinuationTool extends ToolsBase implements ToolInterface
{

    public function name(): string
    {
        return 'article_continuation';
    }

    public function description(): string
    {
        return '文章续写工具,根据用户提供的文章继续写文档.';
    }

    public function inputSchema(): array
    {
        return [
            'type'       => 'object',
            'properties' => [
                'content' => [
                    'type'        => 'string',
                    'description' => '用户提供的文章',
                ],
            ],
            'required'   => ['content'],
        ];
    }

    public function annotations(): array
    {
        return [
            'title' => '文章续写'
        ];
    }

    /**
     * Execute the tool.
     *
     * @param  array  $arguments  Tool arguments
     * @return mixed
     */
    public function execute(array $arguments): string
    {
        try {
            Validator::make($arguments, [
                'content' => ['required', 'string'],
            ])->validate();

            $content = $arguments['content'];
            $openAi  = new OpenAiToolClient(
                'sk-6625f866cdcd41feb700ae01715e75df',
                'https://dashscope.aliyuncs.com/compatible-mode/v1',
            );
            $result  = $openAi->chat('qwen-plus-latest', wateGetSystemPrompt('ArticleContinuation'), $content, 8000, [
                'enable_search'  => true,
                'search_options' => [
                    'forced_search'   => true,
                    'search_strategy' => 'pro',
                ],
            ]);
            return $this->success([
                'article' => $result,
            ]);
        } catch (Exception $exception) {
            return $this->error($exception->getMessage());
        }
    }
}
