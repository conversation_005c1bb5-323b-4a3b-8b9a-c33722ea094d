<?php

namespace App\MCP\Tools;

use App\MCP\ToolsBase;
use App\Packages\Coze\Coze;
use Exception;
use OPGG\LaravelMcpServer\Services\ToolService\ToolInterface;

class QueryLotteryPeriodNumberTool extends ToolsBase implements ToolInterface
{
    public function name(): string
    {
        return 'query_lottery_period_number';
    }

    public function description(): string
    {
        return '获取最新开奖期数';
    }

    public function inputSchema(): array
    {
        return [
            'type'       => 'object',
            'properties' => [
                'name' => [
                    'type'        => 'string',
                    'enum'        => ['dlt', 'ssq'],
                    'description' => '彩票类型，大乐透为dlt，双色球为ssq',
                ],
            ],
            'required'   => ['param1'],
        ];
    }

    public function annotations(): array
    {
        return [
            'title'           => 'QueryLotteryPeriodNumberTool',
            'readOnlyHint'    => false,
            'destructiveHint' => false,
            'idempotentHint'  => false,
            'openWorldHint'   => false,
        ];
    }

    public function execute(array $arguments): mixed
    {
        try {
            $response = Coze::workFlow()
                ->setWorkFlowId('7514946477081526299')
                ->send('run', [
                    'name' => $arguments['name'],
                ]);
            if ($response->isSuccess()) {
                $result = $response->toArray();
                return $this->success($result);
            } else {
                return $this->error($response->getMessage());
            }
        } catch (Exception $e) {
            return $this->error($e->getMessage());
        }
    }
}
