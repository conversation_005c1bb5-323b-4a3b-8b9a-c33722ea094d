<?php

namespace App\MCP\Tools;

use App\MCP\ToolsBase;
use App\Packages\Coze\Coze;
use Exception;
use OPGG\LaravelMcpServer\Services\ToolService\ToolInterface;

class QueryCarSeriesTool extends ToolsBase implements ToolInterface
{

    public function name(): string
    {
        return 'query_car_series';
    }

    public function description(): string
    {
        return '当你需要查询新车信息或者查询某个特定车系（如宝马3系，奔驰e级）信息的时候可以使用此工具，可以获得新车价格，车辆结构，车辆生产年份，售卖链接等信息';
    }

    public function inputSchema(): array
    {
        return [
            'type'       => 'object',
            'properties' => [
                'series' => [
                    'type'        => 'string',
                    'description' => '期望查询的车系，如宝马3系、奔驰e级',
                ],
            ],
            'required'   => ['series'],
        ];
    }

    public function annotations(): array
    {
        return [
            'title'           => 'QueryCarSeriesTool',
            'readOnlyHint'    => false,
            'destructiveHint' => false,
            'idempotentHint'  => false,
            'openWorldHint'   => false,
        ];
    }

    public function execute(array $arguments): mixed
    {
        try {
            $series = $arguments['series'];

            $response = Coze::workFlow()
                ->setWorkFlowId('7516834608328704012')
                ->send('run', [
                    'series' => $series,
                ]);
            if ($response->isSuccess()) {
                return $this->success($response->toArray());
            } else {
                return $this->error($response->getMessage());
            }
        } catch (Exception $e) {
            return $this->error($e->getMessage());
        }
    }
}
