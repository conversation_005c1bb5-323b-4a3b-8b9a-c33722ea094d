<?php

namespace App\MCP\Tools;

use App\MCP\ToolsBase;
use App\MCP\Traits\AliCloudMarketClient;
use Exception;
use OPGG\LaravelMcpServer\Services\ToolService\ToolInterface;

class GetSearchMobileTool extends ToolsBase implements ToolInterface
{

    /**
     * Get the tool name.
     *
     * @return string
     */
    public function name(): string
    {
        return 'query_mobile_location';
    }

    /**
     * Get the tool description.
     *
     * @return string
     */
    public function description(): string
    {
        return '查询手机号归属地的工具，需要用户说出手机号。';
    }

    /**
     * Get the input schema for the tool.
     *
     * @return array
     */
    public function inputSchema(): array
    {
        return [
            'type'       => 'object',//不用动
            'properties' => [
                'mobile' => [
                    'type'        => 'string',//类型
                    'description' => '要查询的手机号',
                ],
            ],
            'required'   => ['mobile'],//必填字段
        ];
    }

    /**
     * Get the tool annotations.
     *
     * @return array
     */
    public function annotations(): array
    {
        return [
            'title' => '查询手机归属地'
        ];
    }

    /**
     * Execute the tool.
     *
     * @param  array  $arguments  Tool arguments
     * @return mixed
     */
    public function execute(array $arguments): string
    {
        try {
            $mobile = $arguments['mobile'];
            if (! $this->isValidChineseMobile($mobile)) {
                return $this->error('您提供的手机号不正确哦！我无法为您查询');
            }

            $appcode  = "de2aaac75e674e6f963434be21ed235c";
            $aliCloud = new AliCloudMarketClient('https://shudichaxu.market.alicloudapi.com', $appcode);
            $data     = $aliCloud->post('/mobile_area', [
                'phone_number' => $mobile
            ]);
            if ($data['status'] === 'OK') {
                return $this->success($data['result']);
            } else {
                return $this->error($data['msg']);
            }
        } catch (Exception $exception) {
            return $this->error($exception->getMessage());
        }
    }

    private function isValidChineseMobile($mobile)
    {
        return preg_match('/^1[3-9]\d{9}$/', $mobile);
    }

    public function getTtsError(string $message): string
    {
        return $message;
    }

    public function getTtsContent(array $params): string
    {
        return sprintf('手机号归属地为:%s%s.运营商为:%s',
            $params['prov'],
            $params['pref'],
            $params['carrier']
        );
    }
}
