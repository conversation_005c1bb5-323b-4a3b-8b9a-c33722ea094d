<?php

namespace App\MCP\Tools;

use App\MCP\ToolsBase;
use App\Packages\Coze\Coze;
use Exception;
use OPGG\LaravelMcpServer\Services\ToolService\ToolInterface;

class SearchVideoFormBilibiliTool extends ToolsBase implements ToolInterface
{

    public function name(): string
    {
        return 'search_video_form_bilibili';
    }

    public function description(): string
    {
        return '根据用户提供的关键字搜索B站的视频';
    }

    public function inputSchema(): array
    {
        return [
            'type'       => 'object',
            'properties' => [
                'keyword' => [
                    'type'        => 'string',
                    'description' => '搜索关键字',
                ],
            ],
            'required'   => ['keyword'],
        ];
    }

    public function annotations(): array
    {
        return [
            'title'           => 'SearchVideoFormBilibiliTool',
            'readOnlyHint'    => false,
            'destructiveHint' => false,
            'idempotentHint'  => false,
            'openWorldHint'   => false,
        ];
    }

    public function execute(array $arguments): mixed
    {
        $keyWord = $arguments['keyword'];
        if (blank($keyWord)) {
            return $this->error('请输入搜索关键字');
        }

        try {
            $response = Coze::workFlow()
                ->setWorkFlowId('7512715134624989247')
                ->send('run', [
                    'keyword' => $keyWord,
                ]);
            if ($response->isSuccess()) {
                $data   = $response->toArray()['data'];
                $data   = array_chunk($data, 10, true)[0];
                $result = collect($data)->map(function ($item, $key) {
                    return [
                        'title'       => $item['title'],
                        'author'      => $item['author'],
                        'tag'         => $item['tag'],
                        'type'        => $item['typename'],
                        'video_url'   => $item['arcurl'],
                        'cover'       => 'https:'.$item['pic'],
                        'author_logo' => $item['upic'],
                    ];
                })->toArray();
                return $this->success($result);
            } else {
                return $this->error($response->getMessage());
            }
        } catch (Exception $e) {
            return $this->error($e->getMessage());
        }
    }
}
