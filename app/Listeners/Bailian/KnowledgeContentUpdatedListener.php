<?php

namespace App\Listeners\Bailian;

use App\Events\KnowledgeContentUpdatedEvent;
use App\Jobs\BaiLian\SendKnowledgeUpdateNotificationJob;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Support\Facades\Log;
use Modules\Interaction\Models\Subscribe;

class KnowledgeContentUpdatedListener implements ShouldQueue
{
    public string $connection = 'redis';
    public int    $delay      = 0;
    public int    $tries      = 3;

    /**
     * 处理知识库内容更新事件
     *
     * @param  KnowledgeContentUpdatedEvent  $event
     * @return void
     */
    public function handle(KnowledgeContentUpdatedEvent $event): void
    {
        try {
            $knowledge  = $event->knowledge;
            $updateType = $event->updateType;
            $itemTitle  = $event->itemTitle;
            // 分批获取订阅用户ID，避免一次性加载过多数据
            $subscriberQuery = Subscribe::where('subscribable_type', $knowledge->getMorphClass())
                ->where('subscribable_id', $knowledge->id)
                ->whereNull('deleted_at');

            $subscriberCount = $subscriberQuery->count();

            if ($subscriberCount === 0) {
                return;
            }

            // 分批处理订阅用户，每批50个，避免内存占用过大
            $subscriberQuery->chunk(50, function ($subscribers) use ($knowledge, $updateType, $itemTitle) {
                foreach ($subscribers as $subscribe) {
                    // 为每个用户分发单独的通知任务
                    SendKnowledgeUpdateNotificationJob::dispatch(
                        $subscribe->user_id,
                        $knowledge->id,
                        $updateType,
                        $itemTitle,
                    );
                }
            });
        } catch (\Exception $e) {
            throw $e; // 重新抛出异常以触发重试
        }
    }

    /**
     * 处理失败的任务
     *
     * @param  KnowledgeContentUpdatedEvent  $event
     * @param  \Exception  $exception
     * @return void
     */
    public function failed(KnowledgeContentUpdatedEvent $event, $exception): void
    {
        Log::error('Knowledge content update notification listener failed permanently', [
            'knowledge_id' => $event->knowledge->id,
            'update_type'  => $event->updateType,
            'item_title'   => $event->itemTitle,
            'error'        => $exception->getMessage()
        ]);
    }
}
