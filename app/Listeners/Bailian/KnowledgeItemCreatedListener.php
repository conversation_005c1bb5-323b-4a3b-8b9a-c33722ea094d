<?php

namespace App\Listeners\Bailian;

use App\Events\KnowledgeItemCreatedEvent;
use App\Jobs\BaiLian\SyncKnowledgeItemToAlibabaJob;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Support\Facades\Log;

/**
 * 知识库项目创建监听器
 *
 * 处理知识库项目创建后的异步操作，主要是同步到阿里云
 */
class KnowledgeItemCreatedListener implements ShouldQueue
{
    use InteractsWithQueue;

    public string $connection = 'redis';
    public string $queue      = 'BaiLian';
    public int    $tries      = 3;

    /**
     * 处理知识库项目创建事件
     *
     * @param  KnowledgeItemCreatedEvent  $event
     * @return void
     */
    public function handle(KnowledgeItemCreatedEvent $event): void
    {
        try {
            $knowledgeItem = $event->knowledgeItem;

            // 刷新模型状态，确保获取最新数据
            $knowledgeItem->refresh();

            // 检查知识库项目是否仍然存在
            if (! $knowledgeItem->exists) {
                return;
            }

            // 检查是否需要同步（只有笔记和文件需要同步到阿里云）
            if (! in_array($knowledgeItem->itemable_type, ['bailian_article', 'bailian_file'])) {
                return;
            }

            // 检查同步状态
            if (! $knowledgeItem->needsSync()) {
                return;
            }

            // 分发同步任务
            SyncKnowledgeItemToAlibabaJob::dispatch($knowledgeItem);
        } catch (\Exception $e) {
            Log::error('处理知识库项目创建事件失败', [
                'knowledge_item_id' => $event->knowledgeItem->id,
                'error'             => $e->getMessage(),
                'trace'             => $e->getTraceAsString()
            ]);

            // 重新抛出异常以触发重试机制
            throw $e;
        }
    }

    /**
     * 处理失败的任务
     *
     * @param  KnowledgeItemCreatedEvent  $event
     * @param  \Exception  $exception
     * @return void
     */
    public function failed(KnowledgeItemCreatedEvent $event, $exception): void
    {
        // 标记知识库项目同步失败
        try {
            $event->knowledgeItem->markAsSyncFailed('事件监听器失败: '.$exception->getMessage());
        } catch (\Exception $e) {
            Log::error('标记知识库项目同步失败时出错', [
                'knowledge_item_id' => $event->knowledgeItem->id,
                'error'             => $e->getMessage()
            ]);
        }
    }
}
