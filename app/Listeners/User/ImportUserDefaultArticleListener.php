<?php

namespace App\Listeners\User;

use App\Events\UserCreatedEvent;
use App\Models\BailianArticle;
use App\Models\SystemConfig;
use Illuminate\Contracts\Queue\ShouldQueue;

class ImportUserDefaultArticleListener implements ShouldQueue
{
    public string $connection = 'redis';

    public int $delay = 0;

    public int $tries = 1;

    public function handle(UserCreatedEvent $event): void
    {
        $user           = $event->user;
        $defaultArticle = SystemConfig::getValue('new_user_default_article_id');
        if ($defaultArticle) {
            $article = BailianArticle::with('tags')->find($defaultArticle);
            if ($article) {
                $newArticle             = $article->replicate();
                $newArticle->user_id    = $user->id;
                $newArticle->created_at = now();
                $newArticle->save();
                if ($article->logs->isNotEmpty()) {
                    $newArticle->tags()->attach($article->tags->pluck('id')->toArray());
                }
            }
        }
    }
}