<?php

namespace App\Listeners\Recharge;

use App\Events\RechargeOrderPaidEvent;
use Illuminate\Contracts\Queue\ShouldQueue;

class OrderPaidListener implements ShouldQueue
{
    public string $connection = 'redis';

    public int $delay = 0;

    public int $tries = 1;

    public function handle(RechargeOrderPaidEvent $event): void
    {
        $order = $event->order;
        $user  = $order->user;
        if ($order->score && $user->account->logs()->where('source->recharge_order_id', $order->id)
                ->where('rule_id', 4)->doesntExist()) {
            $effective = $order->source['effective'] ?? 0;
            $user->account->exec('recharge_score', $order->score, $effective > 0 ? $effective : null, [
                'recharge_order_id' => $order->id,
                'remark'            => $order->getTitle(),
            ]);
        }
        //        Bonus::Recharge($order)->bonus();
    }

}