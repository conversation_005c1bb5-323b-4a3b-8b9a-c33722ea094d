<?php

namespace App\Models;

use App\Packages\BailianAssistant\Tools\KnowledgeTool;
use App\Traits\BelongsToUser;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Modules\Storage\Models\Upload;

class BailianAssistantFile extends Model
{
    use BelongsToUser;

    /**
     * 可以上传到智能体应用的文件类型及其限制
     *
     * 文档类型：单文件不超过100MB
     * 图片类型：单文件不超过20MB（仅支持包含文字内容的本地图片）
     * 视频类型：单文件不超过512MB
     * 音频类型：单文件不超过512MB
     */
    const CAN_UPLOAD_FILE_ASSISTANT_TYPES = [
        // 文档类型 (100MB)
        'doc'  => ['max_size' => 104857600, 'type' => 'document', 'description' => '文档文件'],
        'docx' => ['max_size' => 104857600, 'type' => 'document', 'description' => '文档文件'],
        'wps'  => ['max_size' => 104857600, 'type' => 'document', 'description' => '文档文件'],
        'ppt'  => ['max_size' => 104857600, 'type' => 'document', 'description' => '演示文件'],
        'pptx' => ['max_size' => 104857600, 'type' => 'document', 'description' => '演示文件'],
        'xls'  => ['max_size' => 104857600, 'type' => 'document', 'description' => '表格文件'],
        'xlsx' => ['max_size' => 104857600, 'type' => 'document', 'description' => '表格文件'],
        'md'   => ['max_size' => 104857600, 'type' => 'document', 'description' => 'Markdown文件'],
        'txt'  => ['max_size' => 104857600, 'type' => 'document', 'description' => '文本文件'],
        'pdf'  => ['max_size' => 104857600, 'type' => 'document', 'description' => 'PDF文件'],

        // 图片类型 (20MB)
        'png'  => ['max_size' => 20971520, 'type' => 'image', 'description' => '图片文件'],
        'jpg'  => ['max_size' => 20971520, 'type' => 'image', 'description' => '图片文件'],
        'jpeg' => ['max_size' => 20971520, 'type' => 'image', 'description' => '图片文件'],
        'bmp'  => ['max_size' => 20971520, 'type' => 'image', 'description' => '图片文件'],
        'gif'  => ['max_size' => 20971520, 'type' => 'image', 'description' => '图片文件'],

        // 视频类型 (512MB)
        'mp4'  => ['max_size' => 536870912, 'type' => 'video', 'description' => '视频文件'],
        'mkv'  => ['max_size' => 536870912, 'type' => 'video', 'description' => '视频文件'],
        'avi'  => ['max_size' => 536870912, 'type' => 'video', 'description' => '视频文件'],
        'mov'  => ['max_size' => 536870912, 'type' => 'video', 'description' => '视频文件'],
        'wmv'  => ['max_size' => 536870912, 'type' => 'video', 'description' => '视频文件'],

        // 音频类型 (512MB)
        'aac'  => ['max_size' => 536870912, 'type' => 'audio', 'description' => '音频文件'],
        'amr'  => ['max_size' => 536870912, 'type' => 'audio', 'description' => '音频文件'],
        'flac' => ['max_size' => 536870912, 'type' => 'audio', 'description' => '音频文件'],
        'flv'  => ['max_size' => 536870912, 'type' => 'audio', 'description' => '音频文件'],
        'm4a'  => ['max_size' => 536870912, 'type' => 'audio', 'description' => '音频文件'],
        'mp3'  => ['max_size' => 536870912, 'type' => 'audio', 'description' => '音频文件'],
        'mpeg' => ['max_size' => 536870912, 'type' => 'audio', 'description' => '音频文件'],
        'ogg'  => ['max_size' => 536870912, 'type' => 'audio', 'description' => '音频文件'],
        'opus' => ['max_size' => 536870912, 'type' => 'audio', 'description' => '音频文件'],
        'wav'  => ['max_size' => 536870912, 'type' => 'audio', 'description' => '音频文件'],
        'webm' => ['max_size' => 536870912, 'type' => 'audio', 'description' => '音频文件'],
        'wma'  => ['max_size' => 536870912, 'type' => 'audio', 'description' => '音频文件'],
    ];

    public function storage(): BelongsTo
    {
        return $this->belongsTo(Upload::class, 'storage_id');
    }

    public function getStatusTextAttribute()
    {
        return BailianKnowledgeFile::STATUS_MAP[$this->status] ?? '未知';
    }

    public function resetStatus()
    {
        $res = app(KnowledgeTool::class)->getFileStatus($this->file_id);
        if ($res->status != 200) {
            return;
        }
        $data = $res->data;
        if ($this->status != $data->status) {
            $this->update(['status' => $data->status]);
        }
    }

    public function isFinished(): bool
    {
        return $this->status == BailianKnowledgeFile::STATUS_FILE_IS_READY;
    }
}
