<?php

namespace App\Models;

use App\Traits\HasCovers;
use App\Traits\HasEasyStatus;

class Resources extends Model
{
    use HasEasyStatus,
        HasCovers;

    const IS_FREE_FALSE = 0;
    const IS_FREE_TRUE  = 1;
    const IS_FREE_MAP   = [
        self::IS_FREE_TRUE  => '是',
        self::IS_FREE_FALSE => '否'
    ];

    const IS_FREE_LABEL = [
        self::IS_FREE_FALSE => 'info',
        self::IS_FREE_TRUE  => 'red',
    ];
}