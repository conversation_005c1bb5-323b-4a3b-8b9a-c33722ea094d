<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Modules\Interaction\Models\Like;

class Business extends Model
{
    use SoftDeletes;

    protected $table = 'business';

    const IS_WORK    = [
        1 => '工作已认证',
        0 => '工作未认证',
    ];
    const IS_COMPANY = [
        1 => '企业已认证',
        0 => '企业未认证',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'uid', 'id');
    }

    public function companyUser(): BelongsTo
    {
        return $this->belongsTo(CompanyUser::class, 'uid', 'uid');
    }

    public function likes(): MorphMany
    {
        return $this->morphMany(Like::class, 'likeable');
    }

    public function companyModel(): HasOne
    {
        return $this->hasOne(Company::class, 'id', 'company_id');
    }

    public function getCompany()
    {
        $company = $this->company;
        if (! $company) {
            return null;
        }
        if ($this->company_type != 0) {
            return json_decode($this->company);
        } else {
            return $company;
        }
    }

    public function getBusiness()
    {
        $business = $this->business;
        if (! $business) {
            return null;
        }
        if ($this->business_type != 0) {
            return json_decode($this->business);
        }

        return $business;
    }

    public function browses(): HasMany
    {
        return $this->hasMany(BrowseRecord::class, 'bid', 'id');
    }
}