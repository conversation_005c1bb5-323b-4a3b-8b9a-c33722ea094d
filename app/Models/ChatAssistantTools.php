<?php

namespace App\Models;

use App\Packages\Assistant\ChatTools\ChatToolsProvider;
use Exception;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class ChatAssistantTools extends Model
{
    protected $casts = [
        'params'  => 'json',
        'results' => 'json'
    ];

    public function log(): BelongsTo
    {
        return $this->belongsTo(ChatAssistantLog::class, 'log_id');
    }

    public function callFunctionGetParams(string $function)
    {
        $event = $this->getEvent($this->key);
        if ($event) {
            if (method_exists($event, $function)) {
                return $event->$function($this->results);
            } else {
                throw new Exception('辅助工具方法不存在');
            }
        } else {
            throw new Exception('实例化辅助工具失败');
        }
    }

    protected function getEvent(string $name)
    {
        return ChatToolsProvider::getModel($name);
    }
}
