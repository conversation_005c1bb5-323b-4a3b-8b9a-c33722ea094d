<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class CompanyStaff extends Model
{
    use SoftDeletes;

    protected $table = 'company_staff';

    const PERMISSIONS = [
        1 => '企业知识库管理',
        2 => '企业成员',
        3 => '待审核成员',
        4 => '企业人脉',
    ];

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'uid');
    }

    public static function getRole($uid, $company_id, $type)
    {
        $staff = CompanyStaff::where('uid', $uid)->where('company_id', $company_id)->first();
        $role  = [];
        if ($staff && $staff->is_manage != 0) {
            if ($staff->is_manage == 2) {
                $role = [1, 2, 3, 4];
            } else {
                $role = explode(',', $staff->permission);
            }
        }

        if (in_array($type, $role)) {
            return true;
        }
        return false;
    }

    /**
     * Notes: 关联权限中间表
     *
     * @Author: 玄尘
     * @Date: 2024/12/23 10:13
     * @return \Illuminate\Database\Eloquent\Relations\HasMany
     */
    public function companyDepartmentRoles(): HasMany
    {
        return $this->hasMany(CompanyDepartmentRole::class, 'uid');
    }
}
