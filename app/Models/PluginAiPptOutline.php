<?php

namespace App\Models;

use App\Traits\BelongsToUser;
use App\Traits\HasCovers;
use Illuminate\Database\Eloquent\Casts\Attribute;

class PluginAiPptOutline extends Model
{
    use HasCovers, BelongsToUser;

    const STATUS_INIT    = 0;
    const STATUS_OUTLINE = 1;
    const STATUS_CONTENT = 2;
    const STATUS_SUCCESS = 3;
    const STATUS_ERROR   = 9;
    const STATUS         = [
        self::STATUS_INIT    => '已创建',
        self::STATUS_OUTLINE => '已生成大纲',
        self::STATUS_CONTENT => '创建中',
        self::STATUS_SUCCESS => '已完成',
        self::STATUS_ERROR   => '失败',
    ];
    protected $casts = [
        'outline' => 'json',
        'content' => 'json',
    ];

    public function getStatusTextAttribute()
    {
        return self::STATUS[$this->status] ?? '';
    }

    public function fileUrlAttr(): Attribute
    {
        return new Attribute(
            get: fn() => $this->parseImageUrl($this->getAttribute('file_url'))
        );
    }
}
