<?php

namespace App\Models;

use App\Exceptions\ValidatorException;
use App\Jobs\Plugin\MjQueryJob;
use App\Models\Traits\AssetTrait;
use App\Models\Traits\FFMpegVideoTrait;
use App\Packages\Mj\Mj;
use App\Traits\AutoCreateOrderNo;
use App\Traits\BelongsToUser;
use App\Traits\HasCovers;
use Exception;
use GuzzleHttp\Client;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Support\Facades\Storage;

class PluginMjDraw extends Model
{
    use BelongsToUser, AutoCreateOrderNo, HasCovers, AssetTrait, FFMpegVideoTrait;

    const TYPE_PLUGIN_FACE         = 'plugin_face';
    const TYPE_PLUGIN_EXPAND       = 'plugin_expand';
    const TYPE_PLUGIN_ANIME        = 'plugin_anime';
    const TYPE_PLUGIN_LINEDRAFT    = 'plugin_linedraft';
    const TYPE_PLUGIN_ARTISTICFONT = 'plugin_artisticfont';
    const TYPE_PLUGIN_QRCODE       = 'plugin_qrcode';
    const TYPE_PLUGIN_BLEND        = 'plugin_blend';
    const TYPE_PLUGIN_TRANSFER     = 'plugin_transfer';
    const TYPE_PLUGIN_MIRROR       = 'plugin_mirror';
    const TYPE_PRODUCT_GEN         = 'product_gen';
    const TYPE_PRODUCT_CAPTURE     = 'product_capture';
    const TYPE_EDIT_REDRAW         = 'edit_redraw';
    const TYPE_EDIT_RECANCEL       = 'edit_recancel';
    const TYPE_EDIT_MAGNIFY        = 'edit_magnify';
    const TYPE_EDIT_ENHANCE        = 'edit_enhance';
    const TYPE_EDIT_DEFINITION     = 'edit_definition';
    const TYPE_EDIT_REPAIR         = 'edit_repair';
    const TYPE_EDIT_COLOUR         = 'edit_colour';
    const TYPE_EDIT_IMG_LINE       = 'edit_img_line';
    const TYPE_EDIT_IMG_SKETCH     = 'edit_img_sketch';
    const TYPE_EDIT_ANALYSIS       = 'edit_analysis';
    const TYPE_EDIT_JIGSAW         = 'edit_jigsaw';

    const TYPE_PLUGIN = [
        self::TYPE_PLUGIN_FACE         => 'AI换脸',//火山
        self::TYPE_PLUGIN_EXPAND       => 'AI扩图',//火山
        self::TYPE_PLUGIN_ANIME        => 'AI图片转动漫',//火山
        self::TYPE_PLUGIN_LINEDRAFT    => 'AI线稿渲染',//MJ
        self::TYPE_PLUGIN_ARTISTICFONT => 'AI创意文字',
        self::TYPE_PLUGIN_QRCODE       => 'AI二维码',
        self::TYPE_PLUGIN_BLEND        => 'AI图片融合',
        self::TYPE_PLUGIN_TRANSFER     => 'AI风格迁移',//火山
        self::TYPE_PLUGIN_MIRROR       => 'AI写真',
        self::TYPE_PRODUCT_GEN         => 'AI商品图',//火山
        self::TYPE_PRODUCT_CAPTURE     => 'AI抠图',
        self::TYPE_EDIT_REDRAW         => 'AI局部重绘',//火山
        self::TYPE_EDIT_RECANCEL       => 'AI局部消除',//火山
        self::TYPE_EDIT_MAGNIFY        => 'AI无损放大',
        self::TYPE_EDIT_ENHANCE        => 'AI画质增强',//火山
        self::TYPE_EDIT_DEFINITION     => 'AI动漫细节修复',
        self::TYPE_EDIT_REPAIR         => 'AI老照片修复',//火山
        self::TYPE_EDIT_COLOUR         => 'AI老照片上色',
        self::TYPE_EDIT_IMG_LINE       => 'AI图片转线稿',
        self::TYPE_EDIT_IMG_SKETCH     => 'AI图片转素描',
        self::TYPE_EDIT_ANALYSIS       => 'AI图片解析',
        self::TYPE_EDIT_JIGSAW         => '拼图',
    ];

    const Score = [
        self::TYPE_PLUGIN_FACE         => 2,
        self::TYPE_PLUGIN_EXPAND       => 2,
        self::TYPE_PLUGIN_ANIME        => 4,
        self::TYPE_PLUGIN_LINEDRAFT    => 4,
        self::TYPE_PLUGIN_ARTISTICFONT => 2,
        self::TYPE_PLUGIN_QRCODE       => 2,
        self::TYPE_PLUGIN_BLEND        => 4,
        self::TYPE_PLUGIN_TRANSFER     => 2,
        self::TYPE_PLUGIN_MIRROR       => 2,
        self::TYPE_PRODUCT_GEN         => 2,
        self::TYPE_PRODUCT_CAPTURE     => 2,
        self::TYPE_EDIT_REDRAW         => 4,
        self::TYPE_EDIT_RECANCEL       => 4,
        self::TYPE_EDIT_MAGNIFY        => 4,
        self::TYPE_EDIT_ENHANCE        => 4,
        self::TYPE_EDIT_DEFINITION     => 4,
        self::TYPE_EDIT_REPAIR         => 4,
        self::TYPE_EDIT_COLOUR         => 4,
        self::TYPE_EDIT_IMG_LINE       => 2,
        self::TYPE_EDIT_IMG_SKETCH     => 2,
        self::TYPE_EDIT_ANALYSIS       => 2,
        self::TYPE_EDIT_JIGSAW         => 0,
    ];

    const TYPE_CATE = [
        [
            'key'     => 'edit',
            'title'   => '图片编辑',
            'plugins' => [
                self::TYPE_EDIT_REDRAW     => 'AI局部重绘',
                self::TYPE_EDIT_RECANCEL   => 'AI局部消除',
                self::TYPE_EDIT_MAGNIFY    => 'AI无损放大',
                self::TYPE_EDIT_ENHANCE    => 'AI画质增强',
                self::TYPE_EDIT_DEFINITION => 'AI动漫细节修复',
                self::TYPE_EDIT_REPAIR     => 'AI老照片修复',
                self::TYPE_EDIT_COLOUR     => 'AI老照片上色',
                self::TYPE_EDIT_IMG_LINE   => 'AI图片转线稿',
                self::TYPE_EDIT_IMG_SKETCH => 'AI图片转素描',
                self::TYPE_EDIT_ANALYSIS   => 'AI图片解析',
                self::TYPE_EDIT_JIGSAW     => '拼图',
            ]
        ],
        [
            'key'     => 'product',
            'title'   => '电商设计',
            'plugins' => [
                self::TYPE_PRODUCT_GEN     => 'AI商品图',
                self::TYPE_PRODUCT_CAPTURE => 'AI抠图',
            ]
        ],
        [
            'key'     => 'creativity',
            'title'   => '创意应用',
            'plugins' => [
                self::TYPE_PLUGIN_FACE         => 'AI换脸',
                self::TYPE_PLUGIN_EXPAND       => 'AI扩图',
                self::TYPE_PLUGIN_ANIME        => 'AI图片转动漫',
                self::TYPE_PLUGIN_LINEDRAFT    => 'AI线稿渲染',
                self::TYPE_PLUGIN_ARTISTICFONT => 'AI创意文字',
                self::TYPE_PLUGIN_QRCODE       => 'AI二维码',
                self::TYPE_PLUGIN_BLEND        => 'AI图片融合',
                self::TYPE_PLUGIN_TRANSFER     => 'AI风格迁移',
                self::TYPE_PLUGIN_MIRROR       => 'AI写真',
            ]
        ]
    ];
    public string $orderNoPrefix = 'PM';
    protected     $casts         = [
        'res'      => 'json',
        'inputs'   => 'json',
        'params'   => 'json',
        'over_at'  => 'datetime',
        'start_at' => 'datetime'
    ];

    protected static function boot()
    {
        parent::boot(); // TODO: Change the autogenerated stub
        self::creating(function ($model) {
            if (! isset($model->status)) {
                $model->status = AiUnifyAsset::STATUS_INIT;
            }
            $model->score = self::Score[$model->type] ?? 0;
            $user         = request()->kernel->guestUser();
            if (! $user) {
                throw new ValidatorException('用户尚未登陆');
            }
            if ($model->score > 0 && $user->account->score < $model->score) {
                throw new ValidatorException('积分不足');
            }
        });
        self::created(function ($model) {
            if ($model->score > 0) {
                $model->user->aiSpend('create_work', $model, -$model->score, [
                    'remark' => '创作内容['.$model->type_text.']',
                ]);
            }
        });
        self::deleted(function ($model) {
            blank($model->cover) ?: Storage::delete($model->cover);
            if ($model->status == AiUnifyAsset::STATUS_INIT && $model->score > 0) {
                $model->user->aiSpend('create_work_error', $model, $model->score, [
                    'remark' => '取消创作内容['.$model->type_text.']',
                ]);
            }
        });
    }

    public function getMediaTypeAttribute(): string
    {
        return match ($this->type) {
            self::TYPE_EDIT_ANALYSIS => 'text',
            default => 'image',
        };
    }

    public function getTypeTextAttribute(): string
    {
        return self::TYPE_PLUGIN[$this->type] ?? '';
    }

    public function pictureUrlAttr(): Attribute
    {
        return new Attribute(
            get: function () {
                return [$this->coverUrlAttr];
            }
        );
    }

    public function coverUrlAttr(): Attribute
    {
        return new Attribute(
            get: fn() => $this->parseImageUrl($this->getAttribute('cover'))
        );
    }

    public function cans(): array
    {
        return [
            'polling' => $this->canPolling(),
            'delete'  => $this->canDelete(),
        ];
    }

    public function canPolling(): bool
    {
        return in_array($this->status, [
            AiUnifyAsset::STATUS_ING,
            AiUnifyAsset::STATUS_INIT,
        ]);
    }

    public function canDelete(): bool
    {
        return in_array($this->status, [
            AiUnifyAsset::STATUS_INIT,
            AiUnifyAsset::STATUS_ERROR,
            AiUnifyAsset::STATUS_SUCCESS,
        ]);
    }

    public function getStatusTextAttribute()
    {
        return AiUnifyAsset::STATUS[$this->status] ?? '';
    }

    public function taskQuery()
    {
        if ($this->status == AiUnifyAsset::STATUS_SUCCESS) {
            return;
        }
        $result = Mj::task()->query($this);
        if ($result->isSuccess()) {
            if ($result->queue_progress) {
                $this->jobQuery();
                return;
            }
            $detail = $result->detail;
            if (in_array($detail['status'], [0, 1])) {
                $this->jobQuery();
                return;
            }
            if ($detail['status'] == 2) {
                $path        = json_decode($detail['path']);
                $this->cover = $this->saveStorage($path[0]);
                $size        = getimagesize(Storage::url($this->cover)); // 直接传递流资源
                $width       = $size[0] ?? 0;
                $height      = $size[1] ?? 0;
                $radio       = $this->getFFRadio($width, $height);
                if ($this->type == self::TYPE_EDIT_ANALYSIS) {
                    $prompt       = json_decode($detail['result']);
                    $this->prompt = $prompt[0];
                }
                $this->ext           = [
                    'width'  => $width,
                    'height' => $height,
                    'radio'  => $radio,
                ];
                $this->status        = AiUnifyAsset::STATUS_SUCCESS;
                $this->over_at       = now();
                $this->error_message = '';
                $this->save();
                $this->mjDelete();
                return;
            }
            $this->error($detail['fail_msg'].'-1');
        } else {
            $this->error($result->getMessage().'-2');
        }
    }

    public function jobQuery()
    {
        MjQueryJob::dispatch($this)->delay(now()->addSeconds(10));
    }

    public function saveStorage(string $url, string $extension = 'jpeg'): string
    {
        $client   = new Client();
        $response = $client->request('GET', $url, [
            'verify' => false,
        ]);
        if ($response->getStatusCode() == 200) {
            $content  = $response->getBody()->getContents();
            $filename = $this->no.'.'.$extension;
            $path     = $this->getOssPath();
            if (Storage::put($path.'/'.$filename, $content)) {
                return $path.'/'.$filename;
            } else {
                return '';
            }
        } else {
            return '';
        }
    }

    protected function getOssPath(): string
    {
        return sprintf("plugin-mj/%s/%s/%s",
            date('Y'),
            date('m'),
            date('d')
        );
    }

    public function mjDelete()
    {
        $result = Mj::task()->mjDelete($this);
        if ($result->isSuccess()) {
            return true;
        } else {
            info($result->getMessage());
        }
    }

    public function error(string $message)
    {
        if ($this->status == AiUnifyAsset::STATUS_ING) {
            $this->error_message = $message;
            $this->status        = AiUnifyAsset::STATUS_ERROR;
            $this->over_at       = now();
            $this->save();
            if (! blank($this->serial_no)) {
                $this->mjDelete();
            }
            if ($this->score > 0) {
                $this->user->aiSpend('create_work_error', $this, $this->score, [
                    'remark' => '创作失败['.$this->type_text.']',
                ]);
            }
        }
    }

    public function getExtAttribute()
    {
        return $this->res;
    }

    public function setExtAttribute(array $value)
    {
        $this->attributes['res'] = json_encode($value);
    }

    public function reDraw()
    {
        $this->status    = AiUnifyAsset::STATUS_INIT;
        $this->start_at  = null;
        $this->task_id   = null;
        $this->serial_no = null;
        $this->save();
        $this->draw();
    }

    public function draw()
    {
        if ($this->canDraw()) {
            $this->setIng();
            list($result, $async) = match ($this->type) {
                self::TYPE_PLUGIN_FACE => [Mj::VolcSync()->AIGCFaceSwapV1($this), false],
                self::TYPE_PLUGIN_EXPAND => [Mj::VolcSync()->Img2ImgOutpainting($this), false],
                self::TYPE_PLUGIN_ANIME => [Mj::VolcSync()->AIGCStylizeImage($this), false],
                self::TYPE_PRODUCT_GEN => [Mj::VolcSync()->Img2imgECommerceStyle($this), false],
                self::TYPE_EDIT_REDRAW => [Mj::VolcSync()->Img2ImgInpaintingEdit($this), false],
                self::TYPE_EDIT_RECANCEL => [Mj::VolcSync()->Img2ImgInpainting($this), false],
                self::TYPE_PLUGIN_TRANSFER => [Mj::VolcSync()->AIGCStylizeImage($this), false],
                self::TYPE_EDIT_ENHANCE => [Mj::VolcSync()->AILensLqirImage($this), false],
                self::TYPE_EDIT_REPAIR => [Mj::VolcSync()->AILensOprImage($this), false],
                default => [Mj::task()->createTask($this), true],
            };
            if ($async) {
                if ($result->isSuccess()) {
                    $this->task_id   = $result->id;
                    $this->serial_no = $result->serial_no;
                    $this->save();
                    $this->jobQuery();
                } else {
                    $this->error($result->getMessage());
                    throw new ValidatorException($result->getMessage());
                }
            } else {
                if (is_string($result)) {
                    $this->overCover($result, in_array($this->type, [
                        self::TYPE_EDIT_ENHANCE,
                        self::TYPE_EDIT_REPAIR
                    ]));
                } else {
                }
            }
        } else {
            throw new Exception('当前状态不可执行');
        }
    }

    public function canDraw(): bool
    {
        return $this->status == AiUnifyAsset::STATUS_INIT;
    }

    public function setIng()
    {
        $this->status   = AiUnifyAsset::STATUS_ING;
        $this->start_at = now();
        $this->save();
    }

    protected function overCover(string $url, bool $base64 = false)
    {
        $this->cover         = $base64 ? $this->saveStorageBase($url) : $this->saveStorage($url);
        $size                = getimagesize(Storage::url($this->cover)); // 直接传递流资源
        $width               = $size[0] ?? 0;
        $height              = $size[1] ?? 0;
        $radio               = $this->getFFRadio($width, $height);
        $this->ext           = [
            'width'  => $width,
            'height' => $height,
            'radio'  => $radio,
        ];
        $this->status        = AiUnifyAsset::STATUS_SUCCESS;
        $this->over_at       = now();
        $this->error_message = '';
        $this->save();
    }

    public function saveStorageBase(string $content, string $extension = 'jpeg'): string
    {
        $content  = base64_decode($content);
        $filename = $this->no.'.'.$extension;
        $path     = $this->getOssPath();
        if (Storage::put($path.'/'.$filename, $content)) {
            return $path.'/'.$filename;
        } else {
            return '';
        }
    }
}
