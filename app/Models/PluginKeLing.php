<?php

namespace App\Models;

use App\Contracts\AiLogIndexInterface;
use App\Exceptions\ValidatorException;
use App\Jobs\Plugin\KeLingQueryJob;
use App\Models\Traits\AssetTrait;
use App\Models\Traits\FFMpegVideoTrait;
use App\Packages\Plugin\Plugin;
use App\Traits\AutoCreateOrderNo;
use App\Traits\BelongsToUser;
use App\Traits\HasCovers;
use Exception;
use GuzzleHttp\Client;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\Relations\MorphOne;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Str;

class PluginKeLing extends Model implements AiLogIndexInterface
{
    use BelongsToUser, AutoCreateOrderNo, SoftDeletes, HasCovers, AssetTrait, FFMpegVideoTrait;

    const VIDEO   = 'video';
    const IMAGE   = 'image';
    const CHANNEL = [
        self::VIDEO => '视频',
        self::IMAGE => '图片',
    ];

    const TYPES = [
        'm2v_txt2video_hq'    => '高品质(文To视频)',
        'm2v_txt2video'       => '普通品质(文To视频)',
        'm2v_img2video_hq'    => '高品质(图To视频)',
        'm2v_img2video'       => '普通品质(图To视频)',
        'm2v_img2video_se_hq' => '创意特效',
        'm2v_video_lip_sync'  => '对口型',
        'mmu_txt2img_model'   => '模特生成',
        'mmu_img2img_aitryon' => '试装',
    ];
    const SCORE = [
        'm2v_img2video_se_hq' => 40,
        'mmu_txt2img_model'   => 10,
        'mmu_img2img_aitryon' => 10,
    ];
    public string $orderNoPrefix = 'PK';
    protected     $casts         = [
        'params'   => 'json',
        'ext'      => 'json',
        'over_at'  => 'datetime',
        'start_at' => 'datetime'
    ];

    protected static function boot()
    {
        parent::boot();
        self::creating(function ($model) {
            $model->score = self::SCORE[$model->type] ?? 0;
            $user         = request()->kernel->guestUser();
            if (! $user) {
                throw new ValidatorException('用户尚未登陆');
            }
            if ($model->score > 0 && $user->account->score < $model->score) {
                throw new ValidatorException('积分不足');
            }
        });
        self::created(function ($model) {
            if ($model->score > 0) {
                $model->user->aiSpend('create_work', $model, -$model->score, [
                    'remark' => '创作内容['.$model->type_text.']',
                ]);
            }
        });
        self::deleting(function ($model) {
            $model->logIndex()->delete();
        });
    }

    public function logIndex(): MorphOne
    {
        return $this->morphOne(AiLogIndex::class, 'target');
    }

    public function getMediaTypeAttribute(): string
    {
        return match ($this->type) {
            'm2v_txt2video_hq',
            'm2v_txt2video',
            'm2v_img2video_hq',
            'm2v_img2video',
            'm2v_img2video_se_hq',
            'm2v_video_lip_sync' => 'video',
            default => 'image',
        };
    }

    public function getTypeTextAttribute(): string
    {
        return self::TYPES[$this->type] ?? '';
    }

    public function coverUrlAttr(): Attribute
    {
        return new Attribute(
            get: fn() => $this->parseImageUrl($this->getAttribute('cover'))
        );
    }

    public function videoUrlAttr(): Attribute
    {
        return new Attribute(
            get: fn() => $this->parseImageUrl($this->getAttribute('video_url'))
        );
    }

    public function imageUrlAttr(): Attribute
    {
        return new Attribute(
            get: fn() => $this->parseImageUrl($this->getAttribute('image_url'))
        );
    }

    public function pictureUrlAttr(): Attribute
    {
        return new Attribute(
            get: function () {
                return [];
            }
        );
    }

    public function cans(): array
    {
        return [
            'polling' => $this->canPolling(),
            'delete'  => $this->canDelete(),
        ];
    }

    public function canPolling(): bool
    {
        return in_array($this->status, [
            AiUnifyAsset::STATUS_ING,
            AiUnifyAsset::STATUS_INIT,
        ]);
    }

    public function canDelete(): bool
    {
        return in_array($this->status, [
            AiUnifyAsset::STATUS_INIT,
            AiUnifyAsset::STATUS_ERROR,
            AiUnifyAsset::STATUS_SUCCESS,
        ]);
    }

    public function setVideoAttribute(string $value)
    {
        $this->attributes['video_url'] = $value;
    }

    public function canResetVideo()
    {
        return blank($this->hd_video) && ! blank($this->video_url) && $this->status == AiUnifyAsset::STATUS_SUCCESS;
    }

    public function resetVideo()
    {
        $orgVideo = $this->video_url;
        list($video, $hdVideo, $cover, $width, $height, $duration, $ext) = $this->genFFUrl($orgVideo,
            $this->getOssPath(),
            $this->no);
        $radio           = $this->getFFRadio($width, $height);
        $this->video_url = $video;
        $this->hd_video  = $hdVideo;
        $this->cover     = $cover;
        $this->ext       = [
            'duration' => (int) $duration,
            'width'    => $width,
            'height'   => $height,
            'radio'    => $radio,
            'ext'      => $ext,
        ];
        $this->save();
        Storage::delete($orgVideo);
    }

    public function getOssPath(): string
    {
        return sprintf("plugin-keling/%s/%s/%s",
            date('Y'),
            date('m'),
            date('d')
        );
    }

    public function getStatusTextAttribute()
    {
        return AiUnifyAsset::STATUS[$this->status] ?? '';
    }

    public function taskQuery(): void
    {
        if ($this->task_id && $this->status != AiUnifyAsset::STATUS_SUCCESS) {
            $result = Plugin::Keling()->taskQuery($this);
            if ($result->isSuccess()) {
                $data = $result->toArray();
                $task = $data['task'];
                if ($task['status'] == 6) {
                    $this->error($data['message']);
                }
                if ($task['status'] == 50) {
                    $this->error($data['message']);
                }
                $work = collect($data['works'])->where('taskId', $this->task_id)->first();
                if (blank($this->work_id) && count($work)) {
                    $this->work_id = $work['workId'];
                    $this->save();
                }
                if ($task['status'] == 5) {
                    $this->jobQuery();
                    return;
                }
                if ($task['status'] == 10) {
                    $this->jobQuery();
                    return;
                }

                if ($task['status'] == 99) {
                    if ($this->channel == self::VIDEO) {
                        $videoUrl = $work['resource']['resource'];
                        list($video, $hdVideo, $cover, $width, $height, $duration, $ext) = $this->genFFUrl($videoUrl,
                            $this->getOssPath(),
                            $this->no);
                        $radio           = $this->getFFRadio($width, $height);
                        $this->cover     = $cover;
                        $this->video_url = $video;
                        $this->hd_video  = $hdVideo;
                        $this->ext       = [
                            'duration' => (int) $duration,
                            'width'    => $width,
                            'height'   => $height,
                            'radio'    => $radio,
                            'ext'      => $ext,
                        ];
                    }
                    if ($this->channel == self::IMAGE) {
                        $imageUrl    = $this->saveStorage($work['resource']['resource']);
                        $this->cover = $imageUrl;
                        $size        = getimagesize(Storage::url($this->cover)); // 直接传递流资源
                        $width       = $size[0] ?? 0;
                        $height      = $size[1] ?? 0;
                        $radio       = $this->getFFRadio($width, $height);
                        $this->ext   = [
                            'width'  => $width,
                            'height' => $height,
                            'radio'  => $radio,
                        ];
                    }
                    $this->status  = AiUnifyAsset::STATUS_SUCCESS;
                    $this->over_at = now();
                    $this->save();
                    $this->klDelete();
                }
            }
        }
    }

    public function error(string $message)
    {
        $this->error_message = $message;
        $this->status        = AiUnifyAsset::STATUS_ERROR;
        $this->over_at       = now();
        $this->save();
        if (! blank($this->task_id)) {
            $this->klDelete();
        }
        if ($this->score > 0) {
            $this->user->aiSpend('create_work_error', $this, $this->score, [
                'remark' => '创作资产失败【'.$this->type_text.'】',
            ]);
        }
    }

    public function klDelete()
    {
        try {
            $result = Plugin::Keling()->delete($this);
        } catch (Exception $exception) {
        }
    }

    public function jobQuery()
    {
        KeLingQueryJob::dispatch($this)->delay(now()->addSeconds(20));
    }

    public function saveStorage(string $url): string
    {
        $client   = new Client();
        $response = $client->request('GET', $url, [
            'verify' => false,
        ]);
        if ($response->getStatusCode() == 200) {
            $content  = $response->getBody()->getContents();
            $filename = Str::afterLast($url, '/');
            if (Str::contains($filename, '?')) {
                $filename = Str::before($filename, '?');
            }
            $ext      = Str::afterLast($filename, '.');
            $filename = $this->task_id.'.'.$ext;
            $path     = $this->getOssPath();
            if (Storage::put($path.'/'.$filename, $content)) {
                return $path.'/'.$filename;
            } else {
                return '';
            }
        } else {
            return '';
        }
    }

    public function getResAttribute()
    {
        return $this->ext;
    }

    public function draw()
    {
        if ($this->canDraw()) {
            $result = Plugin::Keling()->imageToVideo($this);
            if ($result->isSuccess()) {
                $data = $result->toArray();
                if ($data['status'] == 6) {
                    throw new Exception($data['message']);
                }
                $this->setIng($result->getTaskId());
            }
        } else {
            throw new Exception('当前状态不可执行');
        }
    }

    public function canDraw(): bool
    {
        return $this->status == AiUnifyAsset::STATUS_INIT;
    }

    public function setIng(string $taskId)
    {
        $this->task_id  = $taskId;
        $this->status   = AiUnifyAsset::STATUS_ING;
        $this->start_at = now();
        $this->save();
        $this->jobQuery();
    }

    public function getLogType(): string
    {
        return $this->channel;
    }

    public function getSystemMessage(): array
    {
        return [
            'message' => '',
            'image'   => $this->coverUrlAttr,
            'video'   => $this->videoUrlAttr,
        ];
    }

    public function getUserMessage(): array
    {
        return [
            'message' => $this->prompt,
            'image'   => $this->image_url,
        ];
    }

    public function getLogStatus(): int
    {
        return $this->status;
    }

    public function getLogStatusText(): string
    {
        return $this->status_text;
    }

    public function getExtend(): array
    {
        return [
            'no'        => $this->no,
            'type'      => $this->type,
            'type_text' => $this->type_text,
        ];
    }
}
