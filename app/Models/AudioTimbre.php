<?php

namespace App\Models;

use App\Traits\BelongsToUser;
use App\Traits\HasEasyStatus;
use Illuminate\Database\Eloquent\Casts\Attribute;
use Illuminate\Database\Eloquent\SoftDeletes;

/**
 * @method static ofUser($user)
 */
class AudioTimbre extends Model
{
    use BelongsToUser, HasEasyStatus, SoftDeletes;

    public function urlUrlAttr(): Attribute
    {
        return new Attribute(
            get: fn() => $this->parseImageUrl($this->getAttribute('url'))
        );
    }

    public function imageUrlAttr(): Attribute
    {
        return new Attribute(
            get: fn() => $this->parseImageUrl($this->getAttribute('image_url'))
        );
    }
}
