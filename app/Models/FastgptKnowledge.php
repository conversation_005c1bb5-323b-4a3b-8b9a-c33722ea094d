<?php

namespace App\Models;

use App\Models\Enums\FastgptKnowledgeLevelEnum;
use App\Models\Traits\CanKnowledge;
use App\Models\Traits\FastGptScope;
use App\Traits\BelongsToUser;
use App\Traits\HasEasyStatus;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Modules\Company\Models\Traits\BelongsToCompany;
use Modules\User\Models\Department;

class FastgptKnowledge extends Model
{
    use BelongsToUser,
        HasEasyStatus,
        BelongsToCompany,
        SoftDeletes,
        FastGptScope,
        CanKnowledge;

    protected $table = 'fastgpt_knowledge';
    protected $casts = [
        'source' => 'array',
        'status' => 'integer',
        'level'  => FastgptKnowledgeLevelEnum::class
    ];

    public function department(): BelongsTo
    {
        return $this->belongsTo(Department::class);
    }

    /**
     * Notes: 关联写作者-没用上
     *
     * @Author: 玄尘
     * @Date: 2025/2/25 16:38
     * @return \Illuminate\Database\Eloquent\Relations\BelongsToMany
     */
    public function collaborators(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'fastgpt_knowledge_collaborators', 'knowledge_id', 'user_id');
    }

    public function fastgptCollaborators(): HasMany
    {
        return $this->hasMany(FastgptKonwledgeCollaborator::class, 'knowledge_id');
    }

    public function knowledgeSets()
    {
        return $this->hasMany(FastgptKnowledgeSet::class, 'knowledge_id');
    }

}
