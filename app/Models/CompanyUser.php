<?php

namespace App\Models;

use App\Models\Traits\BelongsToCompany;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasOne;
use Illuminate\Database\Eloquent\SoftDeletes;

class CompanyUser extends Model
{
    use SoftDeletes,
        BelongsToCompany;

    protected $table = 'company_user';

    const IS_WORK = [
        0 => '在职',
        1 => '离职'
    ];

    const IS_MANAGE = [
        0 => '普通员工',
        1 => '管理员',
        2 => '群主',
    ];

    const IS_CHECK = [
        0 => '待审核',
        1 => '审核通过',
        2 => '审核拒绝',
    ];

    const TYPE_MAP = [
        0 => '创始人',
        1 => '邀请码',
        2 => '工牌',
        3 => '名片',
        4 => '营业执照',
        9 => '待激活'
    ];

    public function user(): HasOne
    {
        return $this->hasOne(User::class, 'id', 'uid');
    }

    public function business(): BelongsTo
    {
        return $this->belongsTo(Business::class, 'bid');
    }

    public function scopeOfTypeSearch(Builder $query, string $type): Builder
    {
        switch ($type) {
            case 1:
                return $query->where('is_check', 0);
                break;
            case 2:
                return $query->where('is_work', 0)->where('is_check', 1);
                break;
            case 3:
                return $query->where('is_check', 1)->where('is_work', 1);
                break;
        }
    }

    public function getTypeNameAttribute(): string
    {
        switch ($this->type) {
            case 0:
                return "创始人";
            case 1:
                return "邀请码加入";
            case 2:
                return "工牌加入";
            case 3:
                return "名片加入";
            case 4:
                return "营业执照加入";
            case 5:
                return "系统录入";
            default:
                return "其他方式";
        }
    }

}
