<?php

namespace App\Models;

use App\Traits\HasCovers;
use App\Traits\HasEasyStatus;
use Illuminate\Database\Eloquent\Casts\Attribute;

class AudioXfSystemTimbre extends Model
{
    use HasCovers, HasEasyStatus;

    public function urlUrlAttr(): Attribute
    {
        return new Attribute(
            get: fn() => $this->parseImageUrl($this->getAttribute('url'))
        );
    }

    public function imageUrlAttr(): Attribute
    {
        return new Attribute(
            get: fn() => $this->parseImageUrl($this->getAttribute('image_url'))
        );
    }
}
