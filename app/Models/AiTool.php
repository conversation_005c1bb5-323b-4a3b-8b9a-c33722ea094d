<?php

namespace App\Models;

use App\Traits\HasCovers;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Spatie\EloquentSortable\SortableTrait;

class AiTool extends Model
{
    use SortableTrait, HasCovers;

    protected array $sortable = [
        'order_column_name'  => 'order',
        'sort_when_creating' => true,
    ];

    public function category(): BelongsTo
    {
        return $this->belongsTo(AiToolCategory::class, 'category_id');
    }
}
