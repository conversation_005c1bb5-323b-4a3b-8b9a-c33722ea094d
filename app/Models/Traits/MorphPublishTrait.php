<?php

namespace App\Models\Traits;

use App\Exceptions\ValidatorException;
use App\Models\PublishMedium;
use App\Models\PublishTag;
use Illuminate\Database\Eloquent\Relations\MorphOne;

trait MorphPublishTrait
{
    protected static function bootMorphPublishTrait(): void
    {
        self::deleting(function ($model) {
            $model->publish()->delete();
        });
    }

    public function publish(): MorphOne
    {
        return $this->morphOne(PublishMedium::class, 'item');
    }

    public function canPublishTo(): bool
    {
        return $this->publish()->doesntExist();
    }

    public function canPublishDown(): bool
    {
        return $this->publish()->exists();
    }

    public function publishTo(string $title, int $categoryId, string $description, array $tags = []): void
    {
        if ($this->publish) {
            throw new ValidatorException('已经发布过了');
        }
        $tags = array_filter($tags);
        if (count($tags) > 0) {
            $inTags = PublishTag::whereIn('name', $tags)->pluck('name');
            $unTags = array_diff($tags, $inTags->toArray());
            PublishTag::insert(collect($unTags)->map(function ($item) {
                return [
                    'name'       => $item,
                    'created_at' => now()->toDateTimeString(),
                ];
            })->toArray());
            PublishTag::whereIn('name', $tags)->increment('used_count');
        }

        $this->publish()->create([
            'item_type'   => $this->getMorphClass(),
            'item_id'     => $this->getKey(),
            'user_id'     => $this->user_id,
            'type'        => $this->getPublishType(),
            'title'       => $title,
            'description' => $description,
            'category_id' => $categoryId,
            'tags'        => $tags ?: null,
            'cover'       => (method_exists($this, 'getPublishCover') ? $this->getPublishCover() : null),
            'pictures'    => (method_exists($this, 'getPublishPictures') ? $this->getPublishPictures() : null),
            'audio'       => (method_exists($this, 'getPublishAudio') ? $this->getPublishAudio() : null),
            'video'       => (method_exists($this, 'getPublishVideo') ? $this->getPublishVideo() : null),
            'ext'         => (method_exists($this, 'getPublishExt') ? $this->getPublishExt() : null),
            'status'      => 1,
        ]);
    }

    public function publishDown()
    {
        if (! $this->publish) {
            throw new ValidatorException('当前素材尚未发布');
        }
        $this->publish()->delete();
    }
}