<?php

namespace App\Models\Traits;

use App\Services\KnowledgeItemService;
use Exception;

/**
 * 知识库项目操作Trait - 重构为服务类代理
 */
trait KnowledgeItemOperationTrait
{
    /**
     * 将项目加入知识库
     *
     * @param  int  $knowledgeId  知识库ID
     * @param  string|null  $categoryId  分类ID
     * @param  array  $options  其他选项
     * @return self
     * @throws Exception
     */
    public function addToKnowledgeBase(
        int $knowledgeId,
        string $categoryId = null,
        array $options = [],
    ): self {
        $service = app(KnowledgeItemService::class);
        return $service->addToKnowledgeBase($this, $knowledgeId, $categoryId, $options);
    }

    /**
     * 从知识库中移除项目
     *
     * @param  bool  $is_self  是否删除自己
     * @return bool
     * @throws Exception
     */
    public function removeFromKnowledge(bool $is_self = false): bool
    {
        $service = app(KnowledgeItemService::class);
        return $service->removeFromKnowledge($this, $is_self);
    }
}
