<?php

namespace App\Models\Traits;

use App\Events\KnowledgeContentUpdatedEvent;
use App\Models\BailianKnowledge;
use Exception;
use Log;

/**
 * 知识库项目工具方法Trait
 */
trait KnowledgeItemUtilityTrait
{
    /**
     * 获取文件大小
     */
    public function getSize(): int
    {
        if ($this->storage) {
            return $this->storage->size;
        }
        return 0;
    }

    /**
     * 格式化文件大小
     */
    public function getReadableSizeAttribute(): string
    {
        return formatBytes($this->getSize());
    }

    /**
     * 获取下载链接
     */
    public function getDownloadUrl(): ?string
    {
        return $this->storage?->path_url;
    }

    /**
     * 获取标题 - 如果模型有自己的getTitle方法，会被覆盖
     */
    public function getTitle(): string
    {
        // 如果模型有自己的getTitle方法，这个方法会被覆盖
        return $this->title ?? $this->name ?? '';
    }

    /**
     * 获取描述
     */
    public function getDescription(): string
    {
        return $this->description ?? '';
    }

    /**
     * 触发知识库内容更新事件
     *
     * @param BailianKnowledge $knowledge 知识库
     * @param string $updateType 更新类型：created, updated, deleted
     */
    public function fireKnowledgeContentUpdatedEvent(BailianKnowledge $knowledge, string $updateType): void
    {
        try {
            // 获取项目标题
            $itemTitle = $this->getTitle();

            // 触发事件
            KnowledgeContentUpdatedEvent::dispatch($knowledge, $updateType, $itemTitle);
        } catch (Exception $e) {
            Log::error('触发知识库内容更新事件失败', [
                'knowledge_id' => $knowledge->id,
                'update_type' => $updateType,
                'item_id' => $this->id,
                'item_type' => class_basename($this),
                'error' => $e->getMessage()
            ]);
        }
    }
}
