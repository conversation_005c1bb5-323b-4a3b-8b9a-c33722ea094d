<?php

namespace App\Models\Traits;

use App\Enums\Bailian\BailianLevelEnum;

trait CanBailianKnowledge
{

    public function getCan($user): array
    {
        return [
            'content' => $this->canSetData($user),//维护内容
            'view'    => $this->canView($user),//查看
            'edit'    => $this->canEdit($user),//修改
            'delete'  => $this->canDelete($user),//删除
            'status'  => $this->canStatus($user),//状态
            'level'   => $this->canLevel($user),//等级
            'members' => $this->canMembers($user),//成员
            'setData' => $this->canSetData($user),//维护内容
        ];
    }

    public function canMembers($user)
    {
        return $this->isMyKnowledge($user) ?? null;
    }

    public function canEdit($user)
    {
        return $this->isMyKnowledge($user);
    }

    public function canStatus($user)
    {
        return $this->canEdit($user);
    }

    public function canLevel($user)
    {
        return $this->canEdit($user);
    }

    public function canView($user): bool
    {
        //如果是公开的
        if ($this->level == BailianLevelEnum::PUBLIC) {
            return true;
        }
        if (! $user) {
            return false;
        }
        //是私有的
        if ($this->level == BailianLevelEnum::PRIVATE) {
            return $this->isMyKnowledge($user);
        }
        //是成员可见的
        $userIds        = $this->members()->pluck('user_id')->toArray();
        $allowedUserIds = array_unique(array_merge($userIds, [$this->user_id]));
        return in_array($user?->id, $allowedUserIds);
    }

    public function canDelete($user)
    {
        return $this->isMyKnowledge($user);
    }

    public function canSetData($user)
    {
        return $this->isMyKnowledge($user);
    }

    public function isMyKnowledge($user)
    {
        return $user && $this->user->is($user);
    }

}