<?php

namespace App\Models\Traits;

use App\Models\Business;
use App\Models\CompanyStaff;
use App\Models\Knowledge;
use App\Models\Realname;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\Relations\HasOne;

trait HasOtherRelation
{
    public function knowledges(): Has<PERSON>any
    {
        return $this->hasMany(Knowledge::class, 'uid');
    }

    public function realName(): HasOne
    {
        return $this->hasOne(Realname::class, 'uid');
    }

    public function businesses(): HasMany
    {
        return $this->hasMany(Business::class, 'uid');
    }

    public function companyStaffs(): HasMany
    {
        return $this->hasMany(CompanyStaff::class, 'uid');
    }

}