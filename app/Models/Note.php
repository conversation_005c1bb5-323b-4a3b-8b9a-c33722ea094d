<?php

namespace App\Models;

use App\Models\Traits\InteractionTrait;
use App\Traits\BelongsToUser;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasOne;

class Note extends Model
{
    use BelongsToUser, InteractionTrait;

    const TYPE_TEXT = 1;
    //代办
    const TYPE_TODO     = 2;
    const TYPE_DOCUMENT = 3;
    const TYPE_MAP      = [
        self::TYPE_TEXT     => '文本',
        self::TYPE_TODO     => '代办',
        self::TYPE_DOCUMENT => '文档'
    ];
    const TYPE_LABEL    = [
        self::TYPE_TEXT     => 'info',
        self::TYPE_TODO     => 'warning',
        self::TYPE_DOCUMENT => 'success',
    ];

    public function getTypeTextAttribute()
    {
        return self::TYPE_MAP[$this->type] ?? '';
    }

    public function tags(): BelongsToMany
    {
        return $this->belongsToMany(NoteTag::class, 'note_taggable');
    }

    public function tagTaggables()
    {
        return $this->hasMany(NoteTaggable::class, 'note_id');
    }

    public function knowledgeSet(): HasOne
    {
        return $this->hasOne(FastgptKnowledgeSet::class, 'note_id');
    }

    public function getContent()
    {
        $note = $this->refresh();
        if ($note->type != self::TYPE_TEXT) {
            return '';
        }

        $content = $note->content;
        $content = json_decode($content, true);
        if (is_array($content)) {
            $info = $content[0];
            if (isset($info['insert'])) {
                return $info['insert'];
            }
            return $info;
        }

        return $content;
    }

    public function getCan($user): array
    {
        return [
            'edit'           => $this->canEdit($user),
            'delete'         => $this->canDelete($user),
            'move_knowledge' => $this->canDelete($user) && ! $this->knowledgeSet,
        ];
    }

    public function canEdit($user): bool
    {
        return $this->user_id == $user?->id;
    }

    public function canDelete($user)
    {
        return $this->user_id == $user?->id;
    }

    public function getTitle()
    {
        if ($this->title) {
            return $this->title;
        }
        $markdown  = trim($this->content, '"');
        $markdown  = str_replace('\\n', "\n", $markdown);
        $firstLine = explode("\n", $markdown)[0];
        $title     = trim(preg_replace('/^#+/', '', $firstLine));
        if ($title) {
            return $title;
        }
        return $this->title;
    }

}
