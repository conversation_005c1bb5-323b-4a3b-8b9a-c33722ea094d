<?php

namespace App\Models;

class TokenLog extends Model
{
    protected $table = 'token_log';
    const TYPE_TEXT  = 1;
    const TYPE_VIDEO = 2;
    const TYPE_DRAW  = 3;
    const TYPE       = [
        self::TYPE_TEXT  => '文本对话',
        self::TYPE_VIDEO => '视频生成',
        self::TYPE_DRAW  => '图形生成',
    ];
    protected $json = [
        'content'
    ];

    public function getTypeTextAttr($value, $data)
    {
        return self::TYPE[$data['type']];
    }

    public function getContent()
    {
        $content = $this->getData('content');
        $type    = $this->getData('type');
        return match ($type) {
            1 => sprintf("问题：%s", str_limit($content->question, 20)),
            2, 3 => sprintf("描述：%s", str_limit($content->prompt, 20)),
            default => '',
        };
    }
}
