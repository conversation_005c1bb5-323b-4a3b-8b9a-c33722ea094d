<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class Knowledge extends Model
{
    protected $table = 'knowledge';

    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class, 'uid');
    }

    public function files(): HasMany
    {
        return $this->hasMany(KnowledgeFile::class, 'kid');
    }

}
