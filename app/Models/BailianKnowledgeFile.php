<?php

namespace App\Models;

use App\Packages\BailianAssistant\Tools\KnowledgeTool;
use App\Traits\BelongsToUser;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Modules\Storage\Models\Upload;

class BailianKnowledgeFile extends Model
{
    use BelongsToUser;

    const STATUS_INIT                  = 'INIT';
    const STATUS_PARSING               = 'PARSING';
    const STATUS_PARSE_SUCCESS         = 'PARSE_SUCCESS';
    const STATUS_PARSE_FAILED          = 'PARSE_FAILED';
    const STATUS_SAFE_CHECKING         = 'SAFE_CHECKING';
    const STATUS_SAFE_CHECK_FAILED     = 'SAFE_CHECK_FAILED';
    const STATUS_INDEX_BUILDING        = 'INDEX_BUILDING';
    const STATUS_INDEX_BUILD_SUCCESS   = 'INDEX_BUILD_SUCCESS';
    const STATUS_INDEX_BUILDING_FAILED = 'INDEX_BUILDING_FAILED';
    const STATUS_INDEX_DELETED         = 'INDEX_DELETED';
    const STATUS_FILE_IS_READY         = 'FILE_IS_READY';

    const STATUS_MAP   = [
        self::STATUS_INIT                  => '待解析',
        self::STATUS_PARSING               => '解析中',
        self::STATUS_PARSE_SUCCESS         => '解析完成',
        self::STATUS_PARSE_FAILED          => '解析失败',
        self::STATUS_SAFE_CHECKING         => '安全检测中',
        self::STATUS_SAFE_CHECK_FAILED     => '安全检测失败',
        self::STATUS_INDEX_BUILDING        => '索引构建中',
        self::STATUS_INDEX_BUILD_SUCCESS   => '索引构建成功',
        self::STATUS_INDEX_BUILDING_FAILED => '索引构建失败',
        self::STATUS_INDEX_DELETED         => '文件索引已删除',
        self::STATUS_FILE_IS_READY         => '文件准备完毕',
    ];
    const STATUS_LABEL = [
        self::STATUS_INIT                  => 'info',
        self::STATUS_PARSING               => 'info',
        self::STATUS_PARSE_SUCCESS         => 'success',
        self::STATUS_PARSE_FAILED          => 'error',
        self::STATUS_SAFE_CHECKING         => 'info',
        self::STATUS_SAFE_CHECK_FAILED     => 'error',
        self::STATUS_INDEX_BUILDING        => 'info',
        self::STATUS_INDEX_BUILD_SUCCESS   => 'success',
        self::STATUS_INDEX_BUILDING_FAILED => 'error',
        self::STATUS_INDEX_DELETED         => 'error',
        self::STATUS_FILE_IS_READY         => 'success',
    ];

    const CATEGORY_TYPE_UNSTRUCTURED = 'UNSTRUCTURED';
    const CATEGORY_TYPE_SESSION_FILE = 'SESSION_FILE';

    const CATEGORY_TYPES = [
        self::CATEGORY_TYPE_UNSTRUCTURED => '非结构化',
        self::CATEGORY_TYPE_SESSION_FILE => '智能体',
    ];

    //可以加入知识库的类型
    const CAN_UPLOAD_FILE_TYPES = [
        'pdf',
        'docx',
        'doc',
        'txt',
        'md',
        'pptx',
        'ppt',
        'xlsx',
        'xls',
        'html',
        'png',
        'jpg',
        'jpeg',
        'bmp',
        'gif',
    ];

    public function getStatusTextAttribute()
    {
        return BailianKnowledgeFile::STATUS_MAP[$this->status] ?? '未知';
    }

    public function sourceable(): MorphTo
    {
        return $this->morphTo();
    }

    public function storage(): BelongsTo
    {
        return $this->belongsTo(Upload::class, 'storage_id');
    }

    public function category(): BelongsTo
    {
        return $this->belongsTo(BailianCategory::class, 'bailian_category_id', 'bailian_id');
    }

    public function bailianKnowledge(): BelongsTo
    {
        return $this->belongsTo(BailianKnowledge::class, 'knowledge_id');
    }

    //重置文件状态
    public function resetStatus()
    {
        $res  = app(KnowledgeTool::class)->getFileStatus($this->file_id);
        $data = $res->data;
        if ($res->status != $data->status) {
            $this->update(['status' => $data->status]);
        }
    }

}
