<?php

namespace App\Models;

use App\Traits\HasEasyStatus;
use GeneaLabs\LaravelModelCaching\Traits\Cachable;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Spatie\EloquentSortable\Sortable;
use <PERSON><PERSON>\EloquentSortable\SortableTrait;

class RechargePackage extends Model implements Sortable
{
    use Sorta<PERSON>Trait, HasEasyStatus, Cachable;

    protected array $sortable = [
        'order_column_name'  => 'order',
        'sort_when_creating' => true,
    ];

    protected static function boot()
    {
        parent::boot();
        self::saving(function ($model) {
            $model->score = $model->base + $model->gift;
            if ($model->is_default) {
                RechargePackage::where('is_default', 1)
                    ->where('id', '!=', $model->id)
                    ->update([
                        'is_default' => 0
                    ]);
            }
        });
    }

    public function orders(): HasMany
    {
        return $this->hasMany(RechargeOrder::class, 'package_id', 'id');
    }
}
