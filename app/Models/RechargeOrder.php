<?php

namespace App\Models;

use App\Events\RechargeOrderPaidEvent;
use App\Traits\AutoCreateOrderNo;
use App\Traits\BelongsToUser;
use Exception;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Support\Facades\DB;
use Modules\Payment\Traits\HasPayment;

class RechargeOrder extends Model
{
    use BelongsToUser, AutoCreateOrderNo, HasPayment;

    const STATUS_INIT   = 0;
    const STATUS_PAID   = 1;
    const STATUS_SETTLE = 6;
    const STATUS_CANCEL = 9;
    const STATUS        = [
        self::STATUS_INIT   => '待支付',
        self::STATUS_PAID   => '已支付',
        self::STATUS_SETTLE => '已结算',
        self::STATUS_CANCEL => '已取消',
    ];

    protected $casts = [
        'source'    => 'json',
        'paid_at'   => 'datetime',
        'settle_at' => 'datetime'
    ];

    public function package(): BelongsTo
    {
        return $this->belongsTo(RechargePackage::class, 'package_id', 'id');
    }

    public function paid($paidAt = '')
    {
        if ($this->status != self::STATUS_INIT) {
            throw new Exception('订单状态不正确');
        }
        DB::transaction(function () use ($paidAt) {
            $this->status  = self::STATUS_PAID;
            $this->paid_at = now();
            $this->save();
            event(new RechargeOrderPaidEvent($this));
        });
    }

    public function getTotalAmount()
    {
        return $this->price;
    }

    public function getTitle()
    {
        return $this->package->name;
    }

    public function getOrderNumber()
    {
        return $this->score;
    }

}
