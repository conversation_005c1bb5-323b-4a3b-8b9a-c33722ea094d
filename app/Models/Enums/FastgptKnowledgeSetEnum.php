<?php

namespace App\Models\Enums;

use App\Traits\EnumMethods;

enum FastgptKnowledgeSetEnum: string
{
    use EnumMethods;

    case EMPTY     = 'empty';
    case TEXT      = 'text';
    case LINK      = 'link';
    case FILE      = 'file';
    case NOTE      = 'note';
    case ARTICLE   = 'article';
    case DIRECTORY = 'directory';

    const SET_TYPE_MAP = [
        self::EMPTY->value     => '空集合',
        self::TEXT->value      => '文本集合',
        self::LINK->value      => '链接集合',
        self::FILE->value      => '文件集合',
        self::NOTE->value      => '小记',
        self::ARTICLE->value   => '文档',
        self::DIRECTORY->value => '目录',
    ];

    const SET_TYPE_MAP_LABEL = [
        self::EMPTY->value     => 'info',
        self::TEXT->value      => 'primary',
        self::LINK->value      => 'success',
        self::FILE->value      => 'warning',
        self::NOTE->value      => 'info',
        self::ARTICLE->value   => 'info',
        self::DIRECTORY->value => 'info',
    ];

    public function toString(): string
    {
        return self::SET_TYPE_MAP[$this->value];
    }
}