<?php

namespace App\Models\Enums;

use App\Traits\EnumMethods;

enum FastgptKnowledgeLevelEnum: string
{
    use EnumMethods;

    case ALL        = 'all';
    case COMPANY    = 'company';
    case DEPARTMENT = 'department';
    case USER       = 'user';

    const LEVEL_MAP   = [
        self::ALL->value        => '公开',
        self::COMPANY->value    => '集团协作型',
        self::DEPARTMENT->value => '部门协作型',
        self::USER->value       => '私人',
    ];
    const LEVEL_LABEL = [
        self::ALL->value        => 'info',
        self::COMPANY->value    => 'primary',
        self::DEPARTMENT->value => 'success',
        self::USER->value       => 'danger',
    ];

    public function toString(): string
    {
        return self::LEVEL_MAP[$this->value];
    }
}