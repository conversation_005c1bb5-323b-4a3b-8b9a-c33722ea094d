<?php

namespace App\Models;

class ImPushLog extends Model
{
    const STATUS_SUCCESS = 1;
    const STATUS_FAILURE = 0;

    const STATUS_MAP = [
        self::STATUS_SUCCESS => '成功',
        self::STATUS_FAILURE => '失败',
    ];

    protected $casts = [
        'params' => 'json',
        'query'  => 'json',
        'result' => 'json'
    ];

    protected $model = [
        Business::class     => '名片',
        PluginJmDraw::class => '文生视频',
    ];

    public function getParams()
    {
        return $this->params;
    }
}
