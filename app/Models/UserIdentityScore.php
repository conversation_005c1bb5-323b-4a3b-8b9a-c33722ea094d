<?php

namespace App\Models;

use App\Traits\BelongsToUser;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Modules\User\Models\IdentityOrder;

class UserIdentityScore extends Model
{
    use BelongsToUser;

    protected $casts = [
        'send_at' => 'datetime',
    ];

    public function order(): BelongsTo
    {
        return $this->belongsTo(IdentityOrder::class, 'order_id');
    }

    public function send()
    {
        if ($this->status === 0) {
            dump($this->status);
            $this->user->account->exec('identity_score',
                $this->score,
                $this->send_at->addMonth()->subDay()->endOfDay(), [
                    'order_id'   => $this->order_id,
                    'order_type' => $this->order->getMorphClass(),
                    'period'     => $this->period,
                ]);
            $this->status  = 1;
            $this->over_at = now();
            $this->save();
        }
    }
}
