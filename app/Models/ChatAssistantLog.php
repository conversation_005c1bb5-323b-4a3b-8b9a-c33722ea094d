<?php

namespace App\Models;

use App\Traits\BelongsToUser;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class ChatAssistantLog extends Model
{
    use BelongsToUser, SoftDeletes;

    protected $casts = [
        'intent'       => 'json',
        'tools'        => 'json',
        'intent_token' => 'json',
        'tools_token'  => 'json',
    ];

    protected static function boot()
    {
        parent::boot(); // TODO: Change the autogenerated stub
        self::forceDeleted(function ($model) {
            $model->manyTools()->delete();
        });
    }

    public function manyTools(): HasMany
    {
        return $this->hasMany(ChatAssistantTools::class, 'log_id');
    }

    public function group(): BelongsTo
    {
        return $this->belongsTo(ChatAssistant::class, 'chat_assistant_id');
    }
}
