<?php

namespace App\Models;

use App\Interfaces\BaiLianKnowledgeItemItemableInterface;
use App\Models\Traits\BaiLianKnowledgeItemItemableCanDo;
use App\Models\Traits\KnowledgeItemTrait;
use App\Traits\BelongsToUser;

class BailianDirectory extends Model implements BaiLianKnowledgeItemItemableInterface
{
    use BelongsToUser, BaiLianKnowledgeItemItemableCanDo, KnowledgeItemTrait;

    public function getFileTypes(): array
    {
        return [
            'extension' => '',
            'mimeType'  => '',
            'icon'      => 'directory',
            'icon_type' => 'directory',
            'name'      => '',
        ];
    }

    public function getDescription()
    {
        return '';
    }
}
