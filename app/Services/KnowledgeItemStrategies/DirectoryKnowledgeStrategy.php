<?php

namespace App\Services\KnowledgeItemStrategies;

use App\Enums\Bailian\ItemSyncStatusEnum;
use App\Models\BailianDirectory;
use App\Models\BailianKnowledge;
use App\Models\BailianKnowledgeItem;
use Exception;
use Log;

/**
 * 目录知识库策略
 */
class DirectoryKnowledgeStrategy extends AbstractKnowledgeItemStrategy
{

    /**
     * Notes: 同步添加笔记到阿里云知识库
     *
     * @Author: 玄尘
     * @Date: 2025/6/11 15:39
     * @param $item
     * @param  \App\Models\BailianKnowledge  $knowledge
     * @param  string  $categoryId
     * @param  array  $options
     * @throws \Exception
     */
    public function addToKnowledgeBase($item, BailianKnowledge $knowledge, string $categoryId, array $options): void
    {
        // 创建知识库项目记录并设置为已同步状态
        $knowledgeItem = $this->createKnowledgeItem($item, $knowledge, $options);
        $knowledgeItem->update([
            'sync_status' => ItemSyncStatusEnum::SYNCED,
            'sync_error'  => null,
            'synced_at'   => now(),
        ]);
    }

    public function addToKnowledge(
        $item,
        BailianKnowledge $knowledge,
        string $categoryId,
        array $options
    ): ?BailianKnowledgeItem {
        return $this->createKnowledgeItem($item, $knowledge, $options);
    }

    /**
     * 同步目录到阿里云知识库
     * 目录本身不需要同步到阿里云，只是在本地管理
     */
    public function syncToAlibaba($item, BailianKnowledge $knowledge, string $categoryId): void
    {
    }

    public function removeFromKnowledge($item): void
    {
        // 目录只需要处理知识库项目记录
        $knowledge = $this->handleKnowledgeItemRemoval($item);
    }

    public function removeFromKnowledgeById(int $itemId, ?int $storageId = null, bool $deleteSelf = false): bool
    {
        try {
            $directory = new BailianDirectory();

            BailianKnowledgeItem::query()
                ->where('itemable_type', $directory->getMorphClass())
                ->where('itemable_id', $itemId)
                ->delete();

            if ($deleteSelf) {
                Log::info('删除目录本身', [
                    'is_self'        => $deleteSelf,
                    'class_basename' => class_basename($directory)
                ]);
                BailianDirectory::query()->where('id', $itemId)->delete();
            }

            return true;
        } catch (Exception $e) {
            $this->logError('从知识库中移除目录失败', $itemId, class_basename(new BailianDirectory()), $e);
            throw $e;
        }
    }

    public function getSupportedModelClass(): string
    {
        return BailianDirectory::class;
    }
}
