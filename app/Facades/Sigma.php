<?php

namespace App\Facades;

use Illuminate\Support\Facades\Facade;

class Sigma extends Facade
{
    protected static array $factor = [10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9];

    protected static array $verifyList = ['1', '0', '9', '8', '7', '6', '5', '4', '3', '2'];

    // 身份证号的加权校验码
    // $factor     = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2];
    // $verifyList = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2'];
    protected static int $modNumber = 10;

    /**
     * Notes   : 加权求和的方式给订单号增加了一位数字，参考身份证的验签方式
     *
     * @Date   : 2023/3/28 17:00
     * <AUTHOR> <Jason.C>
     * @param  string  $str
     * @return string
     */
    public static function orderNo(string $str): string
    {
        $sign = 0;
        for ($i = 0; $i < strlen($str); $i++) {
            $sign += (int) $str[$i] * self::$factor[$i];
        }
        $mod = $sign % self::$modNumber;
        return $str.self::$verifyList[$mod];
    }

    public static function verify(string $str): bool
    {
        $str = strtoupper($str);

        $sign = 0;
        for ($i = 0; $i < strlen($str) - 1; $i++) {
            $sign += (int) $str[$i] * self::$factor[$i];
        }
        $mod = $sign % self::$modNumber;
        return self::$verifyList[$mod] == substr($str, -1);
    }
}