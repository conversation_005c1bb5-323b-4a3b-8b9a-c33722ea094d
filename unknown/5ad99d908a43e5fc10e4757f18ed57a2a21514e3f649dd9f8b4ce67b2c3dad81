<?php

namespace Modules\Payment\Models;

use App\Models\Model;
use App\Packages\XinHuaERP\XinHuaERP;
use App\Traits\AutoCreateOrderNo;
use Modules\Payment\Enums\AccountType;
use Modules\Payment\Enums\BalancePayStatus;
use Modules\Payment\Exceptions\AlreadyPaid;
use Modules\Payment\Exceptions\InsufficientFunds;
use Modules\Payment\Jobs\BalanceJob;
use Modules\Payment\Models\Traits\BelongsToAccount;
use Modules\Payment\Models\Traits\BelongsToPayment;

/**
 * 余额支付
 */
class Balance extends Model
{
    use AutoCreateOrderNo,
        BelongsToAccount,
        BelongsToPayment;

    protected $table = 'payment_balances';

    protected $casts = [
        'paid_at' => 'datetime',
        'status'  => BalancePayStatus::class,
        'channel' => AccountType::class,
    ];

    public function getRouteKeyName(): string
    {
        return 'no';
    }

    /**
     * Notes   : 订单支付，如果要保证余额准确性，要把支付过程放到队列里面处理
     *
     * @Date   : 2023/3/27 17:40
     * <AUTHOR> <Jason.C>
     * @throws \Exception
     */
    public function pay(): void
    {
        if ($this->status != BalancePayStatus::UNPAY) {
            throw new AlreadyPaid('订单非可支付状态');
        }
        // 判断余额是否充足
        if ($this->channel->value != AccountType::BALANCE->value) {
            if ($this->account->{$this->channel->value} < $this->amount) {
                throw new InsufficientFunds($this->channel->toString());
            }
            BalanceJob::dispatch($this);
        } else {
            $this->paid_at = now();
            $this->status  = BalancePayStatus::PAID;
            $this->save();
            $this->paid();
        }
    }

    /**
     * Notes   : 支付完成
     *
     * @Date   : 2023/5/19 10:49
     * <AUTHOR> <Jason.C>
     * @throws \Doctrine\DBAL\Exception
     * @throws \Throwable
     */
    public function paid(): void
    {
        $this->payment->paid(now());
    }
}
