<?php

namespace Modules\Payment\Models;

use App\Models\Model;
use App\Traits\HasCovers;
use App\Traits\HasEasyStatus;
use GeneaLabs\LaravelModelCaching\Traits\Cachable;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Spatie\EloquentSortable\Sortable;
use Spatie\EloquentSortable\SortableTrait;

class Bank extends Model implements Sortable
{
    use Cachable,
        HasCovers,
        HasEasyStatus,
        SortableTrait,
        SoftDeletes;

    protected $table = 'payment_banks';

    protected array $sortable = [
        'order_column_name'  => 'order',
        'sort_when_creating' => true,
    ];

    public function cards(): HasMany
    {
        return $this->hasMany(BankCard::class);
    }
}
