<?php

namespace Modules\Payment\Models\Traits;

use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Modules\Payment\Models\Account;

trait BelongsToAccount
{
    public function account(): BelongsTo
    {
        return $this->belongsTo(Account::class);
    }

    public function setAccountAttribute(Account $account): void
    {
        $this->attributes['account_id'] = $account->getKey();
    }
}