<?php

namespace Modules\Payment\Contracts;

interface Refundable
{
    /**
     * Notes   : 退款完成的通知
     *
     * @Date   : 2023/10/17 10:55
     * <AUTHOR> <Jason.C>
     * @param  bool  $result  退款结果，true 成功 false 失败
     * @param  string|null  $desc  退款结果描述
     * @param  array|null  $data  退款结果的一些扩展数据
     */
    public function refunded(bool $result, ?string $desc = null, ?array $data = null): void;
}