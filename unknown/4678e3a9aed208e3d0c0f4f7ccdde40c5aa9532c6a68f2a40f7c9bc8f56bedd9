<?php

namespace Modules\Storage\Adapters;

use DateTimeInterface;
use Illuminate\Support\Carbon;
use League\Flysystem\Config;
use League\Flysystem\FileAttributes;
use League\Flysystem\UnableToCopyFile;
use League\Flysystem\UnableToDeleteFile;
use League\Flysystem\UnableToListContents;
use League\Flysystem\UnableToMoveFile;
use League\Flysystem\UnableToReadFile;
use League\Flysystem\UnableToRetrieveMetadata;
use League\Flysystem\UnableToSetVisibility;
use Qiniu\Auth;
use Qiniu\Storage\BucketManager;
use Qiniu\Storage\UploadManager;

class QiNiuAdapter extends CoreAdapter
{
    private Auth $auth;

    protected function initClient(): void
    {
        $this->auth   = new Auth($this->config['QINIU_ACCESS_KEY'], $this->config['QINIU_SECRET_KEY']);
        $this->qiniuClient = new BucketManager($this->auth);

        $this->bucket    = $this->config['QINIU_BUCKET'];
        $this->endPoint  = $this->config['QINIU_ENDPOINT'];
        $this->isCname   = $this->config['QINIU_IS_CNAME'];
        $this->useSSL    = $this->config['QINIU_USE_SSL'];
        $this->cdnHost   = $this->config['QINIU_CDN_HOST'];
        $this->signedUrl = $this->config['QINIU_SIGNED_URL'];
    }

    public function getUrl(string $path): string
    {
        $path = $this->pathPrefixer->prefixPath($path);

        if ($this->signedUrl) {
            return $this->getTemporaryUrl($path, Carbon::now()->addHour());
        } else {
            return sprintf(
                '%s%s/%s',
                $this->useSSL ? 'https://' : 'http://',
                $this->cdnHost,
                $path
            );
        }
    }

    public function getTemporaryUrl(string $path, DateTimeInterface $expiration, array $options = []): bool|string
    {
        $path = $this->pathPrefixer->prefixPath($path);

        return sprintf(
            '%s%s/%s',
            $this->useSSL ? 'https://' : 'http://',
            $this->cdnHost,
            $this->auth->privateDownloadUrl($path)
        );
    }

    protected function getMetadata(string $path): FileAttributes
    {
        $path   = $this->pathPrefixer->prefixPath($path);
        $result = $this->qiniuClient->stat($this->bucket, $path);
        $stats  = $result[0];

        return new FileAttributes(
            path: $path,
            fileSize: $stats['fsize'] ?? null,
            visibility: $stats['status'] ?? 'public',
            lastModified: isset($stats['putTime']) ? floor($stats['putTime'] / 10000000) : null,
            mimeType: $stats['mimeType'] ?? null,
            extraMetadata: [
                'hash' => $stats['hash'],
                'md5'  => $stats['md5'],
            ]
        );
    }

    public function fileExists(string $path): bool
    {
        [, $error] = $this->qiniuClient->stat($this->bucket, $this->pathPrefixer->prefixPath($path));

        return is_null($error);
    }

    public function directoryExists(string $path): bool
    {
        return $this->fileExists($path);
    }

    public function write(string $path, string $contents, Config $config): void
    {
        $token = $this->auth->uploadToken($this->bucket);

        $uploadManager = new UploadManager();
        $uploadManager->put($token, $path, $contents);
    }

    public function writeStream(string $path, $contents, Config $config): void
    {
        $data = '';

        while (! feof($contents)) {
            $data .= fread($contents, 1024);
        }

        $this->write($path, $data, $config);
    }

    public function read(string $path): string
    {
        return file_get_contents($this->getTemporaryUrl($path, Carbon::now()->addHour()));
    }

    public function readStream(string $path)
    {
        throw   UnableToReadFile::fromLocation($path);
    }

    public function delete(string $path): void
    {
        $path = $this->pathPrefixer->prefixPath($path);

        [, $error] = $this->qiniuClient->delete($this->bucket, $path);

        if ($error) {
            throw UnableToDeleteFile::atLocation($path, $error->message());
        }
    }

    public function deleteDirectory(string $path): void
    {
        $this->delete($path);
    }

    public function createDirectory(string $path, Config $config): void
    {
        $path  = $this->pathPrefixer->prefixDirectoryPath($path);
        $token = $this->auth->uploadToken($this->bucket);

        $uploadManager = new UploadManager();
        $uploadManager->put($token, $path, '');
    }

    public function setVisibility(string $path, string $visibility): void
    {
        $status = $visibility == 'public' ? 0 : 1;

        [, $error] = $this->qiniuClient->changeStatus($this->bucket, $path, $status);

        if (! is_null($error)) {
            throw UnableToSetVisibility::atLocation($path);
        }
    }

    public function visibility(string $path): FileAttributes
    {
        $meta = $this->getMetadata($path);

        if ($meta->mimeType() === null) {
            throw UnableToRetrieveMetadata::visibility($path);
        }
        return $meta;
    }

    public function mimeType(string $path): FileAttributes
    {
        $meta = $this->getMetadata($path);

        if ($meta->mimeType() === null) {
            throw UnableToRetrieveMetadata::mimeType($path);
        }

        return $meta;
    }

    public function lastModified(string $path): FileAttributes
    {
        $meta = $this->getMetadata($path);

        if ($meta->lastModified() === null) {
            throw UnableToRetrieveMetadata::lastModified($path);
        }

        return $meta;
    }

    public function fileSize(string $path): FileAttributes
    {
        $meta = $this->getMetadata($path);

        if ($meta->fileSize() === null) {
            throw UnableToRetrieveMetadata::fileSize($path);
        }

        return $meta;
    }

    public function listContents(string $path, bool $deep): iterable
    {
        $path = $this->pathPrefixer->prefixDirectoryPath($path);

        [$list, $error] = $this->qiniuClient->listFiles($this->bucket, $path);

        if (! is_null($error)) {
            throw UnableToListContents::atLocation($path, $deep, $error);
        }

        foreach ($list['items'] as $item) {
            yield new FileAttributes(
                $item['key'],
                $item['fsize'],
                $item['status'] ?? 'public',
                $item['putTime'] / 1000
            );
        }

        return [];
    }

    public function move(string $source, string $destination, Config $config): void
    {
        [, $error] = $this->qiniuClient->move(
            $this->bucket,
            $source,
            $this->bucket,
            $destination
        );

        if (! is_null($error)) {
            throw UnableToMoveFile::fromLocationTo($source, $destination);
        }
    }

    public function copy(string $source, string $destination, Config $config): void
    {
        [, $error] = $this->qiniuClient->copy(
            $this->bucket,
            $source,
            $this->bucket,
            $destination
        );

        if (! is_null($error)) {
            throw UnableToCopyFile::fromLocationTo($source, $destination);
        }
    }
}