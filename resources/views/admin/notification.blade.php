<ul class="nav navbar-nav mr-1">
    <li class="dropdown dropdown-notification nav-item">
        <a class="nav-link nav-link-label"
           href="javascript:void(0)"
           data-toggle="dropdown"
           aria-expanded="true"
           wire:poll.30s="refreshAdminNotificationCount"
        >
            <i class="ficon feather icon-bell"></i>
            <span class="badge badge-pill badge-primary badge-up">{{ $count }}</span>
        </a>
        <ul class="dropdown-menu dropdown-menu-media dropdown-menu-right">
            <li class="dropdown-menu-header">
                <div class="dropdown-header m-0 p-2">
                    <a href="{{ route('admin.messages.index') }}">
                        <h3 class="white">{{ $count }} New</h3><span class="grey darken-2">Notifications</span>
                    </a>
                </div>
            </li>
            <li class="scrollable-container media-list overflow-auto">
                @foreach($notifications as $notification)
                    <a href="{{ route('admin.messages.show', $notification->id) }}"
                       wire:key="{{ $notification->id }}">
                        <div class="media d-flex align-items-start" wire:click="readOne('{{$notification->id }}')">
                            <div class="media-body">
                                <h6 class="primary media-heading">{{ $notification->type::getTitle() }}</h6>
                                <small class="notification-text">{{ $notification->data['content'] }}</small>
                            </div>
                            <small>
                                <time class="media-meta">{{ $notification->created_at->diffForHumans(now()) }}</time>
                            </small>
                        </div>
                    </a>
                @endforeach
            </li>
            <li class="dropdown-menu-footer">
                <a class="dropdown-item p-1 text-center"
                   wire:click="$dispatch('mark-all-read')"
                   href="javascript:void(0)"
                >
                    全部标记已读
                </a>
            </li>
        </ul>
    </li>
</ul>

<script>
    document.addEventListener('livewire:init', () => {
        console.log('init')
        Livewire.on('mark-all-read', (event) => {
            Dcat.confirm('标记已读', '确定要将所有未读消息标记为已读么?', () => {
                Livewire.dispatch('mark-all')
            });
        });
        Livewire.on('mark-readed', (event) => {
            Dcat.swal.success(event.message).then(a => {
                Dcat.reload();
            });
        });
    });
</script>

