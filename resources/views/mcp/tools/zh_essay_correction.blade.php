### 评分

- **总分**: {{$score}}
- **感情真挚**: {{$scoreArray['sentimentSincerity']}}【{{$scoreArray['sentimentSincerityDoc']}}】
- **语言流畅**: {{$scoreArray['essayFluence']}}【{{$scoreArray['essayFluenceDoc']}}】
- **结构严谨**: {{$scoreArray['structureStrict']}}【{{$scoreArray['structureStrictDoc']}}】
- **好词好句**: {{$scoreArray['goodSent']}}【{{$scoreArray['goodSentDoc']}}】

### 整体评价

- **偏离主题**: {{$commentCollection['titleConflict']}}
- **整体评级**: {{$commentCollection['rating']}}
- **整体评价**: {{$commentCollection['comment']}}
- **作文类型**: {{$commentCollection['style']}}

### 各方面评价

- **情感**: {{$commentCollection['aspectComment']['emotion']}}
- **表达**: {{$commentCollection['aspectComment']['langExpression']}}
- **结构**: {{$commentCollection['aspectComment']['structLogic']}}
- **内容**: {{$commentCollection['aspectComment']['contentInfo']}}

### 好句评价

@foreach($detailedEvaluation['sentenceEvaluation']??[] as $sentenceEvaluation)
- {{$sentenceEvaluation['comment']}} [{{$sentenceEvaluation['start']}}-{{$sentenceEvaluation['end']}}]
@endforeach

### 成语评价

@foreach($detailedEvaluation['phraseEvaluation']??[] as $sentenceEvaluation)
- {{$sentenceEvaluation['explanation']}}[{{$sentenceEvaluation['start']}}-{{$sentenceEvaluation['end']}}]
@endforeach

### 纠正信息

@foreach($correctedContent as $item)
- 段落[{{$item['paraId']}}],句子[{{$item['sentId']}}]
原文: {{$item['orgSent']}}
建议: {{$item['corSent']}}

    - @foreach($item['errorInfos'] as $errorInfo) "{{$errorInfo['orgChunk']}}" 改为 "{{$errorInfo['corChunk']}}" 【@php echo match ($errorInfo['errorType']){
            'punct' => '拼写错误',
            'space' => '空格错误',
            'spell' => '拼写错误',
            'grammar' => '语法错误',
        }@endphp】    @endforeach

@endforeach

## 要求：不要修改以上内容