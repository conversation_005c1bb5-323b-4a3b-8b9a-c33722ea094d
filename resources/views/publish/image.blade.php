<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Watestar AI</title>
    <link rel="stylesheet" href="{{asset('css/video.css')}}?ver={{time()}}">
</head>
<body>
<div id="mark">
    <div class="mark"></div>
    <div class="mark_img">
        <img src="/img/5736cf4941d3f_610.png">
    </div>
    <div class="mark_text">
        请使用浏览器打开
    </div>
</div>

<div class="container">
    <img class="image" src="{{$publish->assetable->coverUrlAttr}}"/>
    <div class="video-footer">
        <div class="video-user">
            <h4>{{$publish->user->info->nickname}}</h4>
            <p>{{$publish->description}}</p>
            <p>{{$publish->created_at->toDateTimeString()}}</p>
        </div>
        <div class="video-action">
            <img class="author-avatar" src="{{$publish->user->info->avatar_url}}" alt="作者头像">
            <div class="action-item">
                <div class="action-icon">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M7 10v12"></path>
                        <path d="M15 5.88 14 10h5.83a2 2 0 0 1 1.92 2.56l-2.33 8A2 2 0 0 1 17.5 22H4a2 2 0 0 1-2-2v-8a2 2 0 0 1 2-2h2.76a2 2 0 0 0 1.79-1.11L12 2h0a3.13 3.13 0 0 1 3 3.88Z"></path>
                    </svg>
                </div>
                <span class="action-count">{{$publish->likeable_count}}</span>
            </div>
            <div class="action-item">
                <div class="action-icon">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
                    </svg>
                </div>
                <span class="action-count">{{$publish->commentable_count}}</span>
            </div>
            <div class="action-item">
                <div class="action-icon">
                    <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M19 21l-7-5-7 5V5a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2z"></path>
                    </svg>
                </div>
                <span class="action-count">{{$publish->favoriteable_count}}</span>
            </div>
        </div>
    </div>
</div>

<div class="footer">
    <div class="footer-title">
        <img class="footer-logo" src="/img/sharelogo.png"/>
        瓦特AI
    </div>
    <wx-open-launch-app id="launch-btn" appid="wxf3b29d7e2e5ab03a" extinfo="app/asset/{{$publish->id}}">
        <script type="text/wxtag-template">
            <style>
                .btn {
                    background: linear-gradient(40deg, #4d51e8, #1d21b7);
                    border: none;
                    color: white;
                    line-height: 30px;
                    padding: 0 15px;
                    border-radius: 15px;
                    font-size: 10px;
                }
            </style>
            <button class="btn">打开看看</button>
        </script>
    </wx-open-launch-app>
    <button class="footer-button" id="openApp" onclick="location.href='wateai://app/asset/{{$publish->id}}'">打开APP
    </button>
</div>
<script src="https://res.wx.qq.com/open/js/jweixin-1.6.0.js"></script>
<script type="text/javascript">
    function onPlay() {
        let video = document.getElementsByClassName('video')[0]
        let playIcon = document.getElementsByClassName('video-icon')[0]
        if (video.paused) {
            video.play()
            playIcon.style.display = 'none'
        } else {
            video.pause()
            playIcon.style.display = 'block'
        }
    }

    wx.config({!! json_encode($config, JSON_UNESCAPED_UNICODE) !!});
    wx.ready(function () {
        var btn = document.getElementById('launch-btn');
        btn.addEventListener('ready', function (e) {
            document.getElementById('openApp').style.display = 'none';
        });
        btn.addEventListener('launch', function (e) {
        });
        btn.addEventListener('error', function (e) {
            document.getElementById('mark').style.display = 'block';
        });
    });

</script>
</body>
</html>


<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>抖音风格视频播放器</title>
    <link rel="stylesheet" href="{{asset('css/video.css')}}?ver=1">
</head>
<body>
<div class="swiper-container">
    <div class="video-slide" style="transform: translateY(0);">
        <div class="container">
            <img style="width:100vw;" src="{{$publish->assetable->coverUrlAttr}}"></img>
        </div>
        <div class="overlay">
            <div class="content-container">
                <div class="video-info">
                    <h2 class="video-title">
                        <img src="{{$publish->user->info->avatar_url}}" alt="作者头像" class="author-avatar">
                        &nbsp;@&nbsp;{{$publish->user->info->nickname}}</h2>
                    <p class="video-description">{{$publish->description}}</p>
                    <button class="expand-btn">展开</button>
                </div>
                <div class="action-bar">
                    <div class="action-item">
                        <div class="action-icon">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                 stroke-width="2">
                                <path d="M7 10v12"></path>
                                <path d="M15 5.88 14 10h5.83a2 2 0 0 1 1.92 2.56l-2.33 8A2 2 0 0 1 17.5 22H4a2 2 0 0 1-2-2v-8a2 2 0 0 1 2-2h2.76a2 2 0 0 0 1.79-1.11L12 2h0a3.13 3.13 0 0 1 3 3.88Z"></path>
                            </svg>
                        </div>
                        <span class="action-count">{{$publish->likeable_count}}</span>
                    </div>
                    <div class="action-item">
                        <div class="action-icon">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                 stroke-width="2">
                                <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"></path>
                            </svg>
                        </div>
                        <span class="action-count">{{$publish->commentable_count}}</span>
                    </div>
                    <div class="action-item">
                        <div class="action-icon">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                 stroke-width="2">
                                <path d="M19 21l-7-5-7 5V5a2 2 0 0 1 2-2h10a2 2 0 0 1 2 2z"></path>
                            </svg>
                        </div>
                        <span class="action-count">{{$publish->favoriteable_count}}</span>
                    </div>

                    <div class="action-item">
                        <div class="action-icon">
                            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor"
                                 stroke-width="2">
                                <circle cx="18" cy="5" r="3"></circle>
                                <circle cx="6" cy="12" r="3"></circle>
                                <circle cx="18" cy="19" r="3"></circle>
                                <line x1="8.59" y1="13.51" x2="15.42" y2="17.49"></line>
                                <line x1="15.41" y1="6.51" x2="8.59" y2="10.49"></line>
                            </svg>
                        </div>
                        <span class="action-count" id="shareButton">分享</span>
                        <input type="text" id="urlField" value="{{route('app.Publish.Share', $publish)}}" readonly
                               style="position:absolute;left:-9999px;">
                    </div>
                </div>
            </div>
            <div class="progress-bar">
                <div class="progress"></div>
            </div>
        </div>
    </div>
</div>

<script>
    document.addEventListener('DOMContentLoaded', function () {
        const expandBtns = document.querySelectorAll('.expand-btn');
        expandBtns.forEach(btn => {
            btn.addEventListener('click', function () {
                const description = this.previousElementSibling;
                description.classList.toggle('expanded');
                this.textContent = description.classList.contains('expanded') ? '收起' : '展开';
            });
        });
    })

    document.getElementById('shareButton').addEventListener('click', function () {
        var copyText = document.getElementById("urlField");
        copyText.select();
        copyText.setSelectionRange(0, 99999); // For mobile devices
        document.execCommand("copy");
        alert("链接已复制到剪贴板");
    });


</script>
</body>
</html>
