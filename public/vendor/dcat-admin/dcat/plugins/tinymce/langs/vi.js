tinymce.addI18n('vi',{
"Redo": "L\u00e0m l\u1ea1i",
"Undo": "H\u1ee7y thao t\u00e1c",
"Cut": "C\u1eaft",
"Copy": "Sao ch\u00e9p",
"Paste": "D\u00e1n",
"Select all": "Ch\u1ecdn t\u1ea5t c\u1ea3",
"New document": "T\u1ea1o t\u00e0i li\u1ec7u m\u1edbi",
"Ok": "\u0110\u1ed3ng \u00dd",
"Cancel": "Hu\u1ef7 B\u1ecf",
"Visual aids": "M\u1edf khung so\u1ea1n th\u1ea3o",
"Bold": "In \u0111\u1eadm",
"Italic": "In nghi\u00eang",
"Underline": "G\u1ea1ch d\u01b0\u1edbi",
"Strikethrough": "G\u1ea1ch ngang",
"Superscript": "K\u00fd t\u1ef1 m\u0169",
"Subscript": "K\u00fd t\u1ef1 th\u1ea5p",
"Clear formatting": "L\u01b0\u1ee3c b\u1ecf ph\u1ea7n hi\u1ec7u \u1ee9ng",
"Align left": "Canh tr\u00e1i",
"Align center": "Canh gi\u1eefa",
"Align right": "Canh ph\u1ea3i",
"Justify": "Canh \u0111\u1ec1u hai b\u00ean",
"Bullet list": "Danh s\u00e1ch d\u1ea1ng bi\u1ec3u t\u01b0\u1ee3ng",
"Numbered list": "Danh s\u00e1ch d\u1ea1ng s\u1ed1",
"Decrease indent": "Th\u1ee5t l\u00f9i d\u00f2ng",
"Increase indent": "T\u0103ng kho\u1ea3ng c\u00e1ch d\u00f2ng",
"Close": "\u0110\u00f3ng L\u1ea1i",
"Formats": "\u0110\u1ecbnh d\u1ea1ng",
"Your browser doesn't support direct access to the clipboard. Please use the Ctrl+X\/C\/V keyboard shortcuts instead.": "Tr\u00ecnh duy\u1ec7t c\u1ee7a b\u1ea1n kh\u00f4ng h\u1ed7 tr\u1ee3 truy c\u1eadp truy c\u1eadp b\u1ed9 nh\u1edb \u1ea3o, vui l\u00f2ng s\u1eed d\u1ee5ng c\u00e1c t\u1ed5 h\u1ee3p ph\u00edm Ctrl + X, C, V.",
"Headers": "\u0110\u1ea7u trang",
"Header 1": "Ti\u00eau \u0111\u1ec1 1",
"Header 2": "Ti\u00eau \u0111\u1ec1 2",
"Header 3": "Ti\u00eau \u0111\u1ec1 3",
"Header 4": "Ti\u00eau \u0111\u1ec1 4",
"Header 5": "Ti\u00eau \u0111\u1ec1 5",
"Header 6": "Ti\u00eau \u0111\u1ec1 6",
"Headings": "Ph\u1ea7n \u0111\u1ea7u",
"Heading 1": "H1",
"Heading 2": "H2",
"Heading 3": "H3",
"Heading 4": "H4",
"Heading 5": "H5",
"Heading 6": "G6",
"Preformatted": "\u0110\u1ecbnh d\u1ea1ng s\u1eb5n",
"Div": "Khung",
"Pre": "\u0110\u1ecbnh d\u1ea1ng",
"Code": "M\u00e3",
"Paragraph": "\u0110o\u1ea1n v\u0103n",
"Blockquote": "\u0110o\u1ea1n Tr\u00edch D\u1eabn",
"Inline": "C\u00f9ng d\u00f2ng",
"Blocks": "Bao",
"Paste is now in plain text mode. Contents will now be pasted as plain text until you toggle this option off.": "Ch\u1ee9c n\u0103ng D\u00e1n \u0111ang trong tr\u1ea1ng th\u00e1i v\u0103n b\u1ea3n \u0111\u01a1n gi\u1ea3n. N\u1ed9i dung s\u1ebd \u0111\u01b0\u1ee3c d\u00e1n d\u01b0\u1edbi d\u1ea1ng v\u0103n b\u1ea3n thu\u1ea7n, kh\u00f4ng c\u00f3 \u0111\u1ecbnh d\u1ea1ng.",
"Font Family": "Ki\u1ec3u ch\u1eef",
"Font Sizes": "C\u1ee1 ch\u1eef",
"Class": "L\u1edbp",
"Browse for an image": "Ch\u00e8n m\u1ed9t h\u00ecnh \u1ea3nh",
"OR": "HO\u1eb6C",
"Drop an image here": "Th\u1ea3 h\u00ecnh \u1ea3nh v\u00e0o \u0111\u00e2y",
"Upload": "T\u1ea3i l\u00ean",
"Block": "Kh\u1ed1i",
"Align": "Canh l\u1ec1",
"Default": "M\u1eb7c \u0111\u1ecbnh",
"Circle": "H\u00ecnh tr\u00f2n",
"Disc": "H\u00ecnh tr\u00f2n  d\u1ea1ng m\u1ecfng",
"Square": "\u00d4 vu\u00f4ng",
"Lower Alpha": "K\u00fd t\u1ef1 th\u01b0\u1eddng",
"Lower Greek": "S\u1ed1 hy l\u1ea1p th\u01b0\u1eddng",
"Lower Roman": "S\u1ed1 la m\u00e3 th\u01b0\u1eddng",
"Upper Alpha": "K\u00fd t\u1ef1 hoa",
"Upper Roman": "S\u1ed1 la m\u00e3 hoa",
"Anchor": "Neo",
"Name": "T\u00ean",
"Id": "Id",
"Id should start with a letter, followed only by letters, numbers, dashes, dots, colons or underscores.": "Id should start with a letter, followed only by letters, numbers, dashes, dots, colons or underscores.",
"You have unsaved changes are you sure you want to navigate away?": "B\u1ea1n ch\u01b0a l\u01b0u thay \u0111\u1ed5i b\u1ea1n c\u00f3 ch\u1eafc b\u1ea1n mu\u1ed1n di chuy\u1ec3n \u0111i?",
"Restore last draft": "Kh\u00f4i ph\u1ee5c b\u1ea3n g\u1ea7n nh\u1ea5t",
"Special character": "K\u00fd t\u1ef1 \u0111\u1eb7c bi\u1ec7t",
"Source code": "M\u00e3 ngu\u1ed3n",
"Insert\/Edit code sample": "Ch\u00e8n\/S\u1eeda m\u00e3 m\u1eabu",
"Language": "Ng\u00f4n ng\u1eef",
"Code sample": "M\u00e3 m\u1eabu",
"Color": "M\u00e0u s\u1eafc",
"R": "M\u00e0u \u0111\u1ecf",
"G": "M\u00e0u xanh l\u00e1 c\u00e2y",
"B": "M\u00e0u xanh da tr\u1eddi",
"Left to right": "Tr\u00e1i sang ph\u1ea3i",
"Right to left": "Ph\u1ea3i sang tr\u00e1i",
"Emoticons": "Bi\u1ec3u t\u01b0\u1ee3ng c\u1ea3m x\u00fac",
"Document properties": "Thu\u1ed9c t\u00ednh t\u00e0i li\u1ec7u",
"Title": "Ti\u00eau \u0111\u1ec1",
"Keywords": "T\u1eeb kh\u00f3a",
"Description": "M\u00f4 t\u1ea3",
"Robots": "Robots",
"Author": "T\u00e1c gi\u1ea3",
"Encoding": "M\u00e3 h\u00f3a",
"Fullscreen": "To\u00e0n m\u00e0n h\u00ecnh",
"Action": "H\u00e0nh \u0111\u1ed9ng",
"Shortcut": "L\u1ed1i t\u1eaft",
"Help": "Tr\u1ee3 gi\u00fap",
"Address": "\u0110\u1ecba ch\u1ec9",
"Focus to menubar": "Focus to menubar",
"Focus to toolbar": "Focus to toolbar",
"Focus to element path": "Focus to element path",
"Focus to contextual toolbar": "Focus to contextual toolbar",
"Insert link (if link plugin activated)": "Ch\u00e8n \u0111\u01b0\u1eddng d\u1eabn",
"Save (if save plugin activated)": "L\u01b0u",
"Find (if searchreplace plugin activated)": "T\u00ecm ki\u1ebfm",
"Plugins installed ({0}):": "Plugins installed ({0}):",
"Premium plugins:": "Premium plugins:",
"Learn more...": "Learn more...",
"You are using {0}": "You are using {0}",
"Plugins": "Plugins",
"Handy Shortcuts": "Handy Shortcuts",
"Horizontal line": "K\u1ebb ngang",
"Insert\/edit image": "Ch\u00e8n\/s\u1eeda \u1ea3nh",
"Image description": "M\u00f4 t\u1ea3 \u1ea3nh",
"Source": "Ngu\u1ed3n",
"Dimensions": "K\u00edch th\u01b0\u1edbc",
"Constrain proportions": "T\u1ef7 l\u1ec7 h\u1ea1n ch\u1ebf",
"General": "Chung",
"Advanced": "N\u00e2ng cao",
"Style": "Ki\u1ec3u",
"Vertical space": "N\u1eb1m d\u1ecdc",
"Horizontal space": "N\u1eb1m ngang",
"Border": "\u0110\u01b0\u1eddng vi\u1ec1n",
"Insert image": "Ch\u00e8n \u1ea3nh",
"Image": "Image",
"Image list": "Image list",
"Rotate counterclockwise": "Xoay tr\u00e1i",
"Rotate clockwise": "Xoay ph\u1ea3i",
"Flip vertically": "L\u1eadt d\u1ecdc",
"Flip horizontally": "L\u1eadt ngang",
"Edit image": "Ch\u1ec9nh s\u1eeda \u1ea3nh",
"Image options": "T\u00f9y ch\u1ecdn \u1ea3nh",
"Zoom in": "Thu nh\u1ecf",
"Zoom out": "Ph\u00f3ng to",
"Crop": "C\u1eaft \u1ea3nh",
"Resize": "Thay \u0111\u1ed5i k\u00edch th\u01b0\u1edbc",
"Orientation": "\u0110\u1ecbnh h\u01b0\u1edbng",
"Brightness": "\u0110\u1ed9 s\u00e1ng",
"Sharpen": "L\u00e0m s\u1eafc n\u00e9t",
"Contrast": "\u0110\u1ed9 t\u01b0\u01a1ng ph\u1ea3n",
"Color levels": "M\u1ee9c \u0111\u1ed9 m\u00e0u",
"Gamma": "M\u00e0u Gamma",
"Invert": "\u0110\u1ea3o ng\u01b0\u1ee3c",
"Apply": "\u00c1p d\u1ee5ng",
"Back": "Quay l\u1ea1i",
"Insert date\/time": "Ch\u00e8n ng\u00e0y\/th\u00e1ng",
"Date\/time": "Date\/time",
"Insert link": "Ch\u00e8n li\u00ean k\u1ebft",
"Insert\/edit link": "Ch\u00e8n\/s\u1eeda li\u00ean k\u1ebft",
"Text to display": "N\u1ed9i dung hi\u1ec3n th\u1ecb",
"Url": "Url",
"Target": "\u0110\u00edch",
"None": "Kh\u00f4ng",
"New window": "C\u1eeda s\u1ed5 m\u1edbi",
"Remove link": "B\u1ecf li\u00ean k\u1ebft",
"Anchors": "Neo",
"Link": "Link",
"Paste or type a link": "Paste or type a link",
"The URL you entered seems to be an email address. Do you want to add the required mailto: prefix?": "\u0110\u1ecba ch\u1ec9 URL b\u1ea1n v\u1eeba nh\u1eadp gi\u1ed1ng nh\u01b0 m\u1ed9t \u0111\u1ecba ch\u1ec9 email. B\u1ea1n c\u00f3 mu\u1ed1n th\u00eam ti\u1ec1n t\u1ed1 mailto: kh\u00f4ng?",
"The URL you entered seems to be an external link. Do you want to add the required http:\/\/ prefix?": "\u0110\u1ecba ch\u1ec9 URL b\u1ea1n v\u1eeba nh\u1eadp gi\u1ed1ng nh\u01b0 m\u1ed9t li\u00ean k\u1ebft. B\u1ea1n c\u00f3 mu\u1ed1n th\u00eam ti\u1ec1n t\u1ed1 http:\/\/ kh\u00f4ng?",
"Link list": "Link list",
"Insert video": "Ch\u00e8n video",
"Insert\/edit video": "Ch\u00e8n\/s\u1eeda video",
"Insert\/edit media": "Insert\/edit media",
"Alternative source": "Ngu\u1ed3n thay th\u1ebf",
"Poster": "Ng\u01b0\u1eddi g\u1eedi",
"Paste your embed code below:": "D\u00e1n m\u00e3 nh\u00fang c\u1ee7a b\u1ea1n d\u01b0\u1edbi \u0111\u00e2y:",
"Embed": "Nh\u00fang",
"Media": "Media",
"Nonbreaking space": "Kh\u00f4ng xu\u1ed1ng h\u00e0ng",
"Page break": "Ng\u1eaft trang",
"Paste as text": "D\u00e1n \u0111o\u1ea1n v\u0103n b\u1ea3n",
"Preview": "Xem th\u1eed",
"Print": "In",
"Save": "L\u01b0u",
"Find": "T\u00ecm ki\u1ebfm",
"Replace with": "Thay th\u1ebf b\u1edfi",
"Replace": "Thay th\u1ebf",
"Replace all": "Thay t\u1ea5t c\u1ea3",
"Prev": "Tr\u01b0\u1edbc",
"Next": "K\u1ebf ti\u1ebfp",
"Find and replace": "T\u00ecm v\u00e0 thay th\u1ebf",
"Could not find the specified string.": "Kh\u00f4ng t\u00ecm th\u1ea5y chu\u1ed7i qui \u0111\u1ecbnh",
"Match case": "Tr\u01b0\u1eddng h\u1ee3p xem",
"Whole words": "To\u00e0n b\u1ed9 t\u1eeb",
"Spellcheck": "Ki\u1ec3m tra ch\u00ednh t\u1ea3",
"Ignore": "B\u1ecf qua",
"Ignore all": "B\u1ecf qua t\u1ea5t",
"Finish": "Ho\u00e0n t\u1ea5t",
"Add to Dictionary": "Th\u00eam v\u00e0o t\u1eeb \u0111i\u1ec3n",
"Insert table": "Th\u00eam b\u1ea3ng",
"Table properties": "Thu\u1ed9c t\u00ednh b\u1ea3ng",
"Delete table": "Xo\u00e1 b\u1ea3ng",
"Cell": "\u00d4",
"Row": "D\u00f2ng",
"Column": "C\u1ed9t",
"Cell properties": "Thu\u1ed9c t\u00ednh \u00f4",
"Merge cells": "Tr\u1ed9n \u00f4",
"Split cell": "Chia c\u1eaft \u00f4",
"Insert row before": "Th\u00eam d\u00f2ng ph\u00eda tr\u00ean",
"Insert row after": "Th\u00eam d\u00f2ng ph\u00eda d\u01b0\u1edbi",
"Delete row": "Xo\u00e1 d\u00f2ng",
"Row properties": "Thu\u1ed9c t\u00ednh d\u00f2ng",
"Cut row": "C\u1eaft d\u00f2ng",
"Copy row": "Sao ch\u00e9p d\u00f2ng",
"Paste row before": "D\u00e1n v\u00e0o ph\u00eda tr\u01b0\u1edbc, tr\u00ean",
"Paste row after": "D\u00e1n v\u00e0o ph\u00eda sau, d\u01b0\u1edbi",
"Insert column before": "Th\u00eam c\u1ed9t b\u00ean tr\u00e1i",
"Insert column after": "Th\u00eam c\u1ed9t b\u00ean ph\u1ea3i",
"Delete column": "Xo\u00e1 c\u1ed9t",
"Cols": "C\u1ed9t",
"Rows": "D\u00f2ng",
"Width": "\u0110\u1ed9 R\u1ed9ng",
"Height": "\u0110\u1ed9 Cao",
"Cell spacing": "Kho\u1ea3ng c\u00e1ch \u00f4",
"Cell padding": "Kho\u1ea3ng c\u00e1ch trong \u00f4",
"Caption": "Ti\u00eau \u0111\u1ec1",
"Left": "Tr\u00e1i",
"Center": "Gi\u1eefa",
"Right": "Ph\u1ea3i",
"Cell type": "Lo\u1ea1i \u00f4",
"Scope": "Quy\u1ec1n",
"Alignment": "Canh ch\u1ec9nh",
"H Align": "L\u1ec1 ngang",
"V Align": "L\u1ec1 d\u1ecdc",
"Top": "Tr\u00ean",
"Middle": "Kho\u1ea3ng gi\u1eefa",
"Bottom": "D\u01b0\u1edbi",
"Header cell": "Ti\u00eau \u0111\u1ec1 \u00f4",
"Row group": "Gom nh\u00f3m d\u00f2ng",
"Column group": "Gom nh\u00f3m c\u1ed9t",
"Row type": "Th\u1ec3 lo\u1ea1i d\u00f2ng",
"Header": "Ti\u00eau \u0111\u1ec1",
"Body": "N\u1ed9i dung",
"Footer": "Ch\u00e2n",
"Border color": "M\u00e0u vi\u1ec1n",
"Insert template": "Th\u00eam m\u1eabu",
"Templates": "M\u1eabu",
"Template": "Template",
"Text color": "M\u00e0u v\u0103n b\u1ea3n",
"Background color": "M\u00e0u n\u1ec1n",
"Custom...": "Tu\u1ef3 ch\u1ec9nh...",
"Custom color": "Tu\u1ef3 ch\u1ec9nh m\u00e0u",
"No color": "Kh\u00f4ng c\u00f3 m\u00e0u",
"Table of Contents": "Table of Contents",
"Show blocks": "Hi\u1ec3n th\u1ecb kh\u1ed1i",
"Show invisible characters": "Hi\u1ec3n th\u1ecb k\u00fd t\u1ef1 \u1ea9n",
"Words: {0}": "T\u1eeb: {0}",
"{0} words": "{0} words",
"File": "T\u1eadp tin",
"Edit": "S\u1eeda",
"Insert": "Ch\u00e8n",
"View": "Xem",
"Format": "\u0110\u1ecbnh d\u1ea1ng",
"Table": "B\u1ea3ng",
"Tools": "C\u00f4ng c\u1ee5",
"Powered by {0}": "Powered by {0}",
"Rich Text Area. Press ALT-F9 for menu. Press ALT-F10 for toolbar. Press ALT-0 for help": "Rich Text Area. B\u1ea5m ALT-F9 m\u1edf menu. B\u1ea5m ALT-F10 m\u1edf thanh c\u00f4ng c\u1ee5. B\u1ea5m ALT-0 m\u1edf tr\u1ee3 gi\u00fap"
});