//! moment-timezone.js
//! version : 0.5.28
//! Copyright (c) JS Foundation and other contributors
//! license : MIT
//! github.com/moment/moment-timezone

(function (root, factory) {
    "use strict";

    /*global define*/
    if (typeof module === 'object' && module.exports) {
        module.exports = factory(require('moment')); // Node
    } else if (typeof define === 'function' && define.amd) {
        define(['moment'], factory);                 // AMD
    } else {
        factory(root.moment);                        // Browser
    }
}(this, function (moment) {
    "use strict";

    // Do not load moment-timezone a second time.
    // if (moment.tz !== undefined) {
    // 	logError('Moment Timezone ' + moment.tz.version + ' was already loaded ' + (moment.tz.dataVersion ? 'with data from ' : 'without any data') + moment.tz.dataVersion);
    // 	return moment;
    // }

    var VERSION = "0.5.28",
        zones = {},
        links = {},
        countries = {},
        names = {},
        guesses = {},
        cachedGuess;

    if (!moment || typeof moment.version !== 'string') {
        logError('Moment Timezone requires Moment.js. See https://momentjs.com/timezone/docs/#/use-it/browser/');
    }

    var momentVersion = moment.version.split('.'),
        major = +momentVersion[0],
        minor = +momentVersion[1];

    // Moment.js version check
    if (major < 2 || (major === 2 && minor < 6)) {
        logError('Moment Timezone requires Moment.js >= 2.6.0. You are using Moment.js ' + moment.version + '. See momentjs.com');
    }

    /************************************
     Unpacking
     ************************************/

    function charCodeToInt(charCode) {
        if (charCode > 96) {
            return charCode - 87;
        } else if (charCode > 64) {
            return charCode - 29;
        }
        return charCode - 48;
    }

    function unpackBase60(string) {
        var i = 0,
            parts = string.split('.'),
            whole = parts[0],
            fractional = parts[1] || '',
            multiplier = 1,
            num,
            out = 0,
            sign = 1;

        // handle negative numbers
        if (string.charCodeAt(0) === 45) {
            i = 1;
            sign = -1;
        }

        // handle digits before the decimal
        for (i; i < whole.length; i++) {
            num = charCodeToInt(whole.charCodeAt(i));
            out = 60 * out + num;
        }

        // handle digits after the decimal
        for (i = 0; i < fractional.length; i++) {
            multiplier = multiplier / 60;
            num = charCodeToInt(fractional.charCodeAt(i));
            out += num * multiplier;
        }

        return out * sign;
    }

    function arrayToInt(array) {
        for (var i = 0; i < array.length; i++) {
            array[i] = unpackBase60(array[i]);
        }
    }

    function intToUntil(array, length) {
        for (var i = 0; i < length; i++) {
            array[i] = Math.round((array[i - 1] || 0) + (array[i] * 60000)); // minutes to milliseconds
        }

        array[length - 1] = Infinity;
    }

    function mapIndices(source, indices) {
        var out = [], i;

        for (i = 0; i < indices.length; i++) {
            out[i] = source[indices[i]];
        }

        return out;
    }

    function unpack(string) {
        var data = string.split('|'),
            offsets = data[2].split(' '),
            indices = data[3].split(''),
            untils = data[4].split(' ');

        arrayToInt(offsets);
        arrayToInt(indices);
        arrayToInt(untils);

        intToUntil(untils, indices.length);

        return {
            name: data[0],
            abbrs: mapIndices(data[1].split(' '), indices),
            offsets: mapIndices(offsets, indices),
            untils: untils,
            population: data[5] | 0
        };
    }

    /************************************
     Zone object
     ************************************/

    function Zone(packedString) {
        if (packedString) {
            this._set(unpack(packedString));
        }
    }

    Zone.prototype = {
        _set: function (unpacked) {
            this.name = unpacked.name;
            this.abbrs = unpacked.abbrs;
            this.untils = unpacked.untils;
            this.offsets = unpacked.offsets;
            this.population = unpacked.population;
        },

        _index: function (timestamp) {
            var target = +timestamp,
                untils = this.untils,
                i;

            for (i = 0; i < untils.length; i++) {
                if (target < untils[i]) {
                    return i;
                }
            }
        },

        countries: function () {
            var zone_name = this.name;
            return Object.keys(countries).filter(function (country_code) {
                return countries[country_code].zones.indexOf(zone_name) !== -1;
            });
        },

        parse: function (timestamp) {
            var target = +timestamp,
                offsets = this.offsets,
                untils = this.untils,
                max = untils.length - 1,
                offset, offsetNext, offsetPrev, i;

            for (i = 0; i < max; i++) {
                offset = offsets[i];
                offsetNext = offsets[i + 1];
                offsetPrev = offsets[i ? i - 1 : i];

                if (offset < offsetNext && tz.moveAmbiguousForward) {
                    offset = offsetNext;
                } else if (offset > offsetPrev && tz.moveInvalidForward) {
                    offset = offsetPrev;
                }

                if (target < untils[i] - (offset * 60000)) {
                    return offsets[i];
                }
            }

            return offsets[max];
        },

        abbr: function (mom) {
            return this.abbrs[this._index(mom)];
        },

        offset: function (mom) {
            logError("zone.offset has been deprecated in favor of zone.utcOffset");
            return this.offsets[this._index(mom)];
        },

        utcOffset: function (mom) {
            return this.offsets[this._index(mom)];
        }
    };

    /************************************
     Country object
     ************************************/

    function Country(country_name, zone_names) {
        this.name = country_name;
        this.zones = zone_names;
    }

    /************************************
     Current Timezone
     ************************************/

    function OffsetAt(at) {
        var timeString = at.toTimeString();
        var abbr = timeString.match(/\([a-z ]+\)/i);
        if (abbr && abbr[0]) {
            // 17:56:31 GMT-0600 (CST)
            // 17:56:31 GMT-0600 (Central Standard Time)
            abbr = abbr[0].match(/[A-Z]/g);
            abbr = abbr ? abbr.join('') : undefined;
        } else {
            // 17:56:31 CST
            // 17:56:31 GMT+0800 (台北標準時間)
            abbr = timeString.match(/[A-Z]{3,5}/g);
            abbr = abbr ? abbr[0] : undefined;
        }

        if (abbr === 'GMT') {
            abbr = undefined;
        }

        this.at = +at;
        this.abbr = abbr;
        this.offset = at.getTimezoneOffset();
    }

    function ZoneScore(zone) {
        this.zone = zone;
        this.offsetScore = 0;
        this.abbrScore = 0;
    }

    ZoneScore.prototype.scoreOffsetAt = function (offsetAt) {
        this.offsetScore += Math.abs(this.zone.utcOffset(offsetAt.at) - offsetAt.offset);
        if (this.zone.abbr(offsetAt.at).replace(/[^A-Z]/g, '') !== offsetAt.abbr) {
            this.abbrScore++;
        }
    };

    function findChange(low, high) {
        var mid, diff;

        while ((diff = ((high.at - low.at) / 12e4 | 0) * 6e4)) {
            mid = new OffsetAt(new Date(low.at + diff));
            if (mid.offset === low.offset) {
                low = mid;
            } else {
                high = mid;
            }
        }

        return low;
    }

    function userOffsets() {
        var startYear = new Date().getFullYear() - 2,
            last = new OffsetAt(new Date(startYear, 0, 1)),
            offsets = [last],
            change, next, i;

        for (i = 1; i < 48; i++) {
            next = new OffsetAt(new Date(startYear, i, 1));
            if (next.offset !== last.offset) {
                change = findChange(last, next);
                offsets.push(change);
                offsets.push(new OffsetAt(new Date(change.at + 6e4)));
            }
            last = next;
        }

        for (i = 0; i < 4; i++) {
            offsets.push(new OffsetAt(new Date(startYear + i, 0, 1)));
            offsets.push(new OffsetAt(new Date(startYear + i, 6, 1)));
        }

        return offsets;
    }

    function sortZoneScores(a, b) {
        if (a.offsetScore !== b.offsetScore) {
            return a.offsetScore - b.offsetScore;
        }
        if (a.abbrScore !== b.abbrScore) {
            return a.abbrScore - b.abbrScore;
        }
        if (a.zone.population !== b.zone.population) {
            return b.zone.population - a.zone.population;
        }
        return b.zone.name.localeCompare(a.zone.name);
    }

    function addToGuesses(name, offsets) {
        var i, offset;
        arrayToInt(offsets);
        for (i = 0; i < offsets.length; i++) {
            offset = offsets[i];
            guesses[offset] = guesses[offset] || {};
            guesses[offset][name] = true;
        }
    }

    function guessesForUserOffsets(offsets) {
        var offsetsLength = offsets.length,
            filteredGuesses = {},
            out = [],
            i, j, guessesOffset;

        for (i = 0; i < offsetsLength; i++) {
            guessesOffset = guesses[offsets[i].offset] || {};
            for (j in guessesOffset) {
                if (guessesOffset.hasOwnProperty(j)) {
                    filteredGuesses[j] = true;
                }
            }
        }

        for (i in filteredGuesses) {
            if (filteredGuesses.hasOwnProperty(i)) {
                out.push(names[i]);
            }
        }

        return out;
    }

    function rebuildGuess() {

        // use Intl API when available and returning valid time zone
        try {
            var intlName = Intl.DateTimeFormat().resolvedOptions().timeZone;
            if (intlName && intlName.length > 3) {
                var name = names[normalizeName(intlName)];
                if (name) {
                    return name;
                }
                logError("Moment Timezone found " + intlName + " from the Intl api, but did not have that data loaded.");
            }
        } catch (e) {
            // Intl unavailable, fall back to manual guessing.
        }

        var offsets = userOffsets(),
            offsetsLength = offsets.length,
            guesses = guessesForUserOffsets(offsets),
            zoneScores = [],
            zoneScore, i, j;

        for (i = 0; i < guesses.length; i++) {
            zoneScore = new ZoneScore(getZone(guesses[i]), offsetsLength);
            for (j = 0; j < offsetsLength; j++) {
                zoneScore.scoreOffsetAt(offsets[j]);
            }
            zoneScores.push(zoneScore);
        }

        zoneScores.sort(sortZoneScores);

        return zoneScores.length > 0 ? zoneScores[0].zone.name : undefined;
    }

    function guess(ignoreCache) {
        if (!cachedGuess || ignoreCache) {
            cachedGuess = rebuildGuess();
        }
        return cachedGuess;
    }

    /************************************
     Global Methods
     ************************************/

    function normalizeName(name) {
        return (name || '').toLowerCase().replace(/\//g, '_');
    }

    function addZone(packed) {
        var i, name, split, normalized;

        if (typeof packed === "string") {
            packed = [packed];
        }

        for (i = 0; i < packed.length; i++) {
            split = packed[i].split('|');
            name = split[0];
            normalized = normalizeName(name);
            zones[normalized] = packed[i];
            names[normalized] = name;
            addToGuesses(normalized, split[2].split(' '));
        }
    }

    function getZone(name, caller) {

        name = normalizeName(name);

        var zone = zones[name];
        var link;

        if (zone instanceof Zone) {
            return zone;
        }

        if (typeof zone === 'string') {
            zone = new Zone(zone);
            zones[name] = zone;
            return zone;
        }

        // Pass getZone to prevent recursion more than 1 level deep
        if (links[name] && caller !== getZone && (link = getZone(links[name], getZone))) {
            zone = zones[name] = new Zone();
            zone._set(link);
            zone.name = names[name];
            return zone;
        }

        return null;
    }

    function getNames() {
        var i, out = [];

        for (i in names) {
            if (names.hasOwnProperty(i) && (zones[i] || zones[links[i]]) && names[i]) {
                out.push(names[i]);
            }
        }

        return out.sort();
    }

    function getCountryNames() {
        return Object.keys(countries);
    }

    function addLink(aliases) {
        var i, alias, normal0, normal1;

        if (typeof aliases === "string") {
            aliases = [aliases];
        }

        for (i = 0; i < aliases.length; i++) {
            alias = aliases[i].split('|');

            normal0 = normalizeName(alias[0]);
            normal1 = normalizeName(alias[1]);

            links[normal0] = normal1;
            names[normal0] = alias[0];

            links[normal1] = normal0;
            names[normal1] = alias[1];
        }
    }

    function addCountries(data) {
        var i, country_code, country_zones, split;
        if (!data || !data.length) return;
        for (i = 0; i < data.length; i++) {
            split = data[i].split('|');
            country_code = split[0].toUpperCase();
            country_zones = split[1].split(' ');
            countries[country_code] = new Country(
                country_code,
                country_zones
            );
        }
    }

    function getCountry(name) {
        name = name.toUpperCase();
        return countries[name] || null;
    }

    function zonesForCountry(country, with_offset) {
        country = getCountry(country);

        if (!country) return null;

        var zones = country.zones.sort();

        if (with_offset) {
            return zones.map(function (zone_name) {
                var zone = getZone(zone_name);
                return {
                    name: zone_name,
                    offset: zone.utcOffset(new Date())
                };
            });
        }

        return zones;
    }

    function loadData(data) {
        addZone(data.zones);
        addLink(data.links);
        addCountries(data.countries);
        tz.dataVersion = data.version;
    }

    function zoneExists(name) {
        if (!zoneExists.didShowError) {
            zoneExists.didShowError = true;
            logError("moment.tz.zoneExists('" + name + "') has been deprecated in favor of !moment.tz.zone('" + name + "')");
        }
        return !!getZone(name);
    }

    function needsOffset(m) {
        var isUnixTimestamp = (m._f === 'X' || m._f === 'x');
        return !!(m._a && (m._tzm === undefined) && !isUnixTimestamp);
    }

    function logError(message) {
        if (typeof console !== 'undefined' && typeof console.error === 'function') {
            console.error(message);
        }
    }

    /************************************
     moment.tz namespace
     ************************************/

    function tz(input) {
        var args = Array.prototype.slice.call(arguments, 0, -1),
            name = arguments[arguments.length - 1],
            zone = getZone(name),
            out = moment.utc.apply(null, args);

        if (zone && !moment.isMoment(input) && needsOffset(out)) {
            out.add(zone.parse(out), 'minutes');
        }

        out.tz(name);

        return out;
    }

    tz.version = VERSION;
    tz.dataVersion = '';
    tz._zones = zones;
    tz._links = links;
    tz._names = names;
    tz._countries = countries;
    tz.add = addZone;
    tz.link = addLink;
    tz.load = loadData;
    tz.zone = getZone;
    tz.zoneExists = zoneExists; // deprecated in 0.1.0
    tz.guess = guess;
    tz.names = getNames;
    tz.Zone = Zone;
    tz.unpack = unpack;
    tz.unpackBase60 = unpackBase60;
    tz.needsOffset = needsOffset;
    tz.moveInvalidForward = true;
    tz.moveAmbiguousForward = false;
    tz.countries = getCountryNames;
    tz.zonesForCountry = zonesForCountry;

    /************************************
     Interfaces with Moment.js
     ************************************/

    var fn = moment.fn;

    moment.tz = tz;

    moment.defaultZone = null;

    moment.updateOffset = function (mom, keepTime) {
        var zone = moment.defaultZone,
            offset;

        if (mom._z === undefined) {
            if (zone && needsOffset(mom) && !mom._isUTC) {
                mom._d = moment.utc(mom._a)._d;
                mom.utc().add(zone.parse(mom), 'minutes');
            }
            mom._z = zone;
        }
        if (mom._z) {
            offset = mom._z.utcOffset(mom);
            if (Math.abs(offset) < 16) {
                offset = offset / 60;
            }
            if (mom.utcOffset !== undefined) {
                var z = mom._z;
                mom.utcOffset(-offset, keepTime);
                mom._z = z;
            } else {
                mom.zone(offset, keepTime);
            }
        }
    };

    fn.tz = function (name, keepTime) {
        if (name) {
            if (typeof name !== 'string') {
                throw new Error('Time zone name must be a string, got ' + name + ' [' + typeof name + ']');
            }
            this._z = getZone(name);
            if (this._z) {
                moment.updateOffset(this, keepTime);
            } else {
                logError("Moment Timezone has no data for " + name + ". See http://momentjs.com/timezone/docs/#/data-loading/.");
            }
            return this;
        }
        if (this._z) {
            return this._z.name;
        }
    };

    function abbrWrap(old) {
        return function () {
            if (this._z) {
                return this._z.abbr(this);
            }
            return old.call(this);
        };
    }

    function resetZoneWrap(old) {
        return function () {
            this._z = null;
            return old.apply(this, arguments);
        };
    }

    function resetZoneWrap2(old) {
        return function () {
            if (arguments.length > 0) this._z = null;
            return old.apply(this, arguments);
        };
    }

    fn.zoneName = abbrWrap(fn.zoneName);
    fn.zoneAbbr = abbrWrap(fn.zoneAbbr);
    fn.utc = resetZoneWrap(fn.utc);
    fn.local = resetZoneWrap(fn.local);
    fn.utcOffset = resetZoneWrap2(fn.utcOffset);

    moment.tz.setDefault = function (name) {
        if (major < 2 || (major === 2 && minor < 9)) {
            logError('Moment Timezone setDefault() requires Moment.js >= 2.9.0. You are using Moment.js ' + moment.version + '.');
        }
        moment.defaultZone = name ? getZone(name) : null;
        return moment;
    };

    // Cloning a moment should include the _z property.
    var momentProperties = moment.momentProperties;
    if (Object.prototype.toString.call(momentProperties) === '[object Array]') {
        // moment 2.8.1+
        momentProperties.push('_z');
        momentProperties.push('_a');
    } else if (momentProperties) {
        // moment 2.7.0
        momentProperties._z = null;
    }

    loadData({
        "version": "2019c",
        "zones": [
            "Africa/Abidjan|GMT|0|0||48e5",
            "Africa/Nairobi|EAT|-30|0||47e5",
            "Africa/Algiers|CET|-10|0||26e5",
            "Africa/Lagos|WAT|-10|0||17e6",
            "Africa/Maputo|CAT|-20|0||26e5",
            "Africa/Cairo|EET|-20|0||15e6",
            "Africa/Casablanca|+00 +01|0 -10|010101010101010101010101010101|1O9e0 uM0 e00 Dc0 11A0 s00 e00 IM0 WM0 mo0 gM0 LA0 WM0 jA0 e00 28M0 e00 2600 e00 28M0 e00 2600 gM0 2600 e00 28M0 e00 2600 gM0|32e5",
            "Europe/Paris|CET CEST|-10 -20|01010101010101010101010|1O9d0 1o00 11A0 1qM0 WM0 1qM0 WM0 1qM0 11A0 1o00 11A0 1o00 11A0 1qM0 WM0 1qM0 WM0 1qM0 11A0 1o00 11A0 1o00|11e6",
            "Africa/Johannesburg|SAST|-20|0||84e5",
            "Africa/Khartoum|EAT CAT|-30 -20|01|1Usl0|51e5",
            "Africa/Sao_Tome|GMT WAT|0 -10|010|1UQN0 2q00|",
            "Africa/Windhoek|CAT WAT|-20 -10|0101010|1Oc00 11B0 1nX0 11B0 1nX0 11B0|32e4",
            "America/Adak|HST HDT|a0 90|01010101010101010101010|1O100 1zb0 Rd0 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0 Rd0 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0|326",
            "America/Anchorage|AKST AKDT|90 80|01010101010101010101010|1O0X0 1zb0 Rd0 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0 Rd0 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0|30e4",
            "America/Santo_Domingo|AST|40|0||29e5",
            "America/Fortaleza|-03|30|0||34e5",
            "America/Asuncion|-03 -04|30 40|01010101010101010101010|1O6r0 1ip0 19X0 1fB0 19X0 1fB0 19X0 1ip0 17b0 1ip0 17b0 1ip0 19X0 1fB0 19X0 1fB0 19X0 1fB0 19X0 1ip0 17b0 1ip0|28e5",
            "America/Panama|EST|50|0||15e5",
            "America/Mexico_City|CST CDT|60 50|01010101010101010101010|1Oc80 1lb0 14p0 1nX0 11B0 1nX0 11B0 1nX0 14p0 1lb0 14p0 1lb0 14p0 1nX0 11B0 1nX0 11B0 1nX0 14p0 1lb0 14p0 1lb0|20e6",
            "America/Managua|CST|60|0||22e5",
            "America/La_Paz|-04|40|0||19e5",
            "America/Lima|-05|50|0||11e6",
            "America/Denver|MST MDT|70 60|01010101010101010101010|1O0V0 1zb0 Rd0 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0 Rd0 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0|26e5",
            "America/Campo_Grande|-03 -04|30 40|0101010101|1NTf0 1zd0 On0 1zd0 On0 1zd0 On0 1HB0 FX0|77e4",
            "America/Cancun|CST EST|60 50|01|1NKU0|63e4",
            "America/Caracas|-0430 -04|4u 40|01|1QMT0|29e5",
            "America/Chicago|CST CDT|60 50|01010101010101010101010|1O0U0 1zb0 Rd0 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0 Rd0 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0|92e5",
            "America/Chihuahua|MST MDT|70 60|01010101010101010101010|1Oc90 1lb0 14p0 1nX0 11B0 1nX0 11B0 1nX0 14p0 1lb0 14p0 1lb0 14p0 1nX0 11B0 1nX0 11B0 1nX0 14p0 1lb0 14p0 1lb0|81e4",
            "America/Phoenix|MST|70|0||42e5",
            "America/Los_Angeles|PST PDT|80 70|01010101010101010101010|1O0W0 1zb0 Rd0 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0 Rd0 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0|15e6",
            "America/New_York|EST EDT|50 40|01010101010101010101010|1O0T0 1zb0 Rd0 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0 Rd0 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0|21e6",
            "America/Fort_Nelson|PST MST|80 70|01|1O0W0|39e2",
            "America/Halifax|AST ADT|40 30|01010101010101010101010|1O0S0 1zb0 Rd0 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0 Rd0 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0|39e4",
            "America/Godthab|-03 -02|30 20|01010101010101010101010|1O9d0 1o00 11A0 1qM0 WM0 1qM0 WM0 1qM0 11A0 1o00 11A0 1o00 11A0 1qM0 WM0 1qM0 WM0 1qM0 11A0 1o00 11A0 1o00|17e3",
            "America/Grand_Turk|EST EDT AST|50 40 40|0121010101010101010|1O0T0 1zb0 5Ip0 1zb0 Op0 1zb0 Op0 1zb0 Rd0 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0|37e2",
            "America/Havana|CST CDT|50 40|01010101010101010101010|1O0R0 1zc0 Rc0 1zc0 Oo0 1zc0 Oo0 1zc0 Oo0 1zc0 Oo0 1zc0 Rc0 1zc0 Oo0 1zc0 Oo0 1zc0 Oo0 1zc0 Oo0 1zc0|21e5",
            "America/Metlakatla|PST AKST AKDT|80 90 80|01212120121212121212121|1PAa0 Rd0 1zb0 Op0 1zb0 Op0 1zb0 uM0 jB0 1zb0 Op0 1zb0 Rd0 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0|14e2",
            "America/Miquelon|-03 -02|30 20|01010101010101010101010|1O0R0 1zb0 Rd0 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0 Rd0 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0|61e2",
            "America/Montevideo|-02 -03|20 30|01|1O0Q0|17e5",
            "America/Noronha|-02|20|0||30e2",
            "America/Port-au-Prince|EST EDT|50 40|010101010101010101010|1O0T0 1zb0 3iN0 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0 Rd0 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0|23e5",
            "Antarctica/Palmer|-03 -04|30 40|010|1QSr0 Ap0|40",
            "America/Santiago|-03 -04|30 40|010101010101010101010|1QSr0 Ap0 1Nb0 Ap0 1Nb0 Ap0 1zb0 11B0 1nX0 11B0 1nX0 11B0 1nX0 11B0 1nX0 11B0 1qL0 11B0 1nX0 11B0|62e5",
            "America/Sao_Paulo|-02 -03|20 30|0101010101|1NTe0 1zd0 On0 1zd0 On0 1zd0 On0 1HB0 FX0|20e6",
            "Atlantic/Azores|-01 +00|10 0|01010101010101010101010|1O9d0 1o00 11A0 1qM0 WM0 1qM0 WM0 1qM0 11A0 1o00 11A0 1o00 11A0 1qM0 WM0 1qM0 WM0 1qM0 11A0 1o00 11A0 1o00|25e4",
            "America/St_Johns|NST NDT|3u 2u|01010101010101010101010|1O0Ru 1zb0 Rd0 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0 Rd0 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0 Op0 1zb0|11e4",
            "Antarctica/Casey|+08 +11|-80 -b0|010|1RWg0 3m10|10",
            "Asia/Bangkok|+07|-70|0||15e6",
            "Asia/Vladivostok|+10|-a0|0||60e4",
            "Pacific/Bougainville|+11|-b0|0||18e4",
            "Asia/Tashkent|+05|-50|0||23e5",
            "Pacific/Auckland|NZDT NZST|-d0 -c0|01010101010101010101010|1ObO0 1a00 1fA0 1a00 1fA0 1a00 1fA0 1cM0 1fA0 1a00 1fA0 1a00 1fA0 1a00 1fA0 1a00 1fA0 1a00 1io0 1a00 1fA0 1a00|14e5",
            "Asia/Baghdad|+03|-30|0||66e5",
            "Antarctica/Troll|+00 +02|0 -20|01010101010101010101010|1O9d0 1o00 11A0 1qM0 WM0 1qM0 WM0 1qM0 11A0 1o00 11A0 1o00 11A0 1qM0 WM0 1qM0 WM0 1qM0 11A0 1o00 11A0 1o00|40",
            "Asia/Dhaka|+06|-60|0||16e6",
            "Asia/Amman|EET EEST|-20 -30|01010101010101010101010|1O8m0 1qM0 11A0 1o00 11A0 1o00 11A0 1o00 11A0 1o00 11A0 1qM0 WM0 1qM0 11A0 1o00 11A0 1o00 11A0 1o00 11A0 1qM0|25e5",
            "Asia/Kamchatka|+12|-c0|0||18e4",
            "Asia/Baku|+04 +05|-40 -50|010|1O9c0 1o00|27e5",
            "Asia/Barnaul|+06 +07|-60 -70|01|1QyI0|",
            "Asia/Beirut|EET EEST|-20 -30|01010101010101010101010|1O9a0 1nX0 11B0 1qL0 WN0 1qL0 WN0 1qL0 11B0 1nX0 11B0 1nX0 11B0 1qL0 WN0 1qL0 WN0 1qL0 11B0 1nX0 11B0 1nX0|22e5",
            "Asia/Kuala_Lumpur|+08|-80|0||71e5",
            "Asia/Kolkata|IST|-5u|0||15e6",
            "Asia/Chita|+08 +09|-80 -90|01|1QyG0|33e4",
            "Asia/Ulaanbaatar|+08 +09|-80 -90|01010|1O8G0 1cJ0 1cP0 1cJ0|12e5",
            "Asia/Shanghai|CST|-80|0||23e6",
            "Asia/Colombo|+0530|-5u|0||22e5",
            "Asia/Damascus|EET EEST|-20 -30|01010101010101010101010|1O8m0 1qL0 WN0 1qL0 11B0 1nX0 11B0 1nX0 11B0 1nX0 11B0 1qL0 WN0 1qL0 WN0 1qL0 11B0 1nX0 11B0 1nX0 11B0 1qL0|26e5",
            "Asia/Yakutsk|+09|-90|0||28e4",
            "Asia/Dubai|+04|-40|0||39e5",
            "Asia/Famagusta|EET EEST +03|-20 -30 -30|0101201010101010101010|1O9d0 1o00 11A0 15U0 2Ks0 WM0 1qM0 11A0 1o00 11A0 1o00 11A0 1qM0 WM0 1qM0 WM0 1qM0 11A0 1o00 11A0 1o00|",
            "Asia/Gaza|EET EEST|-20 -30|01010101010101010101010|1O8K0 1nz0 1220 1qL0 WN0 1qL0 WN0 1qL0 11c0 1oo0 11c0 1rc0 Wo0 1rc0 Wo0 1rc0 11c0 1oo0 11c0 1oo0 11c0 1oo0|18e5",
            "Asia/Hong_Kong|HKT|-80|0||73e5",
            "Asia/Hovd|+07 +08|-70 -80|01010|1O8H0 1cJ0 1cP0 1cJ0|81e3",
            "Europe/Istanbul|EET EEST +03|-20 -30 -30|01012|1O9d0 1tA0 U00 15w0|13e6",
            "Asia/Jakarta|WIB|-70|0||31e6",
            "Asia/Jayapura|WIT|-90|0||26e4",
            "Asia/Jerusalem|IST IDT|-20 -30|01010101010101010101010|1O8o0 1oL0 10N0 1rz0 W10 1rz0 W10 1rz0 10N0 1oL0 10N0 1oL0 10N0 1rz0 W10 1rz0 W10 1rz0 10N0 1oL0 10N0 1oL0|81e4",
            "Asia/Kabul|+0430|-4u|0||46e5",
            "Asia/Karachi|PKT|-50|0||24e6",
            "Asia/Kathmandu|+0545|-5J|0||12e5",
            "Asia/Magadan|+10 +11|-a0 -b0|01|1QJQ0|95e3",
            "Asia/Makassar|WITA|-80|0||15e5",
            "Asia/Manila|PST|-80|0||24e6",
            "Europe/Athens|EET EEST|-20 -30|01010101010101010101010|1O9d0 1o00 11A0 1qM0 WM0 1qM0 WM0 1qM0 11A0 1o00 11A0 1o00 11A0 1qM0 WM0 1qM0 WM0 1qM0 11A0 1o00 11A0 1o00|35e5",
            "Asia/Novosibirsk|+06 +07|-60 -70|01|1Rmk0|15e5",
            "Asia/Pyongyang|KST KST|-90 -8u|010|1P4D0 6BA0|29e5",
            "Asia/Qyzylorda|+06 +05|-60 -50|01|1Xei0|73e4",
            "Asia/Rangoon|+0630|-6u|0||48e5",
            "Asia/Sakhalin|+10 +11|-a0 -b0|01|1QyE0|58e4",
            "Asia/Seoul|KST|-90|0||23e6",
            "Asia/Tehran|+0330 +0430|-3u -4u|01010101010101010101010|1O6ku 1dz0 1cp0 1dz0 1cN0 1dz0 1cp0 1dz0 1cp0 1dz0 1cp0 1dz0 1cN0 1dz0 1cp0 1dz0 1cp0 1dz0 1cp0 1dz0 1cN0 1dz0|14e6",
            "Asia/Tokyo|JST|-90|0||38e6",
            "Asia/Tomsk|+06 +07|-60 -70|01|1QXU0|10e5",
            "Europe/Lisbon|WET WEST|0 -10|01010101010101010101010|1O9d0 1o00 11A0 1qM0 WM0 1qM0 WM0 1qM0 11A0 1o00 11A0 1o00 11A0 1qM0 WM0 1qM0 WM0 1qM0 11A0 1o00 11A0 1o00|27e5",
            "Atlantic/Cape_Verde|-01|10|0||50e4",
            "Australia/Sydney|AEDT AEST|-b0 -a0|01010101010101010101010|1ObQ0 1cM0 1cM0 1cM0 1cM0 1cM0 1cM0 1fA0 1cM0 1cM0 1cM0 1cM0 1cM0 1cM0 1cM0 1cM0 1cM0 1cM0 1fA0 1cM0 1cM0 1cM0|40e5",
            "Australia/Adelaide|ACDT ACST|-au -9u|01010101010101010101010|1ObQu 1cM0 1cM0 1cM0 1cM0 1cM0 1cM0 1fA0 1cM0 1cM0 1cM0 1cM0 1cM0 1cM0 1cM0 1cM0 1cM0 1cM0 1fA0 1cM0 1cM0 1cM0|11e5",
            "Australia/Brisbane|AEST|-a0|0||20e5",
            "Australia/Darwin|ACST|-9u|0||12e4",
            "Australia/Eucla|+0845|-8J|0||368",
            "Australia/Lord_Howe|+11 +1030|-b0 -au|01010101010101010101010|1ObP0 1cMu 1cLu 1cMu 1cLu 1cMu 1cLu 1fAu 1cLu 1cMu 1cLu 1cMu 1cLu 1cMu 1cLu 1cMu 1cLu 1cMu 1fzu 1cMu 1cLu 1cMu|347",
            "Australia/Perth|AWST|-80|0||18e5",
            "Pacific/Easter|-05 -06|50 60|010101010101010101010|1QSr0 Ap0 1Nb0 Ap0 1Nb0 Ap0 1zb0 11B0 1nX0 11B0 1nX0 11B0 1nX0 11B0 1nX0 11B0 1qL0 11B0 1nX0 11B0|30e2",
            "Europe/Dublin|GMT IST|0 -10|01010101010101010101010|1O9d0 1o00 11A0 1qM0 WM0 1qM0 WM0 1qM0 11A0 1o00 11A0 1o00 11A0 1qM0 WM0 1qM0 WM0 1qM0 11A0 1o00 11A0 1o00|12e5",
            "Etc/GMT-1|+01|-10|0||",
            "Pacific/Fakaofo|+13|-d0|0||483",
            "Pacific/Kiritimati|+14|-e0|0||51e2",
            "Etc/GMT-2|+02|-20|0||",
            "Pacific/Tahiti|-10|a0|0||18e4",
            "Pacific/Niue|-11|b0|0||12e2",
            "Etc/GMT+12|-12|c0|0||",
            "Pacific/Galapagos|-06|60|0||25e3",
            "Etc/GMT+7|-07|70|0||",
            "Pacific/Pitcairn|-08|80|0||56",
            "Pacific/Gambier|-09|90|0||125",
            "Etc/UTC|UTC|0|0||",
            "Europe/Ulyanovsk|+03 +04|-30 -40|01|1QyL0|13e5",
            "Europe/London|GMT BST|0 -10|01010101010101010101010|1O9d0 1o00 11A0 1qM0 WM0 1qM0 WM0 1qM0 11A0 1o00 11A0 1o00 11A0 1qM0 WM0 1qM0 WM0 1qM0 11A0 1o00 11A0 1o00|10e6",
            "Europe/Chisinau|EET EEST|-20 -30|01010101010101010101010|1O9c0 1o00 11A0 1qM0 WM0 1qM0 WM0 1qM0 11A0 1o00 11A0 1o00 11A0 1qM0 WM0 1qM0 WM0 1qM0 11A0 1o00 11A0 1o00|67e4",
            "Europe/Moscow|MSK|-30|0||16e6",
            "Europe/Saratov|+03 +04|-30 -40|01|1Sfz0|",
            "Europe/Volgograd|+03 +04|-30 -40|01|1WQL0|10e5",
            "Pacific/Honolulu|HST|a0|0||37e4",
            "MET|MET MEST|-10 -20|01010101010101010101010|1O9d0 1o00 11A0 1qM0 WM0 1qM0 WM0 1qM0 11A0 1o00 11A0 1o00 11A0 1qM0 WM0 1qM0 WM0 1qM0 11A0 1o00 11A0 1o00|",
            "Pacific/Chatham|+1345 +1245|-dJ -cJ|01010101010101010101010|1ObO0 1a00 1fA0 1a00 1fA0 1a00 1fA0 1cM0 1fA0 1a00 1fA0 1a00 1fA0 1a00 1fA0 1a00 1fA0 1a00 1io0 1a00 1fA0 1a00|600",
            "Pacific/Apia|+14 +13|-e0 -d0|01010101010101010101010|1ObO0 1a00 1fA0 1a00 1fA0 1a00 1fA0 1cM0 1fA0 1a00 1fA0 1a00 1fA0 1a00 1fA0 1a00 1fA0 1a00 1io0 1a00 1fA0 1a00|37e3",
            "Pacific/Fiji|+13 +12|-d0 -c0|01010101010101010101010|1NF20 1SM0 uM0 1VA0 s00 1VA0 s00 1VA0 s00 20o0 pc0 20o0 s00 20o0 pc0 20o0 pc0 20o0 pc0 20o0 pc0 20o0|88e4",
            "Pacific/Guam|ChST|-a0|0||17e4",
            "Pacific/Marquesas|-0930|9u|0||86e2",
            "Pacific/Pago_Pago|SST|b0|0||37e2",
            "Pacific/Norfolk|+1130 +11 +12|-bu -b0 -c0|012121212121212|1PoCu 9Jcu 1cM0 1cM0 1cM0 1cM0 1cM0 1cM0 1cM0 1cM0 1fA0 1cM0 1cM0 1cM0|25e4",
            "Pacific/Tongatapu|+13 +14|-d0 -e0|010|1S4d0 s00|75e3"
        ],
        "links": [
            "Africa/Abidjan|Africa/Accra",
            "Africa/Abidjan|Africa/Bamako",
            "Africa/Abidjan|Africa/Banjul",
            "Africa/Abidjan|Africa/Bissau",
            "Africa/Abidjan|Africa/Conakry",
            "Africa/Abidjan|Africa/Dakar",
            "Africa/Abidjan|Africa/Freetown",
            "Africa/Abidjan|Africa/Lome",
            "Africa/Abidjan|Africa/Monrovia",
            "Africa/Abidjan|Africa/Nouakchott",
            "Africa/Abidjan|Africa/Ouagadougou",
            "Africa/Abidjan|Africa/Timbuktu",
            "Africa/Abidjan|America/Danmarkshavn",
            "Africa/Abidjan|Atlantic/Reykjavik",
            "Africa/Abidjan|Atlantic/St_Helena",
            "Africa/Abidjan|Etc/GMT",
            "Africa/Abidjan|Etc/GMT+0",
            "Africa/Abidjan|Etc/GMT-0",
            "Africa/Abidjan|Etc/GMT0",
            "Africa/Abidjan|Etc/Greenwich",
            "Africa/Abidjan|GMT",
            "Africa/Abidjan|GMT+0",
            "Africa/Abidjan|GMT-0",
            "Africa/Abidjan|GMT0",
            "Africa/Abidjan|Greenwich",
            "Africa/Abidjan|Iceland",
            "Africa/Algiers|Africa/Tunis",
            "Africa/Cairo|Africa/Tripoli",
            "Africa/Cairo|Egypt",
            "Africa/Cairo|Europe/Kaliningrad",
            "Africa/Cairo|Libya",
            "Africa/Casablanca|Africa/El_Aaiun",
            "Africa/Johannesburg|Africa/Maseru",
            "Africa/Johannesburg|Africa/Mbabane",
            "Africa/Lagos|Africa/Bangui",
            "Africa/Lagos|Africa/Brazzaville",
            "Africa/Lagos|Africa/Douala",
            "Africa/Lagos|Africa/Kinshasa",
            "Africa/Lagos|Africa/Libreville",
            "Africa/Lagos|Africa/Luanda",
            "Africa/Lagos|Africa/Malabo",
            "Africa/Lagos|Africa/Ndjamena",
            "Africa/Lagos|Africa/Niamey",
            "Africa/Lagos|Africa/Porto-Novo",
            "Africa/Maputo|Africa/Blantyre",
            "Africa/Maputo|Africa/Bujumbura",
            "Africa/Maputo|Africa/Gaborone",
            "Africa/Maputo|Africa/Harare",
            "Africa/Maputo|Africa/Kigali",
            "Africa/Maputo|Africa/Lubumbashi",
            "Africa/Maputo|Africa/Lusaka",
            "Africa/Nairobi|Africa/Addis_Ababa",
            "Africa/Nairobi|Africa/Asmara",
            "Africa/Nairobi|Africa/Asmera",
            "Africa/Nairobi|Africa/Dar_es_Salaam",
            "Africa/Nairobi|Africa/Djibouti",
            "Africa/Nairobi|Africa/Juba",
            "Africa/Nairobi|Africa/Kampala",
            "Africa/Nairobi|Africa/Mogadishu",
            "Africa/Nairobi|Indian/Antananarivo",
            "Africa/Nairobi|Indian/Comoro",
            "Africa/Nairobi|Indian/Mayotte",
            "America/Adak|America/Atka",
            "America/Adak|US/Aleutian",
            "America/Anchorage|America/Juneau",
            "America/Anchorage|America/Nome",
            "America/Anchorage|America/Sitka",
            "America/Anchorage|America/Yakutat",
            "America/Anchorage|US/Alaska",
            "America/Campo_Grande|America/Cuiaba",
            "America/Chicago|America/Indiana/Knox",
            "America/Chicago|America/Indiana/Tell_City",
            "America/Chicago|America/Knox_IN",
            "America/Chicago|America/Matamoros",
            "America/Chicago|America/Menominee",
            "America/Chicago|America/North_Dakota/Beulah",
            "America/Chicago|America/North_Dakota/Center",
            "America/Chicago|America/North_Dakota/New_Salem",
            "America/Chicago|America/Rainy_River",
            "America/Chicago|America/Rankin_Inlet",
            "America/Chicago|America/Resolute",
            "America/Chicago|America/Winnipeg",
            "America/Chicago|CST6CDT",
            "America/Chicago|Canada/Central",
            "America/Chicago|US/Central",
            "America/Chicago|US/Indiana-Starke",
            "America/Chihuahua|America/Mazatlan",
            "America/Chihuahua|Mexico/BajaSur",
            "America/Denver|America/Boise",
            "America/Denver|America/Cambridge_Bay",
            "America/Denver|America/Edmonton",
            "America/Denver|America/Inuvik",
            "America/Denver|America/Ojinaga",
            "America/Denver|America/Shiprock",
            "America/Denver|America/Yellowknife",
            "America/Denver|Canada/Mountain",
            "America/Denver|MST7MDT",
            "America/Denver|Navajo",
            "America/Denver|US/Mountain",
            "America/Fortaleza|America/Araguaina",
            "America/Fortaleza|America/Argentina/Buenos_Aires",
            "America/Fortaleza|America/Argentina/Catamarca",
            "America/Fortaleza|America/Argentina/ComodRivadavia",
            "America/Fortaleza|America/Argentina/Cordoba",
            "America/Fortaleza|America/Argentina/Jujuy",
            "America/Fortaleza|America/Argentina/La_Rioja",
            "America/Fortaleza|America/Argentina/Mendoza",
            "America/Fortaleza|America/Argentina/Rio_Gallegos",
            "America/Fortaleza|America/Argentina/Salta",
            "America/Fortaleza|America/Argentina/San_Juan",
            "America/Fortaleza|America/Argentina/San_Luis",
            "America/Fortaleza|America/Argentina/Tucuman",
            "America/Fortaleza|America/Argentina/Ushuaia",
            "America/Fortaleza|America/Bahia",
            "America/Fortaleza|America/Belem",
            "America/Fortaleza|America/Buenos_Aires",
            "America/Fortaleza|America/Catamarca",
            "America/Fortaleza|America/Cayenne",
            "America/Fortaleza|America/Cordoba",
            "America/Fortaleza|America/Jujuy",
            "America/Fortaleza|America/Maceio",
            "America/Fortaleza|America/Mendoza",
            "America/Fortaleza|America/Paramaribo",
            "America/Fortaleza|America/Recife",
            "America/Fortaleza|America/Rosario",
            "America/Fortaleza|America/Santarem",
            "America/Fortaleza|Antarctica/Rothera",
            "America/Fortaleza|Atlantic/Stanley",
            "America/Fortaleza|Etc/GMT+3",
            "America/Halifax|America/Glace_Bay",
            "America/Halifax|America/Goose_Bay",
            "America/Halifax|America/Moncton",
            "America/Halifax|America/Thule",
            "America/Halifax|Atlantic/Bermuda",
            "America/Halifax|Canada/Atlantic",
            "America/Havana|Cuba",
            "America/La_Paz|America/Boa_Vista",
            "America/La_Paz|America/Guyana",
            "America/La_Paz|America/Manaus",
            "America/La_Paz|America/Porto_Velho",
            "America/La_Paz|Brazil/West",
            "America/La_Paz|Etc/GMT+4",
            "America/Lima|America/Bogota",
            "America/Lima|America/Eirunepe",
            "America/Lima|America/Guayaquil",
            "America/Lima|America/Porto_Acre",
            "America/Lima|America/Rio_Branco",
            "America/Lima|Brazil/Acre",
            "America/Lima|Etc/GMT+5",
            "America/Los_Angeles|America/Dawson",
            "America/Los_Angeles|America/Ensenada",
            "America/Los_Angeles|America/Santa_Isabel",
            "America/Los_Angeles|America/Tijuana",
            "America/Los_Angeles|America/Vancouver",
            "America/Los_Angeles|America/Whitehorse",
            "America/Los_Angeles|Canada/Pacific",
            "America/Los_Angeles|Canada/Yukon",
            "America/Los_Angeles|Mexico/BajaNorte",
            "America/Los_Angeles|PST8PDT",
            "America/Los_Angeles|US/Pacific",
            "America/Los_Angeles|US/Pacific-New",
            "America/Managua|America/Belize",
            "America/Managua|America/Costa_Rica",
            "America/Managua|America/El_Salvador",
            "America/Managua|America/Guatemala",
            "America/Managua|America/Regina",
            "America/Managua|America/Swift_Current",
            "America/Managua|America/Tegucigalpa",
            "America/Managua|Canada/Saskatchewan",
            "America/Mexico_City|America/Bahia_Banderas",
            "America/Mexico_City|America/Merida",
            "America/Mexico_City|America/Monterrey",
            "America/Mexico_City|Mexico/General",
            "America/New_York|America/Detroit",
            "America/New_York|America/Fort_Wayne",
            "America/New_York|America/Indiana/Indianapolis",
            "America/New_York|America/Indiana/Marengo",
            "America/New_York|America/Indiana/Petersburg",
            "America/New_York|America/Indiana/Vevay",
            "America/New_York|America/Indiana/Vincennes",
            "America/New_York|America/Indiana/Winamac",
            "America/New_York|America/Indianapolis",
            "America/New_York|America/Iqaluit",
            "America/New_York|America/Kentucky/Louisville",
            "America/New_York|America/Kentucky/Monticello",
            "America/New_York|America/Louisville",
            "America/New_York|America/Montreal",
            "America/New_York|America/Nassau",
            "America/New_York|America/Nipigon",
            "America/New_York|America/Pangnirtung",
            "America/New_York|America/Thunder_Bay",
            "America/New_York|America/Toronto",
            "America/New_York|Canada/Eastern",
            "America/New_York|EST5EDT",
            "America/New_York|US/East-Indiana",
            "America/New_York|US/Eastern",
            "America/New_York|US/Michigan",
            "America/Noronha|Atlantic/South_Georgia",
            "America/Noronha|Brazil/DeNoronha",
            "America/Noronha|Etc/GMT+2",
            "America/Panama|America/Atikokan",
            "America/Panama|America/Cayman",
            "America/Panama|America/Coral_Harbour",
            "America/Panama|America/Jamaica",
            "America/Panama|EST",
            "America/Panama|Jamaica",
            "America/Phoenix|America/Creston",
            "America/Phoenix|America/Dawson_Creek",
            "America/Phoenix|America/Hermosillo",
            "America/Phoenix|MST",
            "America/Phoenix|US/Arizona",
            "America/Santiago|Chile/Continental",
            "America/Santo_Domingo|America/Anguilla",
            "America/Santo_Domingo|America/Antigua",
            "America/Santo_Domingo|America/Aruba",
            "America/Santo_Domingo|America/Barbados",
            "America/Santo_Domingo|America/Blanc-Sablon",
            "America/Santo_Domingo|America/Curacao",
            "America/Santo_Domingo|America/Dominica",
            "America/Santo_Domingo|America/Grenada",
            "America/Santo_Domingo|America/Guadeloupe",
            "America/Santo_Domingo|America/Kralendijk",
            "America/Santo_Domingo|America/Lower_Princes",
            "America/Santo_Domingo|America/Marigot",
            "America/Santo_Domingo|America/Martinique",
            "America/Santo_Domingo|America/Montserrat",
            "America/Santo_Domingo|America/Port_of_Spain",
            "America/Santo_Domingo|America/Puerto_Rico",
            "America/Santo_Domingo|America/St_Barthelemy",
            "America/Santo_Domingo|America/St_Kitts",
            "America/Santo_Domingo|America/St_Lucia",
            "America/Santo_Domingo|America/St_Thomas",
            "America/Santo_Domingo|America/St_Vincent",
            "America/Santo_Domingo|America/Tortola",
            "America/Santo_Domingo|America/Virgin",
            "America/Sao_Paulo|Brazil/East",
            "America/St_Johns|Canada/Newfoundland",
            "Antarctica/Palmer|America/Punta_Arenas",
            "Asia/Baghdad|Antarctica/Syowa",
            "Asia/Baghdad|Asia/Aden",
            "Asia/Baghdad|Asia/Bahrain",
            "Asia/Baghdad|Asia/Kuwait",
            "Asia/Baghdad|Asia/Qatar",
            "Asia/Baghdad|Asia/Riyadh",
            "Asia/Baghdad|Etc/GMT-3",
            "Asia/Baghdad|Europe/Kirov",
            "Asia/Baghdad|Europe/Minsk",
            "Asia/Bangkok|Antarctica/Davis",
            "Asia/Bangkok|Asia/Ho_Chi_Minh",
            "Asia/Bangkok|Asia/Krasnoyarsk",
            "Asia/Bangkok|Asia/Novokuznetsk",
            "Asia/Bangkok|Asia/Phnom_Penh",
            "Asia/Bangkok|Asia/Saigon",
            "Asia/Bangkok|Asia/Vientiane",
            "Asia/Bangkok|Etc/GMT-7",
            "Asia/Bangkok|Indian/Christmas",
            "Asia/Dhaka|Antarctica/Vostok",
            "Asia/Dhaka|Asia/Almaty",
            "Asia/Dhaka|Asia/Bishkek",
            "Asia/Dhaka|Asia/Dacca",
            "Asia/Dhaka|Asia/Kashgar",
            "Asia/Dhaka|Asia/Omsk",
            "Asia/Dhaka|Asia/Qostanay",
            "Asia/Dhaka|Asia/Thimbu",
            "Asia/Dhaka|Asia/Thimphu",
            "Asia/Dhaka|Asia/Urumqi",
            "Asia/Dhaka|Etc/GMT-6",
            "Asia/Dhaka|Indian/Chagos",
            "Asia/Dubai|Asia/Muscat",
            "Asia/Dubai|Asia/Tbilisi",
            "Asia/Dubai|Asia/Yerevan",
            "Asia/Dubai|Etc/GMT-4",
            "Asia/Dubai|Europe/Samara",
            "Asia/Dubai|Indian/Mahe",
            "Asia/Dubai|Indian/Mauritius",
            "Asia/Dubai|Indian/Reunion",
            "Asia/Gaza|Asia/Hebron",
            "Asia/Hong_Kong|Hongkong",
            "Asia/Jakarta|Asia/Pontianak",
            "Asia/Jerusalem|Asia/Tel_Aviv",
            "Asia/Jerusalem|Israel",
            "Asia/Kamchatka|Asia/Anadyr",
            "Asia/Kamchatka|Etc/GMT-12",
            "Asia/Kamchatka|Kwajalein",
            "Asia/Kamchatka|Pacific/Funafuti",
            "Asia/Kamchatka|Pacific/Kwajalein",
            "Asia/Kamchatka|Pacific/Majuro",
            "Asia/Kamchatka|Pacific/Nauru",
            "Asia/Kamchatka|Pacific/Tarawa",
            "Asia/Kamchatka|Pacific/Wake",
            "Asia/Kamchatka|Pacific/Wallis",
            "Asia/Kathmandu|Asia/Katmandu",
            "Asia/Kolkata|Asia/Calcutta",
            "Asia/Kuala_Lumpur|Asia/Brunei",
            "Asia/Kuala_Lumpur|Asia/Irkutsk",
            "Asia/Kuala_Lumpur|Asia/Kuching",
            "Asia/Kuala_Lumpur|Asia/Singapore",
            "Asia/Kuala_Lumpur|Etc/GMT-8",
            "Asia/Kuala_Lumpur|Singapore",
            "Asia/Makassar|Asia/Ujung_Pandang",
            "Asia/Rangoon|Asia/Yangon",
            "Asia/Rangoon|Indian/Cocos",
            "Asia/Seoul|ROK",
            "Asia/Shanghai|Asia/Chongqing",
            "Asia/Shanghai|Asia/Chungking",
            "Asia/Shanghai|Asia/Harbin",
            "Asia/Shanghai|Asia/Macao",
            "Asia/Shanghai|Asia/Macau",
            "Asia/Shanghai|Asia/Taipei",
            "Asia/Shanghai|PRC",
            "Asia/Shanghai|ROC",
            "Asia/Tashkent|Antarctica/Mawson",
            "Asia/Tashkent|Asia/Aqtau",
            "Asia/Tashkent|Asia/Aqtobe",
            "Asia/Tashkent|Asia/Ashgabat",
            "Asia/Tashkent|Asia/Ashkhabad",
            "Asia/Tashkent|Asia/Atyrau",
            "Asia/Tashkent|Asia/Dushanbe",
            "Asia/Tashkent|Asia/Oral",
            "Asia/Tashkent|Asia/Samarkand",
            "Asia/Tashkent|Asia/Yekaterinburg",
            "Asia/Tashkent|Etc/GMT-5",
            "Asia/Tashkent|Indian/Kerguelen",
            "Asia/Tashkent|Indian/Maldives",
            "Asia/Tehran|Iran",
            "Asia/Tokyo|Japan",
            "Asia/Ulaanbaatar|Asia/Choibalsan",
            "Asia/Ulaanbaatar|Asia/Ulan_Bator",
            "Asia/Vladivostok|Antarctica/DumontDUrville",
            "Asia/Vladivostok|Asia/Ust-Nera",
            "Asia/Vladivostok|Etc/GMT-10",
            "Asia/Vladivostok|Pacific/Chuuk",
            "Asia/Vladivostok|Pacific/Port_Moresby",
            "Asia/Vladivostok|Pacific/Truk",
            "Asia/Vladivostok|Pacific/Yap",
            "Asia/Yakutsk|Asia/Dili",
            "Asia/Yakutsk|Asia/Khandyga",
            "Asia/Yakutsk|Etc/GMT-9",
            "Asia/Yakutsk|Pacific/Palau",
            "Atlantic/Azores|America/Scoresbysund",
            "Atlantic/Cape_Verde|Etc/GMT+1",
            "Australia/Adelaide|Australia/Broken_Hill",
            "Australia/Adelaide|Australia/South",
            "Australia/Adelaide|Australia/Yancowinna",
            "Australia/Brisbane|Australia/Lindeman",
            "Australia/Brisbane|Australia/Queensland",
            "Australia/Darwin|Australia/North",
            "Australia/Lord_Howe|Australia/LHI",
            "Australia/Perth|Australia/West",
            "Australia/Sydney|Australia/ACT",
            "Australia/Sydney|Australia/Canberra",
            "Australia/Sydney|Australia/Currie",
            "Australia/Sydney|Australia/Hobart",
            "Australia/Sydney|Australia/Melbourne",
            "Australia/Sydney|Australia/NSW",
            "Australia/Sydney|Australia/Tasmania",
            "Australia/Sydney|Australia/Victoria",
            "Etc/UTC|Etc/UCT",
            "Etc/UTC|Etc/Universal",
            "Etc/UTC|Etc/Zulu",
            "Etc/UTC|UCT",
            "Etc/UTC|UTC",
            "Etc/UTC|Universal",
            "Etc/UTC|Zulu",
            "Europe/Athens|Asia/Nicosia",
            "Europe/Athens|EET",
            "Europe/Athens|Europe/Bucharest",
            "Europe/Athens|Europe/Helsinki",
            "Europe/Athens|Europe/Kiev",
            "Europe/Athens|Europe/Mariehamn",
            "Europe/Athens|Europe/Nicosia",
            "Europe/Athens|Europe/Riga",
            "Europe/Athens|Europe/Sofia",
            "Europe/Athens|Europe/Tallinn",
            "Europe/Athens|Europe/Uzhgorod",
            "Europe/Athens|Europe/Vilnius",
            "Europe/Athens|Europe/Zaporozhye",
            "Europe/Chisinau|Europe/Tiraspol",
            "Europe/Dublin|Eire",
            "Europe/Istanbul|Asia/Istanbul",
            "Europe/Istanbul|Turkey",
            "Europe/Lisbon|Atlantic/Canary",
            "Europe/Lisbon|Atlantic/Faeroe",
            "Europe/Lisbon|Atlantic/Faroe",
            "Europe/Lisbon|Atlantic/Madeira",
            "Europe/Lisbon|Portugal",
            "Europe/Lisbon|WET",
            "Europe/London|Europe/Belfast",
            "Europe/London|Europe/Guernsey",
            "Europe/London|Europe/Isle_of_Man",
            "Europe/London|Europe/Jersey",
            "Europe/London|GB",
            "Europe/London|GB-Eire",
            "Europe/Moscow|Europe/Simferopol",
            "Europe/Moscow|W-SU",
            "Europe/Paris|Africa/Ceuta",
            "Europe/Paris|Arctic/Longyearbyen",
            "Europe/Paris|Atlantic/Jan_Mayen",
            "Europe/Paris|CET",
            "Europe/Paris|Europe/Amsterdam",
            "Europe/Paris|Europe/Andorra",
            "Europe/Paris|Europe/Belgrade",
            "Europe/Paris|Europe/Berlin",
            "Europe/Paris|Europe/Bratislava",
            "Europe/Paris|Europe/Brussels",
            "Europe/Paris|Europe/Budapest",
            "Europe/Paris|Europe/Busingen",
            "Europe/Paris|Europe/Copenhagen",
            "Europe/Paris|Europe/Gibraltar",
            "Europe/Paris|Europe/Ljubljana",
            "Europe/Paris|Europe/Luxembourg",
            "Europe/Paris|Europe/Madrid",
            "Europe/Paris|Europe/Malta",
            "Europe/Paris|Europe/Monaco",
            "Europe/Paris|Europe/Oslo",
            "Europe/Paris|Europe/Podgorica",
            "Europe/Paris|Europe/Prague",
            "Europe/Paris|Europe/Rome",
            "Europe/Paris|Europe/San_Marino",
            "Europe/Paris|Europe/Sarajevo",
            "Europe/Paris|Europe/Skopje",
            "Europe/Paris|Europe/Stockholm",
            "Europe/Paris|Europe/Tirane",
            "Europe/Paris|Europe/Vaduz",
            "Europe/Paris|Europe/Vatican",
            "Europe/Paris|Europe/Vienna",
            "Europe/Paris|Europe/Warsaw",
            "Europe/Paris|Europe/Zagreb",
            "Europe/Paris|Europe/Zurich",
            "Europe/Paris|Poland",
            "Europe/Ulyanovsk|Europe/Astrakhan",
            "Pacific/Auckland|Antarctica/McMurdo",
            "Pacific/Auckland|Antarctica/South_Pole",
            "Pacific/Auckland|NZ",
            "Pacific/Bougainville|Antarctica/Macquarie",
            "Pacific/Bougainville|Asia/Srednekolymsk",
            "Pacific/Bougainville|Etc/GMT-11",
            "Pacific/Bougainville|Pacific/Efate",
            "Pacific/Bougainville|Pacific/Guadalcanal",
            "Pacific/Bougainville|Pacific/Kosrae",
            "Pacific/Bougainville|Pacific/Noumea",
            "Pacific/Bougainville|Pacific/Pohnpei",
            "Pacific/Bougainville|Pacific/Ponape",
            "Pacific/Chatham|NZ-CHAT",
            "Pacific/Easter|Chile/EasterIsland",
            "Pacific/Fakaofo|Etc/GMT-13",
            "Pacific/Fakaofo|Pacific/Enderbury",
            "Pacific/Galapagos|Etc/GMT+6",
            "Pacific/Gambier|Etc/GMT+9",
            "Pacific/Guam|Pacific/Saipan",
            "Pacific/Honolulu|HST",
            "Pacific/Honolulu|Pacific/Johnston",
            "Pacific/Honolulu|US/Hawaii",
            "Pacific/Kiritimati|Etc/GMT-14",
            "Pacific/Niue|Etc/GMT+11",
            "Pacific/Pago_Pago|Pacific/Midway",
            "Pacific/Pago_Pago|Pacific/Samoa",
            "Pacific/Pago_Pago|US/Samoa",
            "Pacific/Pitcairn|Etc/GMT+8",
            "Pacific/Tahiti|Etc/GMT+10",
            "Pacific/Tahiti|Pacific/Rarotonga"
        ],
        "countries": [
            "AD|Europe/Andorra",
            "AE|Asia/Dubai",
            "AF|Asia/Kabul",
            "AG|America/Port_of_Spain America/Antigua",
            "AI|America/Port_of_Spain America/Anguilla",
            "AL|Europe/Tirane",
            "AM|Asia/Yerevan",
            "AO|Africa/Lagos Africa/Luanda",
            "AQ|Antarctica/Casey Antarctica/Davis Antarctica/DumontDUrville Antarctica/Mawson Antarctica/Palmer Antarctica/Rothera Antarctica/Syowa Antarctica/Troll Antarctica/Vostok Pacific/Auckland Antarctica/McMurdo",
            "AR|America/Argentina/Buenos_Aires America/Argentina/Cordoba America/Argentina/Salta America/Argentina/Jujuy America/Argentina/Tucuman America/Argentina/Catamarca America/Argentina/La_Rioja America/Argentina/San_Juan America/Argentina/Mendoza America/Argentina/San_Luis America/Argentina/Rio_Gallegos America/Argentina/Ushuaia",
            "AS|Pacific/Pago_Pago",
            "AT|Europe/Vienna",
            "AU|Australia/Lord_Howe Antarctica/Macquarie Australia/Hobart Australia/Currie Australia/Melbourne Australia/Sydney Australia/Broken_Hill Australia/Brisbane Australia/Lindeman Australia/Adelaide Australia/Darwin Australia/Perth Australia/Eucla",
            "AW|America/Curacao America/Aruba",
            "AX|Europe/Helsinki Europe/Mariehamn",
            "AZ|Asia/Baku",
            "BA|Europe/Belgrade Europe/Sarajevo",
            "BB|America/Barbados",
            "BD|Asia/Dhaka",
            "BE|Europe/Brussels",
            "BF|Africa/Abidjan Africa/Ouagadougou",
            "BG|Europe/Sofia",
            "BH|Asia/Qatar Asia/Bahrain",
            "BI|Africa/Maputo Africa/Bujumbura",
            "BJ|Africa/Lagos Africa/Porto-Novo",
            "BL|America/Port_of_Spain America/St_Barthelemy",
            "BM|Atlantic/Bermuda",
            "BN|Asia/Brunei",
            "BO|America/La_Paz",
            "BQ|America/Curacao America/Kralendijk",
            "BR|America/Noronha America/Belem America/Fortaleza America/Recife America/Araguaina America/Maceio America/Bahia America/Sao_Paulo America/Campo_Grande America/Cuiaba America/Santarem America/Porto_Velho America/Boa_Vista America/Manaus America/Eirunepe America/Rio_Branco",
            "BS|America/Nassau",
            "BT|Asia/Thimphu",
            "BW|Africa/Maputo Africa/Gaborone",
            "BY|Europe/Minsk",
            "BZ|America/Belize",
            "CA|America/St_Johns America/Halifax America/Glace_Bay America/Moncton America/Goose_Bay America/Blanc-Sablon America/Toronto America/Nipigon America/Thunder_Bay America/Iqaluit America/Pangnirtung America/Atikokan America/Winnipeg America/Rainy_River America/Resolute America/Rankin_Inlet America/Regina America/Swift_Current America/Edmonton America/Cambridge_Bay America/Yellowknife America/Inuvik America/Creston America/Dawson_Creek America/Fort_Nelson America/Vancouver America/Whitehorse America/Dawson",
            "CC|Indian/Cocos",
            "CD|Africa/Maputo Africa/Lagos Africa/Kinshasa Africa/Lubumbashi",
            "CF|Africa/Lagos Africa/Bangui",
            "CG|Africa/Lagos Africa/Brazzaville",
            "CH|Europe/Zurich",
            "CI|Africa/Abidjan",
            "CK|Pacific/Rarotonga",
            "CL|America/Santiago America/Punta_Arenas Pacific/Easter",
            "CM|Africa/Lagos Africa/Douala",
            "CN|Asia/Shanghai Asia/Urumqi",
            "CO|America/Bogota",
            "CR|America/Costa_Rica",
            "CU|America/Havana",
            "CV|Atlantic/Cape_Verde",
            "CW|America/Curacao",
            "CX|Indian/Christmas",
            "CY|Asia/Nicosia Asia/Famagusta",
            "CZ|Europe/Prague",
            "DE|Europe/Zurich Europe/Berlin Europe/Busingen",
            "DJ|Africa/Nairobi Africa/Djibouti",
            "DK|Europe/Copenhagen",
            "DM|America/Port_of_Spain America/Dominica",
            "DO|America/Santo_Domingo",
            "DZ|Africa/Algiers",
            "EC|America/Guayaquil Pacific/Galapagos",
            "EE|Europe/Tallinn",
            "EG|Africa/Cairo",
            "EH|Africa/El_Aaiun",
            "ER|Africa/Nairobi Africa/Asmara",
            "ES|Europe/Madrid Africa/Ceuta Atlantic/Canary",
            "ET|Africa/Nairobi Africa/Addis_Ababa",
            "FI|Europe/Helsinki",
            "FJ|Pacific/Fiji",
            "FK|Atlantic/Stanley",
            "FM|Pacific/Chuuk Pacific/Pohnpei Pacific/Kosrae",
            "FO|Atlantic/Faroe",
            "FR|Europe/Paris",
            "GA|Africa/Lagos Africa/Libreville",
            "GB|Europe/London",
            "GD|America/Port_of_Spain America/Grenada",
            "GE|Asia/Tbilisi",
            "GF|America/Cayenne",
            "GG|Europe/London Europe/Guernsey",
            "GH|Africa/Accra",
            "GI|Europe/Gibraltar",
            "GL|America/Godthab America/Danmarkshavn America/Scoresbysund America/Thule",
            "GM|Africa/Abidjan Africa/Banjul",
            "GN|Africa/Abidjan Africa/Conakry",
            "GP|America/Port_of_Spain America/Guadeloupe",
            "GQ|Africa/Lagos Africa/Malabo",
            "GR|Europe/Athens",
            "GS|Atlantic/South_Georgia",
            "GT|America/Guatemala",
            "GU|Pacific/Guam",
            "GW|Africa/Bissau",
            "GY|America/Guyana",
            "HK|Asia/Hong_Kong",
            "HN|America/Tegucigalpa",
            "HR|Europe/Belgrade Europe/Zagreb",
            "HT|America/Port-au-Prince",
            "HU|Europe/Budapest",
            "ID|Asia/Jakarta Asia/Pontianak Asia/Makassar Asia/Jayapura",
            "IE|Europe/Dublin",
            "IL|Asia/Jerusalem",
            "IM|Europe/London Europe/Isle_of_Man",
            "IN|Asia/Kolkata",
            "IO|Indian/Chagos",
            "IQ|Asia/Baghdad",
            "IR|Asia/Tehran",
            "IS|Atlantic/Reykjavik",
            "IT|Europe/Rome",
            "JE|Europe/London Europe/Jersey",
            "JM|America/Jamaica",
            "JO|Asia/Amman",
            "JP|Asia/Tokyo",
            "KE|Africa/Nairobi",
            "KG|Asia/Bishkek",
            "KH|Asia/Bangkok Asia/Phnom_Penh",
            "KI|Pacific/Tarawa Pacific/Enderbury Pacific/Kiritimati",
            "KM|Africa/Nairobi Indian/Comoro",
            "KN|America/Port_of_Spain America/St_Kitts",
            "KP|Asia/Pyongyang",
            "KR|Asia/Seoul",
            "KW|Asia/Riyadh Asia/Kuwait",
            "KY|America/Panama America/Cayman",
            "KZ|Asia/Almaty Asia/Qyzylorda Asia/Qostanay Asia/Aqtobe Asia/Aqtau Asia/Atyrau Asia/Oral",
            "LA|Asia/Bangkok Asia/Vientiane",
            "LB|Asia/Beirut",
            "LC|America/Port_of_Spain America/St_Lucia",
            "LI|Europe/Zurich Europe/Vaduz",
            "LK|Asia/Colombo",
            "LR|Africa/Monrovia",
            "LS|Africa/Johannesburg Africa/Maseru",
            "LT|Europe/Vilnius",
            "LU|Europe/Luxembourg",
            "LV|Europe/Riga",
            "LY|Africa/Tripoli",
            "MA|Africa/Casablanca",
            "MC|Europe/Monaco",
            "MD|Europe/Chisinau",
            "ME|Europe/Belgrade Europe/Podgorica",
            "MF|America/Port_of_Spain America/Marigot",
            "MG|Africa/Nairobi Indian/Antananarivo",
            "MH|Pacific/Majuro Pacific/Kwajalein",
            "MK|Europe/Belgrade Europe/Skopje",
            "ML|Africa/Abidjan Africa/Bamako",
            "MM|Asia/Yangon",
            "MN|Asia/Ulaanbaatar Asia/Hovd Asia/Choibalsan",
            "MO|Asia/Macau",
            "MP|Pacific/Guam Pacific/Saipan",
            "MQ|America/Martinique",
            "MR|Africa/Abidjan Africa/Nouakchott",
            "MS|America/Port_of_Spain America/Montserrat",
            "MT|Europe/Malta",
            "MU|Indian/Mauritius",
            "MV|Indian/Maldives",
            "MW|Africa/Maputo Africa/Blantyre",
            "MX|America/Mexico_City America/Cancun America/Merida America/Monterrey America/Matamoros America/Mazatlan America/Chihuahua America/Ojinaga America/Hermosillo America/Tijuana America/Bahia_Banderas",
            "MY|Asia/Kuala_Lumpur Asia/Kuching",
            "MZ|Africa/Maputo",
            "NA|Africa/Windhoek",
            "NC|Pacific/Noumea",
            "NE|Africa/Lagos Africa/Niamey",
            "NF|Pacific/Norfolk",
            "NG|Africa/Lagos",
            "NI|America/Managua",
            "NL|Europe/Amsterdam",
            "NO|Europe/Oslo",
            "NP|Asia/Kathmandu",
            "NR|Pacific/Nauru",
            "NU|Pacific/Niue",
            "NZ|Pacific/Auckland Pacific/Chatham",
            "OM|Asia/Dubai Asia/Muscat",
            "PA|America/Panama",
            "PE|America/Lima",
            "PF|Pacific/Tahiti Pacific/Marquesas Pacific/Gambier",
            "PG|Pacific/Port_Moresby Pacific/Bougainville",
            "PH|Asia/Manila",
            "PK|Asia/Karachi",
            "PL|Europe/Warsaw",
            "PM|America/Miquelon",
            "PN|Pacific/Pitcairn",
            "PR|America/Puerto_Rico",
            "PS|Asia/Gaza Asia/Hebron",
            "PT|Europe/Lisbon Atlantic/Madeira Atlantic/Azores",
            "PW|Pacific/Palau",
            "PY|America/Asuncion",
            "QA|Asia/Qatar",
            "RE|Indian/Reunion",
            "RO|Europe/Bucharest",
            "RS|Europe/Belgrade",
            "RU|Europe/Kaliningrad Europe/Moscow Europe/Simferopol Europe/Kirov Europe/Astrakhan Europe/Volgograd Europe/Saratov Europe/Ulyanovsk Europe/Samara Asia/Yekaterinburg Asia/Omsk Asia/Novosibirsk Asia/Barnaul Asia/Tomsk Asia/Novokuznetsk Asia/Krasnoyarsk Asia/Irkutsk Asia/Chita Asia/Yakutsk Asia/Khandyga Asia/Vladivostok Asia/Ust-Nera Asia/Magadan Asia/Sakhalin Asia/Srednekolymsk Asia/Kamchatka Asia/Anadyr",
            "RW|Africa/Maputo Africa/Kigali",
            "SA|Asia/Riyadh",
            "SB|Pacific/Guadalcanal",
            "SC|Indian/Mahe",
            "SD|Africa/Khartoum",
            "SE|Europe/Stockholm",
            "SG|Asia/Singapore",
            "SH|Africa/Abidjan Atlantic/St_Helena",
            "SI|Europe/Belgrade Europe/Ljubljana",
            "SJ|Europe/Oslo Arctic/Longyearbyen",
            "SK|Europe/Prague Europe/Bratislava",
            "SL|Africa/Abidjan Africa/Freetown",
            "SM|Europe/Rome Europe/San_Marino",
            "SN|Africa/Abidjan Africa/Dakar",
            "SO|Africa/Nairobi Africa/Mogadishu",
            "SR|America/Paramaribo",
            "SS|Africa/Juba",
            "ST|Africa/Sao_Tome",
            "SV|America/El_Salvador",
            "SX|America/Curacao America/Lower_Princes",
            "SY|Asia/Damascus",
            "SZ|Africa/Johannesburg Africa/Mbabane",
            "TC|America/Grand_Turk",
            "TD|Africa/Ndjamena",
            "TF|Indian/Reunion Indian/Kerguelen",
            "TG|Africa/Abidjan Africa/Lome",
            "TH|Asia/Bangkok",
            "TJ|Asia/Dushanbe",
            "TK|Pacific/Fakaofo",
            "TL|Asia/Dili",
            "TM|Asia/Ashgabat",
            "TN|Africa/Tunis",
            "TO|Pacific/Tongatapu",
            "TR|Europe/Istanbul",
            "TT|America/Port_of_Spain",
            "TV|Pacific/Funafuti",
            "TW|Asia/Taipei",
            "TZ|Africa/Nairobi Africa/Dar_es_Salaam",
            "UA|Europe/Simferopol Europe/Kiev Europe/Uzhgorod Europe/Zaporozhye",
            "UG|Africa/Nairobi Africa/Kampala",
            "UM|Pacific/Pago_Pago Pacific/Wake Pacific/Honolulu Pacific/Midway",
            "US|America/New_York America/Detroit America/Kentucky/Louisville America/Kentucky/Monticello America/Indiana/Indianapolis America/Indiana/Vincennes America/Indiana/Winamac America/Indiana/Marengo America/Indiana/Petersburg America/Indiana/Vevay America/Chicago America/Indiana/Tell_City America/Indiana/Knox America/Menominee America/North_Dakota/Center America/North_Dakota/New_Salem America/North_Dakota/Beulah America/Denver America/Boise America/Phoenix America/Los_Angeles America/Anchorage America/Juneau America/Sitka America/Metlakatla America/Yakutat America/Nome America/Adak Pacific/Honolulu",
            "UY|America/Montevideo",
            "UZ|Asia/Samarkand Asia/Tashkent",
            "VA|Europe/Rome Europe/Vatican",
            "VC|America/Port_of_Spain America/St_Vincent",
            "VE|America/Caracas",
            "VG|America/Port_of_Spain America/Tortola",
            "VI|America/Port_of_Spain America/St_Thomas",
            "VN|Asia/Bangkok Asia/Ho_Chi_Minh",
            "VU|Pacific/Efate",
            "WF|Pacific/Wallis",
            "WS|Pacific/Apia",
            "YE|Asia/Riyadh Asia/Aden",
            "YT|Africa/Nairobi Indian/Mayotte",
            "ZA|Africa/Johannesburg",
            "ZM|Africa/Maputo Africa/Lusaka",
            "ZW|Africa/Maputo Africa/Harare"
        ]
    });


    return moment;
}));
