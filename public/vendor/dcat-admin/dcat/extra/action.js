!function(e){var t={};function n(o){if(t[o])return t[o].exports;var r=t[o]={i:o,l:!1,exports:{}};return e[o].call(r.exports,r,r.exports,n),r.l=!0,r.exports}n.m=e,n.c=t,n.d=function(e,t,o){n.o(e,t)||Object.defineProperty(e,t,{enumerable:!0,get:o})},n.r=function(e){"undefined"!=typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(e,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(e,"__esModule",{value:!0})},n.t=function(e,t){if(1&t&&(e=n(e)),8&t)return e;if(4&t&&"object"==typeof e&&e&&e.__esModule)return e;var o=Object.create(null);if(n.r(o),Object.defineProperty(o,"default",{enumerable:!0,value:e}),2&t&&"string"!=typeof e)for(var r in e)n.d(o,r,function(t){return e[t]}.bind(null,r));return o},n.n=function(e){var t=e&&e.__esModule?function(){return e.default}:function(){return e};return n.d(t,"a",t),t},n.o=function(e,t){return Object.prototype.hasOwnProperty.call(e,t)},n.p="/",n(n.s=7)}({7:function(e,t,n){e.exports=n(8)},8:function(e,t){function n(e){return(n="function"==typeof Symbol&&"symbol"==typeof Symbol.iterator?function(e){return typeof e}:function(e){return e&&"function"==typeof Symbol&&e.constructor===Symbol&&e!==Symbol.prototype?"symbol":typeof e})(e)}function o(e,t){for(var n=0;n<t.length;n++){var o=t[n];o.enumerable=o.enumerable||!1,o.configurable=!0,"value"in o&&(o.writable=!0),Object.defineProperty(e,o.key,o)}}!function(e){var t=function(){function t(e){!function(e,t){if(!(e instanceof t))throw new TypeError("Cannot call a class as a function")}(this,t),this.options=$.extend({selector:null,event:"click",method:"POST",key:null,url:null,data:{},confirm:null,calledClass:null,before:function(e,t){},html:function(e,t,n){e.html(t)},success:function(e,t){},error:function(e,t){}},e),this.init()}var r,i,u;return r=t,(i=[{key:"init",value:function(){var t=this,n=t.options;$(n.selector).off(n.event).on(n.event,(function(o){var r=$(this).data(),i=$(this);if(!(i.attr("loading")>0)&&!1!==n.before(r,i,t)){var u=n.confirm;u?e.confirm(u[0],u[1],c):c()}function c(){i.attr("loading",1),Object.assign(r,n.data),t.promise(i,r).then(t.resolve()).catch(t.reject())}}))}},{key:"resolve",value:function(){var t=this.options;return function(n){var o=n[0],r=n[1];!1!==t.success(r,o)&&e.handleJsonResponse(o,{html:t.html,target:r})}}},{key:"reject",value:function(){var t=this.options;return function(o){var r=o[0],i=o[1];!1!==t.success(i,r)&&(r&&"object"===n(r.responseJSON)&&e.error(r.responseJSON.message),console.error(o))}}},{key:"promise",value:function(t,n){var o=this.options;return new Promise((function(r,i){Object.assign(n,{_action:o.calledClass,_key:o.key,_token:e.token}),e.NP.start(),$.ajax({method:o.method,url:o.url,data:n,success:function(n){t.attr("loading",0),e.NP.done(),r([n,t])},error:function(n){t.attr("loading",0),e.NP.done(),i([n,t])}})}))}}])&&o(r.prototype,i),u&&o(r,u),t}();e.Action=function(e){return new t(e)}}(Dcat)}});
//# sourceMappingURL=action.js.map