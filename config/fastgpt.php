<?php
return [
    'chat'            => [
        'simple'   => [
            'parentId' => '',
            'avatar'   => 'core/app/type/simpleFill',
            'name'     => '徐-应用',
            'type'     => 'simple',
            'modules'  => [
                [
                    'nodeId'       => 'userGuide',
                    'name'         => 'common:core.module.template.system_config',
                    'intro'        => 'common:core.module.template.config_params',
                    'avatar'       => 'core/workflow/template/systemConfig',
                    'flowNodeType' => 'userGuide',
                    'position'     => [
                        'x' => 531.2422736065552,
                        'y' => -486.7611729549753,
                    ],
                    'version'      => '481',
                    'inputs'       => [
                        [
                            'key'            => 'welcomeText',
                            'renderTypeList' => ['hidden'],
                            'valueType'      => 'string',
                            'label'          => 'core.app.Welcome Text',
                            'value'          => '',
                        ],
                        [
                            'key'            => 'variables',
                            'renderTypeList' => ['hidden'],
                            'valueType'      => 'any',
                            'label'          => 'core.app.Chat Variable',
                            'value'          => [],
                        ],
                        [
                            'key'            => 'questionGuide',
                            'valueType'      => 'object',
                            'renderTypeList' => ['hidden'],
                            'label'          => 'core.app.Question Guide',
                            'value'          => [
                                'open' => false,
                            ],
                        ],
                        [
                            'key'            => 'tts',
                            'renderTypeList' => ['hidden'],
                            'valueType'      => 'any',
                            'label'          => '',
                            'value'          => [
                                'type' => 'web',
                            ],
                        ],
                        [
                            'key'            => 'whisper',
                            'renderTypeList' => ['hidden'],
                            'valueType'      => 'any',
                            'label'          => '',
                            'value'          => [
                                'open'            => false,
                                'autoSend'        => false,
                                'autoTTSResponse' => false,
                            ],
                        ],
                        [
                            'key'            => 'scheduleTrigger',
                            'renderTypeList' => ['hidden'],
                            'valueType'      => 'any',
                            'label'          => '',
                            'value'          => null,
                        ],
                    ],
                    'outputs'      => [],
                ],
                [
                    'nodeId'       => '448745',
                    'name'         => 'common:core.module.template.work_start',
                    'intro'        => '',
                    'avatar'       => 'core/workflow/template/workflowStart',
                    'flowNodeType' => 'workflowStart',
                    'position'     => [
                        'x' => 558.4082376415505,
                        'y' => 123.72387429194112,
                    ],
                    'version'      => '481',
                    'inputs'       => [
                        [
                            'key'             => 'userChatInput',
                            'renderTypeList'  => ['reference', 'textarea'],
                            'valueType'       => 'string',
                            'label'           => 'common:core.module.input.label.user question',
                            'required'        => true,
                            'toolDescription' => 'common:core.module.input.label.user question',
                        ],
                    ],
                    'outputs'      => [
                        [
                            'id'        => 'userChatInput',
                            'key'       => 'userChatInput',
                            'label'     => 'core.module.input.label.user question',
                            'valueType' => 'string',
                            'type'      => 'static',
                        ],
                    ],
                ],
                [
                    'nodeId'       => 'loOvhld2ZTKa',
                    'name'         => 'common:core.module.template.ai_chat',
                    'intro'        => 'common:core.module.template.ai_chat_intro',
                    'avatar'       => 'core/workflow/template/aiChat',
                    'flowNodeType' => 'chatNode',
                    'showStatus'   => true,
                    'position'     => [
                        'x' => 1097.7317280958762,
                        'y' => -244.16014496351386,
                    ],
                    'version'      => '481',
                    'inputs'       => [
                        [
                            'key'            => 'model',
                            'renderTypeList' => ['settingLLMModel', 'reference'],
                            'label'          => 'core.module.input.label.aiModel',
                            'valueType'      => 'string',
                            'value'          => 'gpt-4o-mini',
                        ],
                        [
                            'key'            => 'temperature',
                            'renderTypeList' => ['hidden'],
                            'label'          => '',
                            'valueType'      => 'number',
                            'min'            => 0,
                            'max'            => 10,
                            'step'           => 1,
                        ],
                        [
                            'key'            => 'maxToken',
                            'renderTypeList' => ['hidden'],
                            'label'          => '',
                            'valueType'      => 'number',
                            'min'            => 100,
                            'max'            => 4000,
                            'step'           => 50,
                        ],
                        [
                            'key'            => 'isResponseAnswerText',
                            'renderTypeList' => ['hidden'],
                            'label'          => '',
                            'value'          => true,
                            'valueType'      => 'boolean',
                        ],
                        [
                            'key'            => 'quoteTemplate',
                            'renderTypeList' => ['hidden'],
                            'label'          => '',
                            'valueType'      => 'string',
                        ],
                        [
                            'key'            => 'quotePrompt',
                            'renderTypeList' => ['hidden'],
                            'label'          => '',
                            'valueType'      => 'string',
                        ],
                        [
                            'key'            => 'systemPrompt',
                            'renderTypeList' => ['textarea', 'reference'],
                            'max'            => 3000,
                            'valueType'      => 'string',
                            'label'          => 'core.ai.Prompt',
                            'description'    => 'core.app.tip.systemPromptTip',
                            'placeholder'    => 'core.app.tip.chatNodeSystemPromptTip',
                            'value'          => '',
                        ],
                        [
                            'key'            => 'history',
                            'renderTypeList' => ['numberInput', 'reference'],
                            'valueType'      => 'chatHistory',
                            'label'          => 'core.module.input.label.chat history',
                            'required'       => true,
                            'min'            => 0,
                            'max'            => 30,
                            'value'          => 6,
                        ],
                        [
                            'key'             => 'userChatInput',
                            'renderTypeList'  => ['reference', 'textarea'],
                            'valueType'       => 'string',
                            'label'           => 'common:core.module.input.label.user question',
                            'required'        => true,
                            'toolDescription' => 'common:core.module.input.label.user question',
                            'value'           => ['448745', 'userChatInput'],
                        ],
                        [
                            'key'            => 'quoteQA',
                            'renderTypeList' => ['settingDatasetQuotePrompt'],
                            'label'          => '',
                            'debugLabel'     => 'common:core.module.Dataset quote.label',
                            'description'    => '',
                            'valueType'      => 'datasetQuote',
                        ],
                        [
                            'key'            => 'aiChatReasoning',
                            'renderTypeList' => ['hidden'],
                            'label'          => '',
                            'valueType'      => 'boolean',
                            'value'          => true,
                        ],
                    ],
                    'outputs'      => [
                        [
                            'id'          => 'history',
                            'key'         => 'history',
                            'label'       => 'core.module.output.label.New context',
                            'description' => 'core.module.output.description.New context',
                            'valueType'   => 'chatHistory',
                            'type'        => 'static',
                        ],
                        [
                            'id'          => 'answerText',
                            'key'         => 'answerText',
                            'label'       => 'core.module.output.label.Ai response content',
                            'description' => 'core.module.output.description.Ai response content',
                            'valueType'   => 'string',
                            'type'        => 'static',
                        ],
                    ],
                ],
            ],
            'edges'    => [
                [
                    'source'       => '448745',
                    'target'       => 'loOvhld2ZTKa',
                    'sourceHandle' => '448745-source-right',
                    'targetHandle' => 'loOvhld2ZTKa-target-left',
                ],
            ],
            //            'chatConfig' => [],
        ],
        'pushData' => [
            'nodes'       => [
                [
                    'nodeId'       => 'userGuide',
                    'name'         => '系统配置',
                    'intro'        => '',
                    'flowNodeType' => 'userGuide',
                    'position'     => [
                        'x' => 531.2422736065552,
                        'y' => -486.7611729549753,
                    ],
                    'version'      => '481',
                    'inputs'       => [],
                    'outputs'      => [],
                ],
                [
                    'nodeId'       => 'workflowStartNodeId',
                    'name'         => '流程开始',
                    'intro'        => '',
                    'avatar'       => 'core/workflow/template/workflowStart',
                    'flowNodeType' => 'workflowStart',
                    'position'     => [
                        'x' => 558.4082376415505,
                        'y' => 123.72387429194112,
                    ],
                    'version'      => '481',
                    'inputs'       => [
                        [
                            'key'             => 'userChatInput',
                            'renderTypeList'  => [
                                'reference',
                                'textarea',
                            ],
                            'valueType'       => 'string',
                            'label'           => 'workflow:user_question',
                            'toolDescription' => 'workflow:user_question',
                            'required'        => true,
                        ],
                    ],
                    'outputs'      => [
                        [
                            'id'        => 'userChatInput',
                            'key'       => 'userChatInput',
                            'label'     => 'common:core.module.input.label.user question',
                            'type'      => 'static',
                            'valueType' => 'string',
                        ],
                        [
                            'id'          => 'userFiles',
                            'key'         => 'userFiles',
                            'label'       => 'app:workflow.user_file_input',
                            'description' => 'app:workflow.user_file_input_desc',
                            'type'        => 'static',
                            'valueType'   => 'arrayString',
                        ],
                    ],
                ],
                [
                    'nodeId'       => '7BdojPlukIQw',
                    'name'         => 'AI 对话',
                    'intro'        => 'AI 大模型对话',
                    'avatar'       => 'core/workflow/template/aiChat',
                    'flowNodeType' => 'chatNode',
                    'showStatus'   => true,
                    'position'     => [
                        'x' => 1106.3238387960757,
                        'y' => -350.6030674683474,
                    ],
                    'version'      => '490',
                    'inputs'       => [
                        [
                            'key'            => 'model',
                            'renderTypeList' => [
                                'settingLLMModel',
                                'reference',
                            ],
                            'label'          => '',
                            'valueType'      => 'string',
                            'value'          => 'qwen-turbo',
                        ],
                        [
                            'key'            => 'temperature',
                            'renderTypeList' => [
                                'hidden',
                            ],
                            'label'          => '',
                            'valueType'      => 'number',
                            'min'            => 0,
                            'max'            => 10,
                            'step'           => 1,
                        ],
                        [
                            'key'            => 'maxToken',
                            'renderTypeList' => [
                                'hidden',
                            ],
                            'label'          => '',
                            'valueType'      => 'number',
                            'min'            => 100,
                            'max'            => 4000,
                            'step'           => 50,
                        ],
                        [
                            'key'            => 'isResponseAnswerText',
                            'renderTypeList' => [
                                'hidden',
                            ],
                            'label'          => '',
                            'value'          => true,
                            'valueType'      => 'boolean',
                        ],
                        [
                            'key'            => 'aiChatQuoteRole',
                            'renderTypeList' => [
                                'hidden',
                            ],
                            'label'          => '',
                            'valueType'      => 'string',
                            'value'          => 'system',
                        ],
                        [
                            'key'            => 'quoteTemplate',
                            'renderTypeList' => [
                                'hidden',
                            ],
                            'label'          => '',
                            'valueType'      => 'string',
                        ],
                        [
                            'key'            => 'quotePrompt',
                            'renderTypeList' => [
                                'hidden',
                            ],
                            'label'          => '',
                            'valueType'      => 'string',
                        ],
                        [
                            'key'            => 'systemPrompt',
                            'renderTypeList' => [
                                'textarea',
                                'reference',
                            ],
                            'max'            => 3000,
                            'valueType'      => 'string',
                            'label'          => 'core.ai.Prompt',
                            'description'    => 'core.app.tip.systemPromptTip',
                            'placeholder'    => 'core.app.tip.chatNodeSystemPromptTip',
                            'value'          => 'SYSTEMPROMPT_TEXT',
                        ],
                        [
                            'key'            => 'history',
                            'renderTypeList' => [
                                'numberInput',
                                'reference',
                            ],
                            'valueType'      => 'chatHistory',
                            'label'          => 'core.module.input.label.chat history',
                            'required'       => true,
                            'min'            => 0,
                            'max'            => 30,
                            'value'          => 6,
                        ],
                        [
                            'key'             => 'userChatInput',
                            'renderTypeList'  => [
                                'reference',
                                'textarea',
                            ],
                            'valueType'       => 'string',
                            'label'           => 'common:core.module.input.label.user question',
                            'required'        => true,
                            'toolDescription' => 'common:core.module.input.label.user question',
                            'value'           => [
                                'workflowStartNodeId',
                                'userChatInput',
                            ],
                        ],
                        [
                            'key'            => 'quoteQA',
                            'renderTypeList' => [
                                'settingDatasetQuotePrompt',
                            ],
                            'label'          => '',
                            'debugLabel'     => 'common:core.module.Dataset quote.label',
                            'description'    => '',
                            'valueType'      => 'datasetQuote',
                            'value'          => [
                                'iKBoX2vIzETU',
                                'quoteQA',
                            ],
                        ],
                        [
                            'key'            => 'fileUrlList',
                            'renderTypeList' => [
                                'reference',
                                'input',
                            ],
                            'label'          => 'app:file_quote_link',
                            'debugLabel'     => 'app:file_quote_link',
                            'valueType'      => 'arrayString',
                            'value'          => [
                                [
                                    'workflowStartNodeId',
                                    'userFiles',
                                ],
                            ],
                        ],
                        [
                            'key'            => 'aiChatVision',
                            'renderTypeList' => [
                                'hidden',
                            ],
                            'label'          => '',
                            'valueType'      => 'boolean',
                            'value'          => true,
                        ],
                        [
                            'key'            => 'aiChatReasoning',
                            'renderTypeList' => [
                                'hidden',
                            ],
                            'label'          => '',
                            'valueType'      => 'boolean',
                            'value'          => true,
                        ],
                        [
                            'key'            => 'aiChatTopP',
                            'renderTypeList' => [
                                'hidden',
                            ],
                            'label'          => '',
                            'valueType'      => 'number',
                        ],
                        [
                            'key'            => 'aiChatStopSign',
                            'renderTypeList' => [
                                'hidden',
                            ],
                            'label'          => '',
                            'valueType'      => 'string',
                        ],
                        [
                            'key'            => 'aiChatResponseFormat',
                            'renderTypeList' => [
                                'hidden',
                            ],
                            'label'          => '',
                            'valueType'      => 'string',
                        ],
                        [
                            'key'            => 'aiChatJsonSchema',
                            'renderTypeList' => [
                                'hidden',
                            ],
                            'label'          => '',
                            'valueType'      => 'string',
                        ],
                    ],
                    'outputs'      => [
                        [
                            'id'          => 'history',
                            'key'         => 'history',
                            'required'    => true,
                            'label'       => 'common:core.module.output.label.New context',
                            'description' => 'common:core.module.output.description.New context',
                            'valueType'   => 'chatHistory',
                            'valueDesc'   => [
                                'obj'   => 'System | Human | AI',
                                'value' => 'string',
                            ],
                            'type'        => 'static',
                        ],
                        [
                            'id'          => 'answerText',
                            'key'         => 'answerText',
                            'required'    => true,
                            'label'       => 'common:core.module.output.label.Ai response content',
                            'description' => 'common:core.module.output.description.Ai response content',
                            'valueType'   => 'string',
                            'type'        => 'static',
                        ],
                        [
                            'id'        => 'reasoningText',
                            'key'       => 'reasoningText',
                            'required'  => false,
                            'label'     => 'workflow:reasoning_text',
                            'valueType' => 'string',
                            'type'      => 'static',
                            'invalid'   => true,
                        ],
                    ],
                ],
                [
                    'nodeId'       => 'iKBoX2vIzETU',
                    'name'         => '知识库搜索',
                    'intro'        => '调用“语义检索”和“全文检索”能力，从“知识库”中查找可能与问题相关的参考内容。优先调用该工具来辅助回答用户的问题。',
                    'avatar'       => 'core/workflow/template/datasetSearch',
                    'flowNodeType' => 'datasetSearchNode',
                    'showStatus'   => true,
                    'position'     => [
                        'x' => 918.5901682164496,
                        'y' => -227.11542247619582,
                    ],
                    'version'      => '481',
                    'inputs'       => [
                        [
                            'key'            => 'datasets',
                            'renderTypeList' => [
                                'selectDataset',
                                'reference',
                            ],
                            'label'          => 'core.module.input.label.Select dataset',
                            'value'          => [],
                            'valueType'      => 'selectDataset',
                            'list'           => [],
                            'required'       => true,
                        ],
                        [
                            'key'            => 'similarity',
                            'renderTypeList' => [
                                'selectDatasetParamsModal',
                            ],
                            'label'          => '',
                            'value'          => 0.4,
                            'valueType'      => 'number',
                        ],
                        [
                            'key'            => 'limit',
                            'renderTypeList' => [
                                'hidden',
                            ],
                            'label'          => '',
                            'value'          => 1500,
                            'valueType'      => 'number',
                        ],
                        [
                            'key'            => 'searchMode',
                            'renderTypeList' => [
                                'hidden',
                            ],
                            'label'          => '',
                            'valueType'      => 'string',
                            'value'          => 'embedding',
                        ],
                        [
                            'key'            => 'usingReRank',
                            'renderTypeList' => [
                                'hidden',
                            ],
                            'label'          => '',
                            'valueType'      => 'boolean',
                            'value'          => false,
                        ],
                        [
                            'key'            => 'datasetSearchUsingExtensionQuery',
                            'renderTypeList' => [
                                'hidden',
                            ],
                            'label'          => '',
                            'valueType'      => 'boolean',
                            'value'          => true,
                        ],
                        [
                            'key'            => 'datasetSearchExtensionModel',
                            'renderTypeList' => [
                                'hidden',
                            ],
                            'label'          => '',
                            'valueType'      => 'string',
                        ],
                        [
                            'key'            => 'datasetSearchExtensionBg',
                            'renderTypeList' => [
                                'hidden',
                            ],
                            'label'          => '',
                            'valueType'      => 'string',
                            'value'          => '',
                        ],
                        [
                            'key'             => 'userChatInput',
                            'renderTypeList'  => [
                                'reference',
                                'textarea',
                            ],
                            'valueType'       => 'string',
                            'label'           => 'workflow:user_question',
                            'toolDescription' => 'workflow:content_to_search',
                            'required'        => true,
                            'value'           => [
                                'workflowStartNodeId',
                                'userChatInput',
                            ],
                        ],
                    ],
                    'outputs'      => [
                        [
                            'id'          => 'quoteQA',
                            'key'         => 'quoteQA',
                            'label'       => 'common:core.module.Dataset quote.label',
                            'description' => 'workflow:special_array_format',
                            'type'        => 'static',
                            'valueType'   => 'datasetQuote',
                            'valueDesc'   => [
                                'id'           => 'string',
                                'datasetId'    => 'string',
                                'collectionId' => 'string',
                                'sourceName'   => 'string',
                                'sourceId'     => 'string',
                                'q'            => 'string',
                                'a'            => 'string',
                            ],
                        ],
                    ],
                ],
            ],
            'edges'       => [
                [
                    'source'       => 'workflowStartNodeId',
                    'target'       => 'iKBoX2vIzETU',
                    'sourceHandle' => 'workflowStartNodeId-source-right',
                    'targetHandle' => 'iKBoX2vIzETU-target-left',
                ],
                [
                    'source'       => 'iKBoX2vIzETU',
                    'target'       => '7BdojPlukIQw',
                    'sourceHandle' => 'iKBoX2vIzETU-source-right',
                    'targetHandle' => '7BdojPlukIQw-target-left',
                ],
            ],
            'chatConfig'  => [
                'questionGuide'  => [
                    'open' => false,
                ],
                'ttsConfig'      => [
                    'type' => 'web',
                ],
                'whisperConfig'  => [
                    'open'            => false,
                    'autoSend'        => false,
                    'autoTTSResponse' => false,
                ],
                'chatInputGuide' => [
                    'open'      => false,
                    'textList'  => [],
                    'customUrl' => '',
                ],
                'instruction'    => '',
                'autoExecute'    => [
                    'open'          => false,
                    'defaultPrompt' => '',
                ],
                'variables'      => [],
                '_id'            => '67dd336c73d409056f3a91f8',
                'welcomeText'    => 'WELCOME_TEXT',
            ],
            'isPublish'   => true,
            'versionName' => '2025-03-21 17:48:34',
        ]
    ],
    'default_welcome' => '请根据提供的知识库内容回答问题。',
];
