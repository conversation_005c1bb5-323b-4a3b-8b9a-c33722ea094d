<?php

return [
    'route' => [
        /**
         * API 路由命名前缀
         */
        'as'         => 'office.',

        /**
         * 可配置 API 独立域名.
         */
        'domain'     => env('OFFICE_ROUTE_DOMAIN', ''),

        /**
         * 不使用用独立域名，API 地址前缀
         */
        'prefix'     => env('OFFICE_ROUTE_PREFIX', 'office'),

        /**
         * 默认加载的中间件
         */
        'middleware' => ['api']
    ],
];
