<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('plugin_vidu_templates', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('vidu_template_category_id')->index();
            $table->string('name')->index();
            $table->string('scene')->comment('模板key')->index();
            $table->json('detail')->comment('场景详情');
            $table->json('input_instruction')->comment('输入说明');
            $table->string('video_url')->comment('视频地址');
            $table->text('prompt')->comment('提示词');
            $table->string('cover_url')->comment('图片地址');
            $table->boolean('status')->default(1);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('plugin_vidu_templates');
    }
};
