<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('bailian_assistant_settings', function (Blueprint $table) {
            $table->unsignedBigInteger('user_id')->primary()->index();
            $table->string('avatar')->nullable();
            $table->string('voice_type')->nullable();
            $table->string('voice_id')->nullable();
            $table->string('font_size')->nullable();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('bailian_assistant_settings');
    }
};
