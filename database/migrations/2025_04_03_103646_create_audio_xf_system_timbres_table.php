<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('audio_xf_system_timbres', function (Blueprint $table) {
            $table->id();
            $table->string('people')->nullable();
            $table->string('url')->nullable();
            $table->string('model')->nullable();
            $table->string('voice_id')->nullable();
            $table->string('scenarios')->nullable();
            $table->string('lang')->nullable();
            $table->string('image_url')->nullable();
            $table->string('hz')->nullable();
            $table->string('format')->nullable();
            $table->boolean('status')->default(1);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('audio_xf_system_timbres');
    }
};
