<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('draw_audio', function (Blueprint $table) {
            $table->id();
            $table->string('no');
            $table->string('parent_no')->nullable();
            $table->unsignedBigInteger('user_id');
            $table->string('gender')->nullable();
            $table->unsignedInteger('mode')->default(0);
            $table->unsignedDecimal('score', 12, 2)->default(0);
            $table->string('job_id')->nullable();
            $table->string('model');
            $table->string('prompt');
            $table->string('name')->nullable();
            $table->string('cover')->nullable();
            $table->string('audio')->nullable();
            $table->string('video')->nullable();
            $table->text('lyric')->nullable();
            $table->string('tags')->nullable();
            $table->unsignedInteger('duration')->default(0);
            $table->boolean('status')->default(0);
            $table->timestamp('over_at')->nullable()->index();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('draw_audio');
    }
};
