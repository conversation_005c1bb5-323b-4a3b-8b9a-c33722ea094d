<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('ai_chat_configs', function (Blueprint $table) {
            $table->string('cover')->nullable()->comment('LOGO');
            $table->text('remark')->nullable()->comment('描述');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('ai_chat_configs', function (Blueprint $table) {
            //
        });
    }
};
