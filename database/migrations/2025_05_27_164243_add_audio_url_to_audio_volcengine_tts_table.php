<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('audio_volcengine_tts', function (Blueprint $table) {
            $table->string('audio_url')->nullable()->comment('试听音频地址')->after('voice_type');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('audio_volcengine_tts', function (Blueprint $table) {
            $table->dropColumn('audio_url');
        });
    }
};
