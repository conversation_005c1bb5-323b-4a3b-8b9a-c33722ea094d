<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('ai_chat_engines', function (Blueprint $table) {
            $table->id();
            $table->string('title')->nullable();
            $table->string('name')->nullable();
            $table->string('type')->index();
            $table->unsignedInteger('maxlen')->default(0);
            $table->unsignedInteger('maxinput')->default(0);
            $table->unsignedInteger('maxout')->default(0);
            $table->string('url')->nullable();
            $table->boolean('status')->default(1)->index();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ai_chat_engines');
    }
};
