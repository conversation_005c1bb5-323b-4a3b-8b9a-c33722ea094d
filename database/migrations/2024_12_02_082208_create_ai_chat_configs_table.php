<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('ai_chat_configs', function (Blueprint $table) {
            $table->id();
            $table->string('name');
            $table->string('key')->index();
            $table->string('engine_name')->index();
            $table->string('token_url')->nullable();
            $table->string('apikey');
            $table->string('secretkey');
            $table->json('params')->nullable();
            $table->boolean('status')->default(false);
            $table->boolean('is_default')->default(false);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ai_configs');
    }
};
