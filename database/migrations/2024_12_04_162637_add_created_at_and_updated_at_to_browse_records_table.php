<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('browse_records', function (Blueprint $table) {
            $table->dropColumn('created_time');
            $table->timestamp('created_at')->nullable()->after('count');
            $table->timestamp('updated_at')->nullable()->after('created_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('browse_records', function (Blueprint $table) {
            //
        });
    }
};
