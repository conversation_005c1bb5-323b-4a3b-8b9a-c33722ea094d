<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('draw_videos', function (Blueprint $table) {
            $table->id();
            $table->unsignedInteger('user_id')->index();
            $table->string('model')->default('cogvideox');
            $table->string('no');
            $table->unsignedInteger('score')->default(0);
            $table->string('job_id')->nullable();
            $table->string('quality')->default('quality');
            $table->string('prompt')->nullable();
            $table->string('image_url')->nullable();
            $table->string('cover')->nullable();
            $table->string('server_video')->nullable();
            $table->boolean('is_vip')->default(false);
            $table->boolean('status')->default(0)->index();
            $table->timestamp('over_at')->nullable()->index();
            $table->string('error_message')->nullable();
            $table->json('source')->nullable();
            $table->json('style')->nullable();
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('draw_videos');
    }
};
