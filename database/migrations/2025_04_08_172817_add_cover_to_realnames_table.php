<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('realnames', function (Blueprint $table) {
            $table->string('cover')->nullable()->comment('手持照片')->after('json_data');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('realnames', function (Blueprint $table) {
            $table->dropColumn('cover');
        });
    }
};
