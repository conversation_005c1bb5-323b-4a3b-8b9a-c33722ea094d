<?php

use App\Models\BailianKnowledge;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('bailian_knowledge', function (Blueprint $table) {
            $table->string('type')
                ->default(BailianKnowledge::TYPE_NORMAL)
                ->comment('知识库类型')
                ->after('description');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('bailian_knowledge', function (Blueprint $table) {
            $table->dropColumn('type');
        });
    }
};
