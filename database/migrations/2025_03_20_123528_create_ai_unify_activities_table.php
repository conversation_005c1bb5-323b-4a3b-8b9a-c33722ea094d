<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('ai_unify_activities', function (Blueprint $table) {
            $table->id();
            $table->string('type')->comment('类型');
            $table->string('title')->comment('标题');
            $table->text('description')->nullable()->comment('描述');
            $table->text('rules')->nullable()->comment('规则');
            $table->string('cover')->comment('展示图片');
            $table->unsignedInteger('score')->default(0)->comment('总积分');
            $table->timestamp('start_at')->nullable()->comment('活动开始时间');
            $table->timestamp('end_at')->nullable()->comment('活动结束时间');
            $table->boolean('status')->default(0)->comment('活动状态');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ai_unify_activities');
    }
};
