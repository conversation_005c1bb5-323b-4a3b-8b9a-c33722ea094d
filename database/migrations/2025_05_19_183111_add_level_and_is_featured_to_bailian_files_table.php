<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('bailian_files', function (Blueprint $table) {
            $table->boolean('is_public')->default(0)->after('description')->comment('是否公开');
            $table->boolean('is_featured')->default(0)->after('is_public')->comment('是否精选');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('bailian_files', function (Blueprint $table) {
            $table->dropColumn('is_public');
            $table->dropColumn('is_featured');
        });
    }
};
