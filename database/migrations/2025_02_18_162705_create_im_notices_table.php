<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('im_notices', function (Blueprint $table) {
            $table->id();
            $table->string('title')->comment('标题');
            $table->string('description')->nullable()->comment('内容');
            $table->integer('type')->default(1)->comment('类型');
            $table->text('user_ids')->nullable()->comment('发放对象');
            $table->easyStatus();
            $table->dateTime('send_at')->nullable()->comment('通知时间');
            $table->timestamps();
            $table->index('created_at');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('im_notices');
    }
};
