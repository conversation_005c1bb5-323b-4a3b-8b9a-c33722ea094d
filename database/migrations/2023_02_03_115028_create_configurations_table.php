<?php

use App\Enums\ConfigType;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('configurations', function (Blueprint $table) {
            $table->id();
            $table->string('module', 32)->index()->comment('所属模块');
            $table->string('key')->index()->comment('配置键值');
            $table->longText('values')->nullable()->comment('值');
            $table->enum('type', ConfigType::values())->comment('类型');
            $table->json('source')->nullable()->comment('select,checkbox,radio的数据源');
            $table->json('ext')->nullable()->comment('文件，图片的拓展配置');
            $table->timestamps();
            $table->softDeletes();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('configurations');
    }
};
