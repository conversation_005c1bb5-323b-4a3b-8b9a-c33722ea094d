<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('recharge_orders', function (Blueprint $table) {
            $table->id();
            $table->string('no');
            $table->unsignedInteger('user_id');
            $table->unsignedInteger('package_id');
            $table->unsignedDecimal('price');
            $table->unsignedInteger('score');
            $table->json('source')->nullable();
            $table->boolean('status')->default(0);
            $table->timestamp('paid_at')->nullable();
            $table->timestamp('settle_at')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('recharge_orders');
    }
};
