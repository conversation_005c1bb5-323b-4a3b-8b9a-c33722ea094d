<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('audio_volcengine_user_configs', function (Blueprint $table) {
            $table->unsignedBigInteger('user_id')->primary()->index();
            $table->unsignedBigInteger('role_id')->nullable()->index();
            $table->json('tts')->nullable();
            $table->string('image_model')->nullable();
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('audio_volcengine_user_configs');
    }
};
