<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('audio_rooms_callbacks', function (Blueprint $table) {
            $table->string('event_id', 100)->primary();
            $table->unsignedBigInteger('room_id')->index();
            $table->string('type')->comment('回调类型')->index();
            $table->json('event_data')->nullable();
            $table->string('app_id')->nullable();
            $table->timestamps();
            $table->foreign('room_id')->references('id')->on('audio_rooms')->cascadeOnDelete();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('audio_rooms_callbacks');
    }
};
