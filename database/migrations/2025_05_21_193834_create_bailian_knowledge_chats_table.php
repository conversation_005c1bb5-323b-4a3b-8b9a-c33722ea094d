<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('bailian_knowledge_chats', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id')->index();
            $table->string('session_id')->nullable();
            $table->json('usage')->nullable();
            $table->string('input_message')->comment('输入的信息');
            $table->json('inputs')->nullable();
            $table->text('output_message')->nullable()->charset('utf8mb4')->collation('utf8mb4_unicode_ci')
                ->comment('回复信息');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('bailian_knowledge_chats');
    }
};
