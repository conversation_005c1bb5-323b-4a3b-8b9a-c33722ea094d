<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('bailian_knowledge_files', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id')->nullable()->comment('用户id');
            $table->string('bailian_category_id')->index()->comment('百炼分类ID');
            $table->unsignedBigInteger('storage_id')->nullable()->comment('存储id');
            $table->string('name')->comment('文件名称');
            $table->string('workspace_id')->index()->comment('类目所属的业务空间 ID');
            $table->string('lease_id')->index()->comment('文档上传租约 ID');
            $table->string('file_id')->nullable()->index()->comment('阿里云文件ID');
            $table->string('parser')->index()->comment('解析器类型');
            $table->string('category_type')->comment('类目类型');
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('bailian_knowledge_files');
    }
};
