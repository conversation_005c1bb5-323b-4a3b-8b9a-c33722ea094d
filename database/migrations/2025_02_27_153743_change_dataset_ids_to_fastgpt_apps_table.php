<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('fastgpt_apps', function (Blueprint $table) {
            $table->json('dataset_ids')->nullable()->comment('关联的知识库')->change();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('fastgpt_apps', function (Blueprint $table) {
            //
        });
    }
};
