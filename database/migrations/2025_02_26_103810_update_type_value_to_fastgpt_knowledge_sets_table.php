<?php

use App\Models\Enums\FastgptKnowledgeSetEnum;
use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        $table      = (new \App\Models\FastgptKnowledgeSet())->getTable();
        $typeValues = array_map(function ($value) {
            return "'{$value}'";
        }, FastgptKnowledgeSetEnum::values());
        $typeValues = implode(',', $typeValues);
        DB::statement("ALTER TABLE {$table} MODIFY type ENUM($typeValues) NOT NULL");
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('fastgpt_knowledge_sets', function (Blueprint $table) {
            //
        });
    }
};
