# Laravel 10

## -1. 写在前面

现在开始，请求头信息可能会慢慢增加，目前需要的请求头信息有

| 参数名            | 参数类型   | 必须 | 说明                   | 例子                 |
|----------------|--------|----|----------------------|--------------------|
| Authorization  | string | Y  | 身份鉴权标识               | Bearer abcdefg     |
| X-Device-Type  | string | Y  | 发送请求的设备类型            | Andriod,IOS,H5,WEB |
| X-Device-Id    | string | Y  | 发送请求的设备ID，APP可以用IMEI |                    |
| Accept         | string | Y  | 必须项目                 | application/json   |
| X-Request-Time | int    | Y  | 当前请求时间，年月日时分秒        | 20230110153223     |

## 测试地址

> http://laravelx.uzchain.tech/admin
> 
> 管理员账号：admin   管理员密码：admin


## 0. 相关文档

- [Laravel 10.x](https://learnku.com/docs/laravel/10.x)
- [DCat Admin 2.x](https://learnku.com/docs/dcat-admin/2.x)
- [模块管理](https://docs.laravelmodules.com/)
- [模块安装](https://github.com/joshbrw/laravel-module-installer)
- [Guzzle 7](https://docs.guzzlephp.org/en/stable/)
- [Guzzle 6 中文文档](https://guzzle-cn.readthedocs.io/zh_CN/latest/)
- [模型数据缓存](https://github.com/GeneaLabs/laravel-model-caching)

## 1. 安装及初始化

(1.1) 克隆项目

```shell
git clone https://git.yuzhankeji.cn/Jason/laravel-X.git demo
```

(1.2) 初始化 `.env` 文件，配置 `mysql`，`redis`，`storage` 等信息

```shell
cp .env.example .env
php artisan key:generate
```

(1.3) 安装Admin

```shell
php artisan admin:install
```

(1.4) nginx伪静态

```
location / {
    try_files $uri $uri/ /index.php?$query_string;
}
```

## 2. Api 中间件

> auth:sanctum 必须认证

> guess:sanctum 可有可无，Api::user() = null

## 2.1 中台接口 Office 

> 配置 office.php

## 3. 开发模块

(3.1) 创建模块

```shell
php artisan module:make ModuleName
```

(3.2) 初始化模块

1. `Bootstrap` 中，可以配置在模块注册和注销的过程中，创建和删除数据库。

2. `Bootstrap` 中，可以配置在模块注册过程中，自动对数据库进行填充。

3. 模块间的关联关系，可使用对模型注入的方式，进行关联，在 `ServiceProvider` 的 `boot` 方法中使用

```php
use App\Models\User;

class ServiceProvider {
    public function boot(): void
        User::resolveRelationUsing('stores', fn(User $user) => $user->hasMany(Store::class));
    }
}
```

(3.3) 配置参考，`config.json`

```json
[
    {
        "name": "配置名称",
        "type": "tabs",
        "key": "unique_key",
        "list": [
            {
                "name": "支付日志",
                "type": "类型请参考 App\Models\Configuration 中的常量",
                "key": "LOGGER_ENABLE",
                "value": "1",
                "source": {
                    "0": "关闭",
                    "1": "开启"
                }
            }
        ]
    }
]
```

## Traits 说明

### 1. AutoCreateOrderNo

统一的订单编号生成，引入后，如果订单编号字段为 `no` 可以不做任何修改

如果不是，在模型中实现方法 `getOrderNoField():string` 即可

### 自动格式化

https://mlocati.github.io/php-cs-fixer-configurator/#version:3.16

## 模块错误概略

XX YYY

XX 模块编号
YY 错误点代码

### 已定义编号

| 编号 | 模块      |                                                                              |
|----|---------|------------------------------------------------------------------------------|
| 10 | 内容模块    | [Cms](https://git.yuzhankeji.cn/UzTech/laravel-cms-module-x)                 |
| 11 | 付费课程    | [Course](https://git.yuzhankeji.cn/UzTech/laravel-course-module-x)           |
| 12 | 活动报名    | [Activity](https://git.yuzhankeji.cn/UzTech/laravel-activity-module-x)       |
| 15 | 商务社交名片  | [Business](https://git.yuzhankeji.cn/UzTech/laravel-business-module-x)       |
| 20 | App版本管理 | [AppVersion](https://git.yuzhankeji.cn/UzTech/laravel-app-version-module-x)  |
| 30 | 商城模块    | [Mall](https://git.yuzhankeji.cn/UzTech/laravel-mall-module-x)               |
| 31 | 供应链     | [Supply](https://git.yuzhankeji.cn/UzTech/laravel-supply-module-x)           |
| 32 | 优惠券     | [Coupon](https://git.yuzhankeji.cn/UzTech/laravel-coupon-module-x)           |
| 35 | 互动模块    | [Interaction](https://git.yuzhankeji.cn/UzTech/laravel-interaction-module-x) |
| 39 | 溯源模块    | [Source](https://git.yuzhankeji.cn/UzTech/laravel-source-module-x)           |
| 40 | 客户关系管理  | [Crm](https://git.yuzhankeji.cn/UzTech/laravel-crm-module-x)                 |
| 41 | 仓储物流系统  | [Wms](https://git.yuzhankeji.cn/UzTech/laravel-wms-module-x)                 |
| 50 | 投票模块    | [Vote](https://git.yuzhankeji.cn/UzTech/laravel-vote-module-x)               |
| 60 | 区块链模块   | [Chain](https://git.yuzhankeji.cn/UzTech/laravel-chain-module-x)             |
| 61 | 阿里云     | [Aliyun](https://git.yuzhankeji.cn/UzTech/laravel-aliyun-module-x)           |
| 62 | Ai      | [Ai](https://git.yuzhankeji.cn/UzTech/laravel-ai-module-x)                   |
| 65 | 实名认证与签约 | [Contract](https://git.yuzhankeji.cn/UzTech/laravel-contract-module-x)       |
| 80 | 用户模块    | [User](https://git.yuzhankeji.cn/UzTech/laravel-user-module-x)               |
| 81 | 任务管理    | [Task](https://git.yuzhankeji.cn/UzTech/laravel-task-module-x)               |
| 82 | 三方平台    | [Socialite](https://git.yuzhankeji.cn/UzTech/laravel-socialite-module-x)     |
| 83 | 海报管理    | [Poster](https://git.yuzhankeji.cn/UzTech/laravel-poster-module-x)        |
| 90 | 结算模块    | [Settlement](https://git.yuzhankeji.cn/UzTech/laravel-settlement-module-x)   |
| 92 | 存储模块    | [Storage](https://git.yuzhankeji.cn/UzTech/laravel-storage-module-x)         |
| 94 | 工作流     | [Workflow](https://git.yuzhankeji.cn/UzTech/laravel-workflow-module-x)       |
| 95 | 支付模块    | [Payment](https://git.yuzhankeji.cn/UzTech/laravel-payment-module-x)         |
