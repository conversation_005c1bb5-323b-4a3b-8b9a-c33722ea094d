<?php

namespace Modules\Socialite\Http\Controllers\Api\Wechat\Handles;

use EasyWeChat\OfficialAccount\Message;
use Modules\Socialite\Enums\MessageType;
use Modules\Socialite\Models\WechatKeyword;

class SubscribeMessage
{
    public function __construct(protected Message $message)
    {
    }

    public function __invoke()
    {
        $match = WechatKeyword::where('keyword', '首次关注')
            ->first();

        if ($match) {
            return match ($match->type) {
                MessageType::TEXT => $match->message['text']['content'],
                MessageType::IMAGE => [
                    'MsgType' => 'image',
                    'Image'   => [
                        'MediaId' => $match->message['image']['media_id'],
                    ],
                ],
                MessageType::VOICE => [
                    'MsgType' => 'voice',
                    'Voice'   => [
                        'MediaId' => $match->message['voice']['media_id'],
                    ],
                ],
                MessageType::VIDEO => [
                    'MsgType' => 'video',
                    'Video'   => [
                        'Title'       => $match->message['video']['title'],
                        'Description' => $match->message['video']['description'],
                        'MediaId'     => $match->message['video']['media_id'],
                    ],
                ],
                MessageType::MUSIC => [
                    'MsgType' => 'music',
                    'Music'   => [
                        'Title'        => $match->message['music']['title'],
                        'Description'  => $match->message['music']['description'],
                        'MusicUrl'     => $match->message['music']['music_url'],
                        'HQMusicUrl'   => $match->message['music']['hq_music_url'],
                        'ThumbMediaId' => $match->message['music']['thumb_media_id'],
                    ],
                ],
                MessageType::NEWS => $this->parseNewsMessage($match->message['news']),
                default => 'NO KEY',
            };
        } else {
            return '未匹配到关键字';
        }
    }

    public function parseNewsMessage(array $messages): array
    {
        $articles = [];

        foreach ($messages as $message) {
            $articles[] = [
                'Title'       => $message['title'],
                'Description' => $message['description'],
                'PicUrl'      => $message['pic_url'],
                'Url'         => $message['url'],
            ];
        }
        return [
            'MsgType'      => 'news',
            'ArticleCount' => count($messages),
            'Articles'     => $articles,
        ];
    }
}