<?php

namespace Modules\Socialite\Http\Controllers\Api\Wechat;

use App\Facades\Api;
use App\Models\User;
use App\Packages\XinHuaERP\XinHuaERP;
use Exception;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Storage;
use Modules\Socialite\Http\Controllers\Api\Controller;
use Modules\Socialite\Http\Requests\Wechat\MiniOpenidRequest;
use Modules\Socialite\Models\Wechat;

class MiniController extends Controller
{
    public function index(): JsonResponse
    {
        return $this->success();
    }

    /**
     * Notes: 小程序手机号登录
     *
     * @Author: 玄尘
     * @Date: 2023/12/14 15:59
     * @param  \Modules\Socialite\Http\Requests\Wechat\MiniOpenidRequest  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function login(MiniOpenidRequest $request)
    {
        try {
            $code       = $request->code;
            $info_code  = $request->info_code;
            $data       = [
                'code' => (string) $code,
            ];
            $miniApp    = app('wechat.mini');
            $mobileData = $miniApp->getClient()->postJson('wxa/business/getuserphonenumber', $data);
            if ($mobileData->isFailed()) {
                info($mobileData->toArray());
                return $this->failed('获取手机号失败');
            }

            $mobile = $mobileData->toArray()['phone_info']['purePhoneNumber'];
            $user   = User::where('username', $mobile)->first();

            $utils    = $miniApp->getUtils();
            $response = $utils->codeToSession($info_code);

            $parent_id = $this->parseParentIdFromRequest($request);
            $staffer   = $this->parseStafferIdFromRequest($request);
            $wechat    = Wechat::updateOrCreate([
                'union_id' => $response['unionid'] ?? '',
            ], [
                'mini_openid' => $response['openid'] ?? '',
            ]);
            if (! $user) {
                $user = User::create([
                    'username'     => $mobile,
                    'password'     => '111111',
                    'wechat_model' => $wechat,
                ]);
            } elseif ($user->is_lock) {
                return $this->failed('当前用户登录失败');
            }

            if ($user->relation->parent_id === 0 && $parent_id > 0) {
                try {
                    $user->relation->changeParent($parent_id);
                } catch (Exception $exception) {
                }
            }

            //关联员工邀请
            if ($user->wasRecentlyCreated && $staffer) {
                $this->setStaffer($staffer, $user);
            }

            $token = $this->loginUser($user);
            XinHuaERP::user()->addVisitor($response['unionid'] ?? '');
            return $this->success([
                'token'  => $token->plainTextToken,
                'is_new' => $user->wasRecentlyCreated,
                'type'   => 'Bearer',
            ]);
        } catch (Exception $e) {
            return $this->failed($e->getMessage());
        }
    }

    /**
     * Notes: 获取小程序openid
     *
     * @Author: 玄尘
     * @Date: 2023/12/12 15:36
     * @param  \Modules\Socialite\Http\Requests\Wechat\MiniOpenidRequest  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function openid(MiniOpenidRequest $request)
    {
        try {
            $app    = app('wechat.mini');
            $config = $app->getConfig();

            $session = $app->getClient()->get('/sns/jscode2session', [
                'appid'      => $config->get('app_id', ''),
                'secret'     => $config->get('secret', ''),
                'js_code'    => $request->safe()->code,
                'grant_type' => 'authorization_code',
            ]);
            if ($session->isFailed()) {
                return $this->failed($session->toArray()['errmsg']);
            }

            return $this->success([
                'openid' => $session->toArray()['openid']
            ]);
        } catch (Exception $exception) {
            return $this->failed($exception->getMessage());
        }
    }

    /**
     * Notes: 小程序邀请码
     *
     * @Author: 玄尘
     * @Date: 2023/12/12 15:46
     */
    public function share(Request $request)
    {
        $user       = Api::user();
        $size       = $request->size ?? '300';
        $envVersion = $request->version ?? 'release';
        $data       = [
            'username' => $user->username,
            'nickname' => $user->info->nickname ?? '',
            'avatar'   => $user->info->avatar_url ?? '',
        ];

        $invite = app('user.hashids')->encode($user->id);

        try {
            $app  = app('wechat.mini');
            $name = md5($user->id).'.png';
            $path = "share/users/{$envVersion}/{$name}";
            if (! Storage::has($path) || $request->refresh) {
                $response = $app->getClient()->postJson('/wxa/getwxacodeunlimit', [
                    'scene'       => http_build_query([
                        'invite' => $invite
                    ]),
                    'page'        => 'pages/index/index',
                    'width'       => $size,
                    'is_hyaline'  => true,
                    'line_color'  => [
                        'r' => 255,
                        'g' => 255,
                        'b' => 255,
                    ],
                    'env_version' => $envVersion,
                    'check_path'  => false,
                ]);
                Storage::put($path, $response->getContent());
            }

            $data = array_merge($data, [
                'qrcode' => Storage::url($path),
            ]);
            return $this->success($data);
        } catch (Exception $e) {
            return $this->failed($e->getMessage());
        }
    }
}