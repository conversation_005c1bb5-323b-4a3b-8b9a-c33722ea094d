<?php

namespace Modules\Socialite\Http\Controllers\Api;

use App\Http\Controllers\ApiController;
use App\Models\User;
use Exception;
use Illuminate\Http\Request;
use Lara<PERSON>\Sanctum\NewAccessToken;
use Modules\Mall\Models\StoreCustomer;
use Modules\Mall\Models\StoreStaffer;
use Vin<PERSON>\Hashids\Facades\Hashids;

class Controller extends ApiController
{
    /**
     * Notes   : 登录用户
     *
     * @Date   : 2023/7/14 13:55
     * <AUTHOR> <Jason.C>
     * @param  \App\Models\User  $user
     * @return \Laravel\Sanctum\NewAccessToken
     * @throws \Exception
     */
    protected function loginUser(User $user): NewAccessToken
    {
        if ($user->is_lock) {
            throw new Exception('用户被禁止登录');
        }

        return $user->createToken(config('user.TOKEN_NAME'));
    }

    /**
     * Notes: 解码推荐人
     *
     * @Author: 玄尘
     * @Date: 2023/12/14 10:32
     * @param  \Illuminate\Http\Request  $request
     * @return int
     * @throws \Exception
     */
    protected function parseParentIdFromRequest(Request $request): int
    {
        $code = $request->invite;

        if (! $code) {
            return 0;
        }

        $invite = app('user.hashids')->decode($code);

        if (empty($invite)) {
            return 0;
//            throw  new Exception('邀请码不正确');
        }

        $parentId   = $invite[0];
        $parentUser = User::find($parentId);

        if (! $parentUser) {
            return 0;
//            throw new Exception('推荐人不存在');
        }

        return $parentId;
    }

    /**
     * Notes: 解码员工邀请
     *
     * @Author: 玄尘
     * @Date: 2024/5/8 下午3:05
     * @param  \Illuminate\Http\Request  $request
     * @throws \Exception
     */
    protected function parseStafferIdFromRequest(Request $request)
    {
        $code = $request->staffer_invite;

        if (! $code) {
            return '';
        }

        $invite = app('user.hashids')->decode($code);

        if (empty($invite)) {
            return '';
//            throw  new Exception('邀请码不正确');
        }

        $stafferId = $invite[0];
        $staffer   = StoreStaffer::find($stafferId);

        if (! $staffer) {
            return '';

//            throw new Exception('推荐人(员工)不存在');
        }

        return $staffer;
    }

    /**
     * Notes: 设置员工关联
     *
     * @Author: 玄尘
     * @Date: 2024/5/8 下午3:18
     * @param $staffer
     * @param $user
     */
    public function setStaffer($staffer, $user)
    {
        StoreCustomer::updateOrCreate([
            'user_id' => $user->id
        ], [
            'store_id'         => $staffer->store_id,
            'store_staffer_id' => $staffer->id,
        ]);
    }
}