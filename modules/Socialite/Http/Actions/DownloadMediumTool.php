<?php

namespace Modules\Socialite\Http\Actions;

use Dcat\Admin\Actions\Response;
use Dcat\Admin\Grid\Tools\AbstractTool;
use Exception;
use Modules\Socialite\Enums\MediaType;
use Modules\Socialite\Models\WechatMedium;
use RuntimeException;

class DownloadMediumTool extends AbstractTool
{
    protected string $title = '同步素材';

    public function handle(): Response
    {
        try {
            $this->downloadMedium();

            return $this->response()->success('同步成功')->refresh();
        } catch (Exception $exception) {
            return $this->response()->error($exception->getMessage());
        }
    }

    /**
     * Notes    : 获取所有类型素材并保存
     *
     * <AUTHOR> 丁明远
     * @Date    : 2023/10/20 0020 上午 11:23
     */
    private function downloadMedium(): void
    {
        $countList = $this->mediumCount();
        foreach ($countList as $key => $value) {
            $type = substr($key, 0, -6);
            if ($value > 0 && in_array($type, MediaType::values(), true)) {
                $this->mediumList($type);
            }
        }
    }

    /**
     * Notes    : 获取素材列表全部素材并保存
     *
     * <AUTHOR> 丁明远
     * @Date    : 2023/10/20 0020 上午 11:22
     * @param  string  $type 素材类型（image、voice、video、thumb）
     * @param  int  $page   页码
     * @param  int  $count  总条数（不大于20）
     */
    private function mediumList(string $type, int $page = 1, int $count = 20): void
    {
        $result = $this->getMediumList($type, $page, $count);
        if (empty($result)) {
            return;
        }
        foreach ($result['item'] as $item) {
            $this->saveWechatMedium($type, $item);
        }
        if ($page < ceil($result['total_count'] / $count)) {
            $this->mediumList($type, ($page + 1), $count);
        }
    }

    /**
     * Notes    : 获取素材总数
     *
     * <AUTHOR> 丁明远
     * @Date    : 2023/10/20 0020 上午 11:20
     * @return array    [voice_count => number, video_count => number, image_count => number, news_count]
     */
    private function mediumCount(): array
    {
        $result   = app('wechat.official_account')->getClient()->get('cgi-bin/material/get_materialcount');
        $response = $result->toArray(false);
        if (! empty($response['errcode'])) {
            throw new RuntimeException($response['errmsg'] ?? '', $response['errcode']);
        }

        return is_array($response) ? $response : [];
    }

    /***
     * Notes    : 获取素材列表
     *
     * <AUTHOR> 丁明远
     * @Date    : 2023/10/20 0020 上午 11:16
     * @param  string  $type  素材类型（image、voice、video、thumb）
     * @param  int  $page  页码
     * @param  int  $count  总条数（不大于20）
     * @return array
     */
    private function getMediumList(string $type, int $page, int $count): array
    {
        $data     = [
            'type'   => $type,
            'offset' => ($page - 1) * $count,
            'count'  => $count,
        ];
        $result   = app('wechat.official_account')->getClient()->postJson('cgi-bin/material/batchget_material', $data);
        $response = $result->toArray(false);
        if (! is_array($response) && ! isset($response['item'], $response['total_count'], $response['item_count'])) {
            return [];
        }

        return $response;
    }

    /**
     * Notes    : 保存素材item
     *
     * <AUTHOR> 丁明远
     * @Date    : 2023/10/20 0020 上午 11:19
     * @param  string  $type
     * @param  array  $item
     */
    private function saveWechatMedium(string $type, array $item): void
    {
        WechatMedium::firstOrCreate([
            'type'     => $type,
            'media_id' => $item['media_id'],
        ], [
            'title'  => $item['name'],
            'status' => 1,
            'ext'    => $item,
        ]);
    }

    public function confirm(): array
    {
        return [
            '同步素材',
            '确定要同步微信公众号素材到当前列表么?',
        ];
    }

    protected function authorize($user): bool
    {
        return $user->isAdministrator();
    }
}