<?php

namespace Modules\Socialite\Http\Actions;

use Dcat\Admin\Actions\Response;
use Dcat\Admin\Grid\Tools\AbstractTool;
use Exception;
use Illuminate\Http\Request;
use Modules\Socialite\Models\WechatMenu;

class PublishMenuTool extends AbstractTool
{
    protected string $title = '发布菜单';

    public function handle(): Response
    {
        try {
            $this->publishMenu();
            return $this->response()->success('发布成功')->refresh();
        } catch (Exception $exception) {
            return $this->response()->error($exception->getMessage());
        }
    }

    /**
     * @throws \Exception
     */
    protected function publishMenu(): void
    {
        $menus = WechatMenu::where('parent_id', 0)->get();
        $data  = [
            'button' => [],
        ];
        foreach ($menus as $item) {
            $data['button'][] = $item->toMenuArray();
        }

        $result = app('wechat.official_account')->getClient()->postJson('cgi-bin/menu/create', $data);

        $response = json_decode($result->getContent(), true);

        if ($response['errcode']) {
            throw new Exception($response['errmsg'], $response['errcode']);
        }
    }

    public function confirm(): array
    {
        return [
            '发布菜单',
            '确定要发布当前菜单结构到微信公众号么?',
        ];
    }

    protected function authorize($user): bool
    {
        return $user->isAdministrator();
    }
}