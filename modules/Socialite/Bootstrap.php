<?php

namespace Modules\Socialite;

use App\Contracts\ModuleBootstrap;
use Illuminate\Support\Facades\Artisan;

class Bootstrap implements ModuleBootstrap
{
    protected static string $menuKey = 'socialite-module';

    public static function install(): void
    {
        Artisan::call('migrate', [
            '--path' => 'modules/Socialite/Database/Migrations',
        ]);
        # 载入菜单
        self::createAdminMenu();
    }

    public static function uninstall(): void
    {
        Artisan::call('migrate:reset', [
            '--path' => 'modules/Socialite/Database/Migrations',
        ]);
        # 删除菜单
        self::deleteAdminMenu();
    }

    public static function upgrade(): void
    {
    }

    public static function createAdminMenu(): void
    {
        $menuModel = config('admin.database.menu_model');

        $main = $menuModel::create([
            'parent_id' => 0,
            'order'     => 83,
            'title'     => '三方平台',
            'icon'      => 'fa-github-alt '.self::$menuKey,
        ]);

        $wechat = $main->children()->create([
            'order' => 1,
            'title' => '微信',
            'icon'  => 'fa-wechat '.self::$menuKey,
        ]);

        $wechat->children()->createMany([
            [
                'order' => 0,
                'title' => '微信概览',
                'icon'  => 'fa-dashboard '.self::$menuKey,
                'uri'   => 'socialite/wechat',
            ],
            [
                'order' => 1,
                'title' => '微信账户',
                'icon'  => 'fa-group '.self::$menuKey,
                'uri'   => 'socialite/wechat/accounts',
            ],
            [
                'order' => 2,
                'title' => '微信菜单',
                'icon'  => 'fa-bars '.self::$menuKey,
                'uri'   => 'socialite/wechat/menus',
            ],
            [
                'order' => 3,
                'title' => '微信关键字',
                'icon'  => 'fa-bars '.self::$menuKey,
                'uri'   => 'socialite/wechat/keywords',
            ],
            [
                'order' => 4,
                'title' => '微信消息',
                'icon'  => 'fa-bars '.self::$menuKey,
                'uri'   => 'socialite/wechat/messages',
            ],
            [
                'order' => 5,
                'title' => '微信素材',
                'icon'  => 'fa-bars '.self::$menuKey,
                'uri'   => 'socialite/wechat/media',
            ],
        ]);

        $alipay = $main->children()->create([
            'order' => 2,
            'title' => '支付宝',
            'icon'  => 'fa-android '.self::$menuKey,
            'uri'   => 'socialite/alipay/accounts',
        ]);

        $weibo = $main->children()->create([
            'order' => 3,
            'title' => '微博',
            'icon'  => 'fa-weibo '.self::$menuKey,
            'uri'   => 'socialite/weibo/accounts',
        ]);

        $qq = $main->children()->create([
            'order' => 4,
            'title' => 'QQ',
            'icon'  => 'fa-qq '.self::$menuKey,
            'uri'   => 'socialite/qq/accounts',
        ]);

        $tiktok = $main->children()->create([
            'order' => 5,
            'title' => '抖音',
            'icon'  => 'fa-music '.self::$menuKey,
            'uri'   => 'socialite/tiktok/accounts',
        ]);
    }

    public static function deleteAdminMenu(): void
    {
        $menuModel = config('admin.database.menu_model');
        $menuModel::where('icon', 'like', '%'.self::$menuKey)->delete();
    }
}