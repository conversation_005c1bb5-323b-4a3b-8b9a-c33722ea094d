<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::create('socialite_wechats', function (Blueprint $table) {
            $table->id();
            $table->user()
                ->default(0);
            $table->string('wx_openid')
                ->index()
                ->nullable();
            $table->string('mini_openid')
                ->index()
                ->nullable();
            $table->string('union_id')
                ->index()
                ->nullable();
            $table->string('nickname')
                ->nullable();
            $table->string('avatar')
                ->nullable();
            $table->tinyInteger('gender')
                ->default(0);
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('socialite_wechats');
    }
};
