<?php

namespace Modules\Socialite\Listeners;

use Illuminate\Support\Facades\Storage;
use Modules\Socialite\Events\WechatMediumDeletedEvent;
use Modules\Socialite\Models\WechatMedium;
use RuntimeException;

class WechatMediumDeletedListener
{
    public function handle(WechatMediumDeletedEvent $event): void
    {
        $this->wechatMediumDelete($event->medium);
    }

    private function wechatMediumDelete(WechatMedium $medium): void
    {
        if ($medium->status ?? null) {
            $data     = ['media_id' => $medium->media_id];
            $result   = app('wechat.official_account')->getClient()->postJson('cgi-bin/material/del_material', $data);
            $response = json_decode($result->getContent(), true, 512, JSON_THROW_ON_ERROR);
            if (! empty($response['errcode'])) {
                throw new RuntimeException($response['errmsg'] ?? '', $response['errcode']);
            }
        }
        if (! empty($medium->file) && Storage::exists($medium->file)) {
            Storage::delete($medium->file);
        }
    }
}