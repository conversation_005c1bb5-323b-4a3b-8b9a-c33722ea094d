<?php

namespace Modules\Socialite\Jobs;

use App\Models\CustomerLog;
use Illuminate\Http\File;
use Illuminate\Support\Facades\Storage;

class AnalysisMediaJob extends SocialiteBaseJob
{
    public function __construct(protected CustomerLog $log)
    {
    }

    public function handle(): void
    {
        $client   = app('wechat.work')->getClient();
        $response = $client->get('/cgi-bin/media/get', [
            'query' => [
                'media_id' => $this->log->ext['media_id'],
            ],
        ]);
        if ($response->isSuccessful()) {
            $tempFilePath = tempnam(sys_get_temp_dir(), 'media_');
            file_put_contents($tempFilePath, $response->getContent());
            $file    = new File($tempFilePath);
            $hasName = $file->hashName();
            $storage = Storage::putFileAs('customer', $file, $hasName);
            $this->log->update([
                'media' => $storage
            ]);
        }
    }
}