<?php

namespace Modules\Socialite\Enums;

use App\Traits\EnumMethods;

enum MediaType: string
{
    use EnumMethods;

    case IMAGE = 'image';
    case VOICE = 'voice';
    case VIDEO = 'video';
    case THUMB = 'thumb';

    public const TYPE_MAP = [
        self::IMAGE->value => '图片',
        self::VOICE->value => '语音',
        self::VIDEO->value => '视频',
        self::THUMB->value => '缩略图',
    ];

    public function toString(): string
    {
        return self::TYPE_MAP[$this->value];
    }
}
