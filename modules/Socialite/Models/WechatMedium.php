<?php

namespace Modules\Socialite\Models;

use App\Models\Model;
use App\Traits\HasCovers;
use App\Traits\HasEasyStatus;
use GeneaLabs\LaravelModelCaching\Traits\Cachable;
use Modules\Socialite\Events\WechatMediumDeletedEvent;
use Modules\Socialite\Jobs\WechatMediumUploadJob;

class WechatMedium extends Model
{
    use Cachable,
        HasCovers,
        HasEasyStatus;

    protected $table = 'socialite_wechat_media';

    protected $casts = [
        'ext' => 'json',
    ];

    protected string $cover_field = 'file';

    protected static function boot(): void
    {
        parent::boot();

        self::created(static function ($model) {
            WechatMediumUploadJob::dispatch($model);
        });

        self::deleted(static function ($model) {
            WechatMediumDeletedEvent::dispatch($model);
        });
    }
}
