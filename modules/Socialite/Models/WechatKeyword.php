<?php

namespace Modules\Socialite\Models;

use App\Models\Model;
use GeneaLabs\LaravelModelCaching\Traits\Cachable;
use Modules\Socialite\Enums\MediaType;
use Modules\Socialite\Enums\MessageType;

class WechatKeyword extends Model
{
    use Cachable;

    protected $table = 'socialite_wechat_keywords';

    protected $casts = [
        'type'    => MessageType::class,
        'message' => 'json',
    ];
}
