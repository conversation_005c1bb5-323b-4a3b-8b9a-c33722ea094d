<?php

use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Route;
use Modules\Socialite\Http\Controllers\Api\Alipay;
use Modules\Socialite\Http\Controllers\Api\Qq;
use Modules\Socialite\Http\Controllers\Api\Tiktok;
use Modules\Socialite\Http\Controllers\Api\Wechat;
use Modules\Socialite\Http\Controllers\Api\Weibo;
use Modules\Socialite\Http\Controllers\Api\Apple;

# 支付宝
Route::post('alipay/oauth', [Alipay\AlipayController::class, 'index']);
# 抖音
Route::post('tiktok/oauth', [Tiktok\TiktokController::class, 'index']);
# QQ
Route::post('qq/oauth', [Qq\QqController::class, 'index']);
# 微博
Route::post('weibo/oauth', [Weibo\WeiboController::class, 'index']);

Route::group([
    'middleware' => 'guess:sanctum',
    'prefix'     => 'apple',
], function (Router $router) {
    $router->any('oauth', [Apple\IndexController::class, 'oauth']);
    $router->post('code', [Apple\IndexController::class, 'code']);
    $router->post('bindmobile', [Apple\IndexController::class, 'bindMobile']);
});

Route::group([
    'middleware' => 'guess:sanctum',
    'prefix'     => 'wechat',
], function (Router $router) {
    # 公众平台
    $router->post('sns/oauth', [Wechat\SnsController::class, 'oauth']);
    $router->post('sns/code', [Wechat\SnsController::class, 'code']);
    $router->post('sns/login', [Wechat\SnsController::class, 'login']);
    # 公众平台回调
    $router->any('official/callback', [Wechat\OfficialController::class, 'index'])->name('wechat.callback');
    $router->get('official/config', [Wechat\OfficialController::class, 'config'])->middleware(['auth:sanctum']);
    $router->get('official/share', [Wechat\OfficialController::class, 'share'])->middleware(['auth:sanctum']);
    # 开放平台 todo
    $router->post('open', [Wechat\OpenController::class, 'index']);
    $router->post('open/oauth', [Wechat\OpenController::class, 'oauth']);
    $router->post('open/code', [Wechat\OpenController::class, 'code']);
    $router->post('open/login', [Wechat\OpenController::class, 'login']);
    $router->post('open/bindmobile', [Wechat\OpenController::class, 'bindMobile'])->middleware('check-sms:DEFAULT');
    $router->get('open/get_appid', [Wechat\OpenController::class, 'getAppId']);
    # 小程序登录 todo
    $router->post('mini', [Wechat\MiniController::class, 'index']);
    $router->get('mini/openid', [Wechat\MiniController::class, 'openid']);//获取小程序openid
    $router->post('mini/login', [Wechat\MiniController::class, 'login']); //小程序手机号登录
});

Route::group([
    'middleware' => 'auth:sanctum',
    'prefix'     => 'wechat',
], function (Router $router) {
    $router->get('mini/share', [Wechat\MiniController::class, 'share']);//小程序邀请码
    $router->post('open/bind_wechat', [Wechat\OpenController::class, 'bindWechat']);
});
