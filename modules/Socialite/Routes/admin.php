<?php

use Illuminate\Support\Facades\Route;
use Modules\Socialite\Http\Controllers\Admin\Alipay\AlipayController;
use Modules\Socialite\Http\Controllers\Admin\Qq\QqController;
use Modules\Socialite\Http\Controllers\Admin\Tiktok\TiktokController;
use Modules\Socialite\Http\Controllers\Admin\Wechat\KeywordController;
use Modules\Socialite\Http\Controllers\Admin\Wechat\MediumController;
use Modules\Socialite\Http\Controllers\Admin\Wechat\MenuController;
use Modules\Socialite\Http\Controllers\Admin\Wechat\MessageController;
use Modules\Socialite\Http\Controllers\Admin\Wechat\WechatController;

Route::get('wechat', [WechatController::class, 'index']);
Route::get('wechat/accounts', [WechatController::class, 'accounts']);
Route::resource('wechat/menus', MenuController::class);
Route::resource('wechat/keywords', KeywordController::class);
Route::resource('wechat/messages', MessageController::class);
Route::get('wechat/media/ajax', [MediumController::class, 'ajax'])->name('wechat.media.ajax');
Route::resource('wechat/media', MediumController::class);

Route::get('alipay/accounts', [AlipayController::class, 'index']);
Route::get('weibo/accounts', [AlipayController::class, 'index']);
Route::get('qq/accounts', [QqController::class, 'index']);
Route::get('tiktok/accounts', [TiktokController::class, 'index']);
