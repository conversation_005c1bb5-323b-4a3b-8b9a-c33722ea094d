<?php

namespace Modules\Company\Providers;

use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;
use Modules\Company\Events\CompanyCreated;
use Modules\Company\Listeners\CompanyCreatedListener;

class EventServiceProvider extends ServiceProvider
{

    protected $listen = [
        CompanyCreated::class => [
            CompanyCreatedListener::class,
        ],
    ];

}