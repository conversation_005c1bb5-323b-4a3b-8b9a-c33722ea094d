<?php

namespace Modules\Company\Traits;

use App\Exceptions\ValidatorException;
use Modules\Company\Models\BusinessContact;

trait BusinessTrait
{
    public function checkBusinessById($request)
    {
        $request->kernel->validate([
            'business_id' => 'required|integer|exists:jz_company_business,id',
        ], [
            'business_id.required' => '名片id不可为空',
            'business_id.integer'  => '名片id只能是数字',
            'business_id.exists'   => '名片不存在'
        ]);
    }

    public function checkBusiness($business, $user = '', $message = '名片不存在', $msg = '只能查看自己的名片')
    {
        if (! $business) {
            throw new ValidatorException($message, 404);
        }

        if ($user) {
            if ($business->user->isNot($user)) {
                throw new ValidatorException($msg);
            }
        }
    }

    public function findLink($user_id, $business_id, $destination_id, $default_id = "")
    {
        // 检查目标方发起的联系请求
        $contact_to = BusinessContact::query()
            ->where('destination_id', $destination_id)
            ->where('from_business_id', $business_id)
            ->first();
        if ($contact_to) {
            return $contact_to->status === 0 ? 3 : 2;
        }

        $contact = BusinessContact::where('destination_id', $user_id)
            ->where('to_business_id', $business_id)
            ->where('user_id', $destination_id)
            ->first();
        if ($contact) {
            return $contact->status === 0 ? 1 : 2;
        }

        return 0;
    }

    /**
     * Notes: 检查用户是否可以添加名片,如果没有部门可以随意添加
     *
     * @Author: 玄尘
     * @Date: 2024/12/27 09:43
     * @param $user
     * @return true
     * @throws \App\Exceptions\ValidatorException
     */
    public function canDepartmentAddBusiness($user): true
    {
        if (! $user) {
            throw new ValidatorException('用户不存在');
        }
        // 没有部门可以随意添加
        if (! $user->departments()->exists()) {
            return true;
        }
        $status = $user->departments()->where('is_business', 1)->exists();
        if (! $status) {
            throw new ValidatorException('您没有添加名片的权限');
        }
        return $status;
    }

}
