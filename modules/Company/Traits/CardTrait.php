<?php

namespace Modules\Company\Traits;

use App\Exceptions\ValidatorException;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Modules\Company\Models\Business;
use Modules\Company\Models\BusinessContact;
use Modules\Company\Models\Company;
use Modules\Company\Models\Staff;

trait CardTrait
{
    /**
     * Notes: 私人名片
     *
     * @Author: 玄尘
     * @Date: 2025/4/11 16:05
     * @param $user_id
     * @return mixed
     */
    public function findFriendBusinessIds($user_id): mixed
    {
        if ($user_id instanceof Collection) {
            $user_id = $user_id->toArray();
        }

        $tableName = (new BusinessContact())->getTable();
        $userIds   = is_array($user_id) ? $user_id : [$user_id];

        return BusinessContact::whereIn('destination_id', $userIds)
            ->where('status', 1)
            ->whereExists(function ($query) use ($tableName) {
                $query->select(DB::raw(1))
                    ->from($tableName.' as c')
                    ->whereRaw("c.user_id = {$tableName}.destination_id")
                    ->whereRaw("c.to_business_id = {$tableName}.from_business_id")
                    ->where('c.status', 1)
                    ->where('c.destination_id', DB::raw("{$tableName}.user_id"));
            })
            ->pluck('from_business_id')
            ->unique()
            ->toArray();
    }

    /**
     * Notes: 企业名片
     *
     * @Author: 玄尘
     * @Date: 2025/4/11 16:05
     * @param $user
     * @return array
     */
    public function getCompanyBusinessIds($user): array
    {
        // 获取企业认证的名片以及企业朋友的业务ID
        $businessIds = [];
        $companyIds  = $user->jzBusinesses()
            ->whereHas('company', function ($query) {
                $query->where('status', Company::STATUS_PASS);
            })
            ->pluck('company_id')
            ->unique();

        $companyStaffIds = [];
        foreach ($companyIds as $company_id) {
            if ($this->isUserStaffInCompany($user, $company_id)) {
                $companyStaffIds[] = $company_id;
            }
        }

        if (! empty($companyStaffIds)) {
            $userIds     = Business::where('is_default', 0)
                ->where('is_private', 1)
                ->whereIn('company_id', $companyIds)
                ->pluck('user_id')
                ->unique();
            $businessIds = array_merge($businessIds, $this->findFriendBusinessIds($userIds));
        }

        return array_unique($businessIds);
    }

    /**
     * Notes: 当前企业的人脉
     *
     * @Author: 玄尘
     * @Date: 2024/12/26 16:57
     * @param $companyId
     * @return array
     * @throws \App\Exceptions\ValidatorException
     */
    public function getCompanySpecificBusinessIds($companyId): array
    {
        // 获取特定公司的人脉
        if (empty($companyId)) {
            throw new ValidatorException("企业ID不能为空");
        }

        $company = Company::find($companyId);
        if (! $company) {
            throw new ValidatorException("企业不存在");
        }

        $userIds = Business::where('company_id', $companyId)
            ->whereHas('company', function ($query) use ($companyId) {
                $query->where('status', Company::STATUS_PASS);
            })
            ->pluck('user_id')
            ->unique();

        return BusinessContact::query()
            ->whereIn('destination_id', $userIds)
            ->where('user_id', '<>', $company->user_id)
            ->where('status', 1)
            ->pluck('from_business_id')
            ->unique()
            ->toArray();
    }

    /**
     * Notes: 获取混合
     *
     * @Author: 玄尘
     * @Date: 2024/12/26 16:23
     * @param $user
     * @return array
     */
    public function getMixedBusinessIds($user): array
    {
        // 获取个人和企业人脉混合的业务ID
        $personal = $all = $this->findFriendBusinessIds($user->id);

        $companyIds = $user->jzBusinesses()
            ->whereHas('company', function ($query) {
                $query->where('status', Company::STATUS_PASS);
            })
            ->where('is_default', 0)
            ->pluck('company_id')
            ->unique();

        $userIds = Business::where('is_default', 0)
            ->where('is_private', 1)
            ->whereIn('company_id', $companyIds)
            ->pluck('user_id')
            ->unique();
        $company = $this->findFriendBusinessIds($userIds);

        $all = array_merge($all, $company);

        return [
            'all'      => array_unique($all),
            'personal' => array_unique($personal),
            'company'  => array_unique($company),
        ];
    }

    public function isUserStaffInCompany($user, $companyId)
    {
        // 判断用户是否为该公司员工
        return Staff::where('user_id', $user->id)
            ->where('company_id', $companyId)
            ->where('status', Staff::STATUS_APPROVED)
            ->where('is_open', 0)
            ->exists();
    }

}
