<?php

namespace Modules\Company\Http\Actions\Staff;

use Dcat\Admin\Grid\RowAction;
use Dcat\Admin\Widgets\Modal;
use Modules\Company\Http\Forms\StaffAuditForm;

class StaffAudit extends RowAction
{
    protected string $title = '审核';

    public function render(): string
    {
        return Modal::make()
            ->lg()
            ->centered()
            ->title($this->title)
            ->body(StaffAuditForm::make()->payload(['staff_id' => $this->getKey()]))
            ->button($this->title);
    }

    protected function authorize($user): bool
    {
        return $user->isAdministrator();
    }
}