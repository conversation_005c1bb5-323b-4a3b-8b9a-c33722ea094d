<?php

namespace Modules\Company\Http\Controllers\Admin;

use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;
use Modules\Company\Http\Actions\Staff\StaffAudit;
use Modules\Company\Models\Company;
use Modules\Company\Models\Staff;

class StaffController extends AdminController
{

    protected string $title = '员工列表';

    protected function grid(): Grid
    {
        return Grid::make(Staff::with(['user.info', 'company']), function (Grid $grid) {
            $grid->disableCreateButton();
            $grid->disableRowSelector();

            $grid->actions(function (Grid\Displayers\Actions $actions) {
                if ($actions->row->status == Staff::STATUS_PENDING) {
                    $actions->append(new StaffAudit);
                }
            });

            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('user_id', '绑定用户')
                    ->select()
                    ->ajax(route('admin.user.users.ajax'));
                $filter->like('name', '员工姓名');
                $filter->equal('status', '状态')->select(Staff::STATUS_MAP);
                $filter->like('mobile', '员工手机号');
                $filter->like('position', '员工职位');
                $filter->equal('company_id', '所属企业')
                    ->select(function ($query) {
                        return Company::pluck('name', 'id');
                });
            });

            $grid->actions(function (Grid\Displayers\Actions $actions) {
                $actions->disableView();
                $actions->disableDelete();
            });

            $grid->column('id', '#ID#');
            $grid->column('user', '绑定用户')->display(fn() => $this->user->show_all_name);
            $grid->column('company.name', '所属企业');
            $grid->column('name', '员工姓名');
            $grid->column('mobile', '员工手机号');
            $grid->column('position', '员工职位');
            $grid->column('status', '状态')
                ->using(Staff::STATUS_MAP)
                ->label(Staff::STATUS_LABEL);
            $grid->column('is_display_network', '展示人脉')->bool();
            $grid->column('created_at', '加入时间');
        });
    }

    protected function form(): Form
    {
        return Form::make(Staff::class, function (Form $form) {
            $form->text('name', '员工姓名');
            $form->mobile('mobile', '员工手机号');
            $form->text('position', '员工职位');
            $form->radio('is_display_network', '是否展示人脉')
                ->options([
                    1 => '是',
                    0 => '否'
                ]);
        });
    }

}
