<?php

namespace Modules\Company\Http\Controllers\Admin;

use App\Admin\Traits\WithUploads;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;
use Modules\Company\Models\Industry;

class IndustryController extends AdminController
{
    use WithUploads;

    protected string $title = '行业列表';

    protected function grid(): Grid
    {
        return Grid::make(Industry::class, function (Grid $grid) {
//            $grid->filter(function (Grid\Filter $filter) {
//                $filter->like('title', '行业名称');
//                $filter->equal('status', '状态')->radio([
//                    0 => '禁用',
//                    1 => '正常',
//                ]);
//            });
            $grid->column('id', '#ID#');
//            $grid->column('cover', '行业图片')->image('', 100, 100);
            $grid->column('title', '行业名称');
//            $grid->column('alpha', '首字母');
//            $grid->column('pinyin', '完整拼音');

            $grid->column('status', '状态')->bool();
            $grid->column('created_at', '创建时间');
        });
    }

    protected function form(): Form
    {
        return Form::make(Industry::class, function (Form $form) {
            $form->text('title', '行业名称')->rules('required');
//            $form->text('alpha', '首字母');
//            $form->text('pinyin', '完整拼音');
//            $this->cover($form, 'cover', '行业图片');

            $form->switch('status', '显示')->default(1);

            return $form;
        });
    }

}