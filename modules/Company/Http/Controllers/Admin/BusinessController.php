<?php

namespace Modules\Company\Http\Controllers\Admin;

use App\Admin\Traits\WithUploads;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;
use Modules\Company\Models\Business;
use Modules\User\Rules\MobileRule;

class BusinessController extends AdminController
{
    use WithUploads;

    protected string $title = '名片列表';

    public function grid(): Grid
    {
        return Grid::make(Business::with(['user', 'staff', 'company']), function (Grid $grid) {
            $grid->disableRowSelector();

            $grid->filter(function (Grid\Filter $filter) {
                $filter->like('name', '部门名称');
                $filter->equal('user_id', '所属用户')
                    ->select()
                    ->ajax(route('admin.user.users.ajax'));
                $filter->between('created_at', '创建时间')->datetime();
            });

            $grid->column('id', '#ID#');
            $grid->column('nickname', '姓名');
            $grid->column('user', '所属用户')
                ->display(fn() => $this->user->show_all_name);
            $grid->column('company.name', '所属公司');
            $grid->column('company_name', '公司名称');
            $grid->column('phone', '手机号');
            $grid->column('wechat', '微信');
            $grid->column('email', '邮箱');
            $grid->column('address', '地址');
            $grid->column('position', '职位');
            $grid->column('created_at');
        });
    }

    public function form(): Form
    {
        return Form::make(Business::class, function (Form $form) {
            $form->text('nickname', '姓名')->required();
            $form->text('company_name', '公司名称')->required();
            $form->text('phone', '手机号')->rules(['required', new MobileRule()]);
            $form->text('wechat', '微信');
            $form->text('email', '邮箱')->rules(['nullable', 'email']);
            $form->text('address', '地址');
            $form->text('position', '职位');
        });
    }
}