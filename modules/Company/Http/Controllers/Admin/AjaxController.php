<?php

namespace Modules\Company\Http\Controllers\Admin;

use App\Http\Controllers\Controller;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Modules\Company\Models\Company;
use Modules\Company\Models\Industry;

class AjaxController extends Controller
{

    public function index(Request $request)
    {
        $userId = $request->get('q');

        return Company::where(['user_id' => $userId, 'status' => 1])->get(['id', DB::raw('name as text')]);
    }

    /**
     * Notes   : 获取行业分类
     *
     * @Date   : 2021/11/19 16:27
     * <AUTHOR> Mr.wang
     * @param  Request  $request
     * @return mixed
     */
    public function industry(Request $request)
    {
        $type = $request->get('q');

        return Industry::OfEnabled()->get(['id', DB::raw('title as text')]);
    }

}