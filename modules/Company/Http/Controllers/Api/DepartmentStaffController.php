<?php

namespace Modules\Company\Http\Controllers\Api;

use App\Exceptions\ValidatorException;
use App\Http\Controllers\ApiController;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Modules\Company\Models\Staff;
use Modules\User\Http\Resources\DepartmentResource;
use Modules\User\Models\Department;

class DepartmentStaffController extends ApiController
{

    /**
     * Notes: 员工归属的部门
     *
     * @Author: 玄尘
     * @Date: 2024/12/26 08:59
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function departments(Request $request): JsonResponse
    {
        $user        = $request->kernel->user();
        $departments = $user->departments;
        return $request->kernel->success(DepartmentResource::collection($departments));
    }

    /**
     * Notes: 加入部门
     *
     * @Author: 玄尘
     * @Date: 2024/12/20 16:52
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     * @throws \App\Exceptions\ValidatorException
     */
    public function join(Request $request): JsonResponse
    {
        $request->kernel->validate([
            'department_id' => 'required|integer|exists:user_departments,id',
            'staff_id'      => 'required|integer|exists:Modules\Company\Models\Staff,id',
        ], [
            'department_id.required' => '缺少部门id',
            'department_id.integer'  => '部门id必须是数字',
            'department_id.exists'   => '部门id不存在',
            'staff_id.required'      => '缺少员工id',
            'staff_id.integer'       => '员工id只能是数字',
            'staff_id.exists'        => '员工不存在',
        ]);

        $user = $request->kernel->user();

        $staff_id      = $request->staff_id;
        $department_id = $request->department_id;

        $staff = Staff::find($staff_id);

        $department = Department::find($department_id);

        if (! $user->canManageStaff($department->company_id, $department_id)) {
            throw new ValidatorException('您没有权限');
        }

        if ($staff->user->isMyDepartment($department_id)) {
            throw new ValidatorException('您已经是本部成员');
        }

        $staff->user->departments()->attach($department_id);

        return $request->kernel->success('分配成功');
    }

    /**
     * Notes: 移除部门
     *
     * @Author: 玄尘
     * @Date: 2024/12/20 17:50
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     * @throws \App\Exceptions\ValidatorException
     */
    public function remove(Request $request): JsonResponse
    {
        $request->kernel->validate([
            'department_id' => 'required|integer|exists:user_departments,id',
            'staff_id'      => 'required|integer|exists:Modules\Company\Models\Staff,id',
        ], [
            'department_id.required' => '缺少部门id',
            'department_id.integer'  => '部门id必须是数字',
            'department_id.exists'   => '部门id不存在',
            'staff_id.required'      => '缺少员工id',
            'staff_id.integer'       => '员工id只能是数字',
            'staff_id.exists'        => '员工不存在',
        ]);

        $user = $request->kernel->user();

        $staff_id      = $request->staff_id;
        $department_id = $request->department_id;

        $staff = Staff::find($staff_id);

        $department = Department::find($department_id);

        if (! $user->canManageStaff($department->company_id, $department_id)) {
            throw new ValidatorException('您没有权限');
        }

        if (! $staff->isMyDepartment($department_id)) {
            throw new ValidatorException('您不是本部员工');
        }

        $staff->user->departments()->detach($department_id);

        return $request->kernel->success('移除成功');
    }

}