<?php

namespace Modules\Company\Http\Resources\Api\Business;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class BusinessStyleResource extends JsonResource
{
    public function toArray(Request $request)
    {
        return [
            'id'                 => $this->id,
            'avatar_show'        => $this->avatar_show,
            'nickname_show'      => $this->nickname_show,
            'phone_show'         => $this->phone_show,
            'company_name_show'  => $this->company_name_show,
            'position_show'      => $this->position_show,
            'company_icon_show'  => $this->company_icon_show,
            'email_show'         => $this->email_show,
            'address_show'       => $this->address_show,
            'mini_qrcode'        => $this->mini_qrcode,
            'image'              => $this->image_url,
            'layout'             => $this->layout,
            'background'         => $this->background,
            'nickname_size'      => $this->nickname_size,
            'nickname_bold'      => $this->nickname_bold,
            'nickname_color'     => $this->nickname_color,
            'phone_size'         => $this->phone_size,
            'phone_bold'         => $this->phone_bold,
            'phone_color'        => $this->phone_color,
            'company_name_size'  => $this->company_name_size,
            'company_name_bold'  => $this->company_name_bold,
            'company_name_color' => $this->company_name_color,
            'position_size'      => $this->position_size,
            'position_bold'      => $this->position_bold,
            'position_color'     => $this->position_color,
            'email_size'         => $this->email_size,
            'email_bold'         => $this->email_bold,
            'email_color'        => $this->email_color,
            'address_size'       => $this->address_size,
            'address_bold'       => $this->address_bold,
            'address_color'      => $this->address_color,
            'wiki_size'          => $this->wiki_size,
            'wiki_bold'          => $this->wiki_bold,
            'wiki_color'         => $this->wiki_color,
        ];
    }
}