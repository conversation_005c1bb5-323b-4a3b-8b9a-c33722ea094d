<?php

namespace Modules\Company\Http\Resources\Api\Business;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Modules\Company\Http\Resources\Api\UserBaseResource;

class BusinessResource extends JsonResource
{
    public function toArray(Request $request)
    {
        return [
            'id'            => $this->id,
            'avatar'        => $this->avatar_url,
            'nickname'      => $this->nickname,
            'company_name'  => $this->company_name,
            'company_icon'  => $this->company_icon,
            'phone'         => $this->phone,
            'wechat'        => $this->wechat,
            'email'         => $this->email,
            'address'       => $this->address,
            'introduction'  => $this->introduction,
            'annex'         => $this->annex,
            'annex_name'    => $this->annex_name,
            'business'      => $this->getBusiness(),
            'business_type' => $this->business_type,
            'company'       => $this->getCompany(),
            'company_type'  => $this->company_type,
            'position'      => $this->position,
            'wiki'          => $this->wiki,
            'is_default'    => $this->is_default,
            'is_private'    => $this->is_private,
            'province'      => $this->province,
            'city'          => $this->city,
            'area'          => $this->area,
            'lat'           => $this->lat,
            'lng'           => $this->lng,
            'can'           => $this->getCan($request->kernel->user()),
            'style'         => new BusinessStyleResource($this->style),
            'user'          => new UserBaseResource($this->user),
        ];
    }
}