<?php

namespace Modules\Company\Http\Resources\Api\Card;

use App\Http\Resources\BaseWateCollection;
use Illuminate\Http\Request;

class CardCollection extends BaseWateCollection
{
    public $personalBusinessIds;

    public function __construct($resource, $personalBusinessIds)
    {
        parent::__construct($resource);

        $this->personalBusinessIds = $personalBusinessIds;
    }

    public function toArray(Request $request)
    {
        return array_merge([
            'data' => $this->collection->map(function ($info) {
                $info->from = in_array($info->id, $this->personalBusinessIds) ? "私人" : "企业";
                return new CardResource($info);
            }),
        ], $this->page());
    }
}