<?php

namespace Modules\Company\Http\Resources\Api\Contact;

use App\Http\Resources\BaseWateCollection;
use Illuminate\Http\Request;

class BusinessContactCollection extends BaseWateCollection
{
    public function toArray(Request $request)
    {
        return array_merge([
            'data' => $this->collection->map(function ($item) {
                return new BusinessContactResource($item);
            }),
        ], $this->page());
    }
}