<?php

namespace Modules\Company\Http\Resources\Api\BusinessBrowseRecord;

use Illuminate\Http\Resources\Json\JsonResource;
use Modules\Company\Models\BusinessContact;

class RecordMyResource extends JsonResource
{

    public function toArray($request): array
    {
        $data = [
            'to_id'             => $this->business_id,
            'visitor_time'      => (string) $this->created_at,
            'is_realname_check' => $this->user->realName && $this->user->realName->status == 1,
        ];
        if ($this->user->jzBusiness) {
            $business  = $this->user->jzBusiness;
            $otherData = [
                'id'            => $business->id,
                'nickname'      => $business->nickname,
                'is_work'       => $business->isWork(),
                'is_company'    => $business->isCompany(),
                'avatar'        => $business->avatar,
                'count'         => $business->count,
                'company_name'  => $business->company_name,
                'annex'         => $business->annex ?? '',
                'annex_name'    => $business->annex_name ?? '',
                'position'      => $business->position,
                'change_status' => BusinessContact::getChangeStatus(
                    $this->user_id,
                    $business->id,
                    $this->business->user_id
                ),
            ];
        } else {
            $otherData = [
                'change_status' => BusinessContact::STATUS_NO_BUSINESS,
                'nickname'      => $this->user->info->nickname ?? '',
                'avatar'        => $this->user->info->avatar ?? '',
            ];
        }

        return array_merge($otherData, $data);
    }

}
