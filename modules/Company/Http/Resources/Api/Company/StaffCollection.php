<?php

namespace Modules\Company\Http\Resources\Api\Company;

use App\Http\Resources\BaseWateCollection;
use Illuminate\Http\Request;

class StaffCollection extends BaseWateCollection
{
    public function toArray(Request $request)
    {
        return array_merge([
            'data' => $this->collection->map(function ($item) {
                return new StaffResource($item);
            }),
        ], $this->page());
    }
}