<?php

namespace Modules\Company\Http\Resources\Api\Company;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class CompanyBaseResource extends JsonResource
{
    public function toArray(Request $request)
    {
        return [
            'id'            => $this->id,
            'name'          => $this->name,
            'certification' => new CertificationResource($this->certification),
            'can'           => $this->getCan($request->kernel->user()),
            'created_at'    => (string) $this->created_at,
        ];
    }
}