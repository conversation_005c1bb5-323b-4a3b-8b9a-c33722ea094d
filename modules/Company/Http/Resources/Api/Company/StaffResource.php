<?php

namespace Modules\Company\Http\Resources\Api\Company;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Modules\Company\Http\Resources\Api\Roles\RolesResource;
use Modules\User\Http\Resources\DepartmentBaseResource;

class StaffResource extends JsonResource
{
    public function toArray(Request $request)
    {
        return [
            'id'          => $this->id,
            'user_id'     => $this->user_id,
            'nickname'    => $this->name,
            'position'    => $this->position,
            'departments' => DepartmentBaseResource::collection($this->departments),
            'roles'       => RolesResource::collection($this->roles),
            'is_manage'   => $this->user->isManagementRole($this->company_id),
            'mobile'      => $this->mobile,
            'wechat'      => $this->business->wechat ?? '',
            'avatar'      => $this->business->avatar ?? '',
            'type_name'   => $this->getTypeName($this->type),
            'status'      => [
                'value' => $this->status,
                'text'  => $this->status_text,
            ],
            'created_at'  => (string) $this->created_at,
        ];
    }
}