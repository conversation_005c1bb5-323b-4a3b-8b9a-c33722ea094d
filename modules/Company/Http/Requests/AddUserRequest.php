<?php

namespace Modules\Company\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;
use Modules\User\Rules\MobileRule;

class AddUserRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'company_id' => 'required|integer',
            'nickname'   => 'required',
            //            'wechat'     => 'required',
            'phone'      => ['required', new MobileRule()],
            'position'   => 'required',
            'email'      => 'nullable|email',
            //            'address'    => 'required',
            //            'wiki'       => 'required',
            //            'industry'   => 'required',
        ];
    }

    public function messages(): array
    {
        return [
            'company_id.required' => '请选择要生成邀请码的企业id',
            'company_id.integer'  => '企业id只能是数字',
            'nickname.required'   => '昵称不能为空',
            'wechat.required'     => '微信不能为空',
            'phone.required'      => '手机不能为空',
            'position.required'   => '职位不能为空',
            'email.required'      => '邮箱不能为空',
            'address.required'    => '地址不能为空',
            'wiki.required'       => 'wiki地址不能为空',
            'industry.required'   => '所属行业不能为空',
        ];
    }

}
