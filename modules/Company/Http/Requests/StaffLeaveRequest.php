<?php

namespace Modules\Company\Http\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StaffLeaveRequest extends FormRequest
{

    public function rules(): array
    {
        return [
            'staff_id' => 'required|integer|exists:jz_company_staff,id',
            'type'     => 'required|in:1,2,3',
        ];
    }

    public function messages(): array
    {
        return [
            'staff_id.required' => '员工id不能为空',
            'staff_id.integer'  => '员工id只能是数字',
            'staff_id.exists'   => '员工id只能是数字',
            'type.required'     => '类型不能为空',
            'type.in'           => '类型错误',
        ];
    }

}