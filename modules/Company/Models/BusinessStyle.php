<?php

namespace Modules\Company\Models;

use App\Models\Model;
use App\Traits\HasCovers;
use Modules\Company\Models\Traits\BelongsToBusiness;

class BusinessStyle extends Model
{
    use BelongsToBusiness, HasCovers;

    protected $table = 'jz_company_business_styles';

    const STYLE_MAPPINGS = [
        'avatar_show'       => 'avatar',
        'phone_show'        => 'phone',
        'company_name_show' => 'company_name',
        'position_show'     => 'position',
        'nickname_show'     => 'nickname',
        'company_icon_show' => 'company_icon',
        'email_show'        => 'email',
        'address_show'      => 'address'
    ];

    const EXCLUDED_FIELDS = [
        'user_id',
        'is_default',
        'mini_qrcode',
        'created_at',
        'updated_at',
        'deleted_at',
        'style',
        'user',
    ];

    public static function processData(array $data): array
    {
        $style = $data['style'] ?? [];
        $data  = array_merge($data, $style);
        unset($data['style']);
        foreach (self::STYLE_MAPPINGS as $styleKey => $dataKey) {
            if (! empty($style[$styleKey])) {
                $data[$dataKey] = '';
            }
        }

        return array_diff_key($data, array_flip(self::EXCLUDED_FIELDS));
    }

    public function getImageUrlAttribute(): string
    {
        return $this->parseImageUrl($this->image);
    }
}