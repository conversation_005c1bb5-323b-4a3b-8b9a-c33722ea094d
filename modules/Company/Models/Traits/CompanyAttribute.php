<?php

namespace Modules\Company\Models\Traits;

use App\Exceptions\ValidatorException;
use Modules\Company\Models\BusinessContact;
use Modules\Company\Models\Staff;

trait CompanyAttribute
{

    public function getIsCheckAttribute(): true
    {
        return $this->certification()->where('status', 1)->exists();
    }

    /**
     * Notes: 企业认证
     *
     * @Author: 玄尘
     * @Date: 2024/12/25 13:24
     */
    public static function doCertification($name, $legalName, $enterpriseNo)
    {
        $companyData = alibaba(
            url: 'https://juccvvt.market.alicloudapi.com/enterprise/business/base',
            paramsType: 'form_params',
            params: [
                'keyword' => $name,
            ],
            method: 'POST',
        );

        if ($companyData['LegalPerson'] != $legalName) {
            throw new ValidatorException("法人姓名错误");
        }

        if ($companyData['CreditNo'] != $enterpriseNo) {
            throw new ValidatorException("企业社会统一信用代码错误");
        }
        return $companyData;
    }

    /**
     * Notes: 在公司下能做的事情
     *
     * @Author: 玄尘
     * @Date: 2024/12/26 10:30
     * @param $user
     * @return array
     */
    public function getCan($user): array
    {
        $departmentIds = $this->departments()->pluck('id')->toArray();

        return [
            'staff'      => $user->canManageStaff($this->id, $departmentIds),//是否可以管理员工
            'knowledge'  => $user->canManageKnowledge($this->id, $departmentIds),//是否可以管理知识库
            'role'       => $user->canManageRole($this->id),//是否可以分配职位
            'department' => $user->canManageRole($this->id),//是否可以创建部门
        ];
    }

    /**
     * Notes: 获取公司统计数据
     *
     * @Author: 玄尘
     * @Date: 2024/12/26 11:12
     * @param  int  $company_id
     * @return array
     */
    public function getData(int $company_id = 0): array
    {
        $company_id = $company_id ?: $this->id;
        $company    = self::find($company_id);

        return [
            //在职人员数
            'in_job'    => $company->staffs()->where('status', Staff::STATUS_APPROVED)->count(),
            //离职人员数
            'out_job'   => $company->staffs()->where('status', Staff::STATUS_LEAVE)->count(),
            //待审核成员数
            'is_check'  => $company->staffs()->where('status', Staff::STATUS_PENDING)->count(),
            //待激活成员数
            'is_active' => $company->staffs()->where('status', Staff::STATUS_PENDING)->count(),
            //企业人脉总数
            'contact'   => BusinessContact::query()
                ->whereHas('business', function ($query) use ($company) {
                    $query->where('company_id', $company->id);
                })
                ->where('status', 1)
                ->count(),
        ];
    }
}