<?php

namespace Modules\Company\Models;

use App\Exceptions\ValidatorException;
use App\Models\CompanyDepartmentRole;
use App\Models\Model;
use App\Models\User;
use Illuminate\Database\Eloquent\Relations\BelongsToMany;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;

class Role extends Model
{
    use SoftDeletes;

    protected $table = 'jz_company_roles';

    const LABEL_ADMINISTRATOR     = 'administrator';
    const LABEL_SUB_ADMINISTRATOR = 'sub_administrator';
    const LABEL_MANAGER           = 'manager';
    const LABEL_KNOWLEDGE         = 'knowledge';
    const LABEL_ROLES_MAP         = [
        self::LABEL_ADMINISTRATOR     => '超级管理员',
        self::LABEL_SUB_ADMINISTRATOR => '副超级管理员',
        self::LABEL_MANAGER           => '部门主管',
        self::LABEL_KNOWLEDGE         => '知识库管理员',
    ];

    /**
     * 管理角色标签
     */
    const MANAGEMENT_ROLES = [self::LABEL_ADMINISTRATOR, self::LABEL_SUB_ADMINISTRATOR];

    /**
     * 可管理知识库角色标签
     */
    const KNOWLEDGE_MANAGEMENT_ROLES = [self::LABEL_MANAGER, self::LABEL_KNOWLEDGE];

    public function users(): BelongsToMany
    {
        return $this->belongsToMany(User::class, 'company_department_role')
            ->withPivot(['position'])
            ->withTimestamps();
    }

    public function companyDepartmentRoles(): HasMany
    {
        return $this->hasMany(CompanyDepartmentRole::class, 'company_role_id');
    }

    /**
     * Notes: 是否可以增加
     *
     * @Author: 玄尘
     * @Date: 2024/12/19 14:41
     * @param $user
     * @param $staff
     * @param $department
     * @return bool
     * @throws \App\Exceptions\ValidatorException
     */
    public function canJoin($user, $staff, $department): bool
    {
        if ($this->label == self::LABEL_ADMINISTRATOR) {
            throw new ValidatorException('不可分配此角色');
        }

        if ($this->label == self::LABEL_SUB_ADMINISTRATOR && ! $user->isAdministrator()) {
            throw new ValidatorException('您不是超级管理员不可分配此职位');
        }

        if ($this->lable == self::LABEL_MANAGER) {
            if ($this->companyDepartmentRoles()->exists()) {
                throw new ValidatorException('部门主管只能有一位');
            }
        }
        if ($department) {
            if ($staff->company->isNot($department->company)) {
                throw new ValidatorException('不可分配');
            }
            $departmentId = $this->getDepartmentId($department->id);
            $exists       = $staff->user->companyDepartmentRoles()
                ->where('department_id', $departmentId)
                ->where('company_role_id', $this->id)
                ->exists();
            if ($exists) {
                throw new ValidatorException('此员工已有此角色');
            }
        }

        return true;
    }

    /**
     * Notes: description
     *
     * @Author: 玄尘
     * @Date: 2024/12/26 08:56
     * @param $user
     * @param $staff
     * @param $department
     * @return true
     * @throws \App\Exceptions\ValidatorException
     */
    public function canLeave($user, $staff): true
    {
        if ($staff->user->isAdministrator()) {
            throw new ValidatorException('您没有权限移除此人权限');
        }

        if ($this->label == self::LABEL_ADMINISTRATOR) {
            throw new ValidatorException('您没有权限移除此权限');
        }

        if ($this->label == self::LABEL_SUB_ADMINISTRATOR && ! $user->isAdministrator()) {
            throw new ValidatorException('您不是超级管理员不可操作此权限');
        }

        return true;
    }

    public function getDepartmentId($departmentId): int
    {
        return $this->label == self::LABEL_SUB_ADMINISTRATOR ? 0 : $departmentId;
    }
}
