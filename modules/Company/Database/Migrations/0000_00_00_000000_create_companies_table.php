<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Modules\Company\Models\Company;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create($this->getTableName(), function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id');
            $table->unsignedBigInteger('industry_id')
                ->default(0)
                ->comment('所属行业');
            $table->string('name')
                ->comment('公司名称');
            $table->string('logo')
                ->nullable();
            $table->string('description')
                ->nullable()
                ->comment('公司描述');
            $table->easyStatus();
            $table->timestamps();
            $table->softDeletes()
                ->index();
            $table->index('created_at');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists($this->getTableName());
    }

    public function getTableName(): string
    {
        return (new Company())->getTable();
    }
};
