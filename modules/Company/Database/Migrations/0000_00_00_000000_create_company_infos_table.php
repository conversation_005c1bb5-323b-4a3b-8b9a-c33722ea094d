<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Modules\Company\Models\CompanyInfo;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create($this->getTableName(), function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('company_id')->index();
            $table->string('code')
                ->nullable()
                ->comment('邀请码');
            $table->dateTime('exp_time')
                ->nullable()
                ->comment('邀请码到期时间');
            $table->boolean('is_open')->default(0)->comment('是否开启邀请码');
            $table->string('business_type')
                ->nullable()
                ->comment('业务类型 0组合 1图片 2视频');
            $table->string('business')
                ->nullable()
                ->comment('业务介绍');
            $table->string('company_type')
                ->nullable()
                ->comment('企业介绍类型： 0组合 1图片 2视频');
            $table->string('company')
                ->nullable()
                ->comment('企业介绍');

            $table->timestamps();
            $table->softDeletes();
            $table->index('created_at');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists($this->getTableName());
    }

    public function getTableName(): string
    {
        return (new CompanyInfo())->getTable();
    }
};
