<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Modules\Company\Models\Staff;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create($this->getTableName(), function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('company_id')->comment('企业id')->index();
            $table->unsignedBigInteger('user_id')->comment('用户id')->index();
            $table->unsignedBigInteger('business_id')->default(0)->comment('名片id')->index();
            $table->string('name');
            $table->string('mobile');
            $table->string('position');
            $table->easyStatus();
            $table->cover();
            $table->boolean('type')->comment('1:邀请码 2工牌 3名片 4营业执照 0创始人 9待激活');
            $table->boolean('is_display_network')->default(0)->comment('是否展示人脉');
            $table->string('reject_reason')
                ->nullable()
                ->comment('拒绝原因');
            $table->timestamps();
            $table->index('created_at');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists($this->getTableName());
    }

    public function getTableName(): string
    {
        return (new Staff())->getTable();
    }
};
