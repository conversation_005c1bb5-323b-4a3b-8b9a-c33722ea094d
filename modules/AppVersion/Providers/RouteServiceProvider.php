<?php

namespace Modules\AppVersion\Providers;

use Illuminate\Support\Facades\Route;
use Illuminate\Foundation\Support\Providers\RouteServiceProvider as ServiceProvider;

class RouteServiceProvider extends ServiceProvider
{
    protected string $moduleNamespace = 'Modules\AppVersion\Http\Controllers';

    public function map(): void
    {
        $this->mapAdminRoutes();
        $this->mapApiRoutes();
    }

    protected function mapAdminRoutes(): void
    {
        Route::as(config('admin.route.as').'app_version.')
            ->domain(config('admin.route.domain'))
            ->middleware(config('admin.route.middleware'))
            ->namespace($this->moduleNamespace.'\Admin')
            ->prefix(config('admin.route.prefix').'/app_version')
            ->group(__DIR__.'/../Routes/admin.php');
    }

    protected function mapApiRoutes(): void
    {
        Route::as(config('api.route.as').'app_version.')
            ->domain(config('api.route.domain'))
            ->middleware(config('api.route.middleware'))
            ->namespace($this->moduleNamespace.'\Api')
            ->prefix(config('api.route.prefix').'/app_version')
            ->group(__DIR__.'/../Routes/api.php');
    }
}
