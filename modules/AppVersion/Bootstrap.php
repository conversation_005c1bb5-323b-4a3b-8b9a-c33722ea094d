<?php

namespace Modules\AppVersion;

use App\Contracts\ModuleBootstrap;
use Illuminate\Support\Facades\Artisan;

class Bootstrap implements ModuleBootstrap
{
    protected static string $menuKey = 'app-version-module';

    public static function install(): void
    {
        Artisan::call('migrate', [
            '--path' => 'modules/AppVersion/Database/Migrations',
        ]);
        self::createAdminMenu();
    }

    public static function uninstall(): void
    {
        Artisan::call('migrate:reset', [
            '--path' => 'modules/AppVersion/Database/Migrations',
        ]);

        self::deleteAdminMenu();
    }

    public static function upgrade(): void
    {
        // TODO: Implement upgrade() method.
    }
    
    public static function createAdminMenu(): void
    {
        $menuModel = config('admin.database.menu_model');

        $main = $menuModel::create([
            'parent_id' => 0,
            'order'     => 20,
            'title'     => 'APP版本管理',
            'icon'      => 'fa-telegram '.self::$menuKey,
        ]);

        $main->children()->createMany([
            [
                'order' => 1,
                'title' => '版本列表',
                'icon'  => 'fa-bars '.self::$menuKey,
                'uri'   => 'app_version/versions',
            ],
        ]);
    }

    public static function deleteAdminMenu(): void
    {
        $menuModel = config('admin.database.menu_model');
        $menuModel::where('icon', 'like', '%'.self::$menuKey)->delete();
    }
}