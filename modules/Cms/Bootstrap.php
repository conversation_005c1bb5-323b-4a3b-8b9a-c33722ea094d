<?php

namespace Modules\Cms;

use App\Contracts\ModuleBootstrap;
use Illuminate\Support\Facades\Artisan;

class Bootstrap implements ModuleBootstrap
{
    protected static string $menuKey = 'cms-module';

    public static function install(): void
    {
        Artisan::call('migrate', [
            '--path' => 'modules/Cms/Database/Migrations',
        ]);

        Artisan::call('module:seed Cms');

        self::createAdminMenu();
    }

    public static function uninstall(): void
    {
        Artisan::call('migrate:reset', [
            '--path' => 'modules/Cms/Database/Migrations',
        ]);
        self::deleteAdminMenu();
    }

    public static function upgrade(): void
    {
        self::deleteAdminMenu();
        Artisan::call('migrate', [
            '--path' => 'modules/Cms/Database/Migrations',
        ]);
        self::createAdminMenu();
    }

    public static function createAdminMenu(): void
    {
        $menuModel = config('admin.database.menu_model');

        $main = $menuModel::create([
            'parent_id' => 0,
            'order'     => 10,
            'title'     => '内容管理',
            'icon'      => 'fa-book '.self::$menuKey,
        ]);

        $main->children()->createMany([
            [
                'order' => 0,
                'title' => '内容看板',
                'icon'  => 'fa-dashboard '.self::$menuKey,
                'uri'   => 'cms',
            ],
            [
                'order' => 1,
                'title' => '内容列表',
                'icon'  => 'fa-bars '.self::$menuKey,
                'uri'   => 'cms/contents',
            ],
            [
                'order' => 2,
                'title' => '分类管理',
                'icon'  => 'fa-share-alt '.self::$menuKey,
                'uri'   => 'cms/categories',
            ],
            [
                'order' => 3,
                'title' => '标签管理',
                'icon'  => 'fa-tags '.self::$menuKey,
                'uri'   => 'cms/tags',
            ],
            [
                'order' => 4,
                'title' => '单页管理',
                'icon'  => 'fa-file-pdf-o '.self::$menuKey,
                'uri'   => 'cms/pages',
            ],
            [
                'order' => 5,
                'title' => '素材管理',
                'icon'  => 'fa-picture-o '.self::$menuKey,
                'uri'   => 'cms/materials',
            ],
        ]);
    }

    public static function deleteAdminMenu(): void
    {
        $menuModel = config('admin.database.menu_model');
        $menuModel::where('icon', 'like', '%'.self::$menuKey)->delete();
    }
}