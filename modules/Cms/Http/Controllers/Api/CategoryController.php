<?php

namespace Modules\Cms\Http\Controllers\Api;

use App\Http\Controllers\ApiController;
use Illuminate\Contracts\Database\Eloquent\Builder;
use Illuminate\Http\JsonResponse;
use Modules\Cms\Http\Requests\CategoryRequest;
use Modules\Cms\Http\Resources\CategoryResource;
use Modules\Cms\Models\Category;

class CategoryController extends ApiController
{
    public function index(CategoryRequest $request): JsonResponse
    {
        $resource = Category::select(['id','name','parent_id','description','cover'])
            ->shown()->ordered()
            ->when($request->parent_id, function (Builder $query, $pid) {
                $query->where('parent_id', $pid);
            })
            ->when($request->name, function (Builder $query, $name) {
                $query->where('name', 'like', "%$name%");
            })
            ->get();

        return $this->success(CategoryResource::collection($resource));
    }
}