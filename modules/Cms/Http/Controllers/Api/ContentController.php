<?php

namespace Modules\Cms\Http\Controllers\Api;

use App\Http\Controllers\ApiController;
use Illuminate\Contracts\Database\Eloquent\Builder;
use Illuminate\Http\JsonResponse;
use Modules\Cms\Http\Requests\ContentRequest;
use Modules\Cms\Http\Resources\ContentCollection;
use Modules\Cms\Http\Resources\ContentResource;
use Modules\Cms\Models\Content;

class ContentController extends ApiController
{
    public function index(ContentRequest $request): JsonResponse
    {
        $list = Content::ofEnabled()
            ->ofPassed()
            ->select([
                'id', 'title', 'category_id', 'sub_title', 'description', 'cover',
                'pictures', 'video', 'clicks', 'created_at',
            ])
            ->with([
                'category' => function (Builder $query) {
                    $query->select(['id', 'name']);
                },
            ])
            ->when($request->category_id, function (Builder $query, $categoryId) {
                $query->where('category_id', $categoryId);
            })
            ->when($request->tag, function (Builder $query, $tag) {
                $query->whereHas('tags', function (Builder $builder) use ($tag) {
                    $builder->whereIn('cms_tags.id', explode(',', $tag));
                });
            })
            ->when($request->title, function (Builder $query, $title) {
                $query->where('title', 'like', "%$title%");
            })
            ->orderByDesc('sort')
            ->latest()
            ->paginate($request->limit);

        return $this->success(new ContentCollection($list));
    }

    public function show(Content $content): JsonResponse
    {
        if ($content->isDisabled() || ! $content->isPassed()) {
            return $this->failed();
        }
        $content->increment('clicks');
        return $this->success(new ContentResource($content));
    }
}
