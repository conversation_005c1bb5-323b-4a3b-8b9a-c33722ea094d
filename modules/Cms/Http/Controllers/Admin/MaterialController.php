<?php

namespace Modules\Cms\Http\Controllers\Admin;

use App\Admin\Traits\WithUploads;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;
use Modules\Cms\Models\Material;

class MaterialController extends AdminController
{
    use WithUploads;

    protected string $title = '素材管理';

    public function grid(): Grid
    {
        return Grid::make(Material::latest(), function (Grid $grid) {
            $grid->showBatchDelete();

            $grid->quickSearch(['name'])
                ->placeholder('素材名称');

            $grid->filter(function (Grid\Filter $filter) {
                $filter->like('name', '素材名称');
                $filter->between('created_at', '创建时间')
                    ->datetime();
            });

            $grid->column('id');
            $grid->column('name', '素材名称');
            $grid->column('cover', '素材图片')
                ->thumb();
            $grid->column('路径')
                ->display(fn() => $this->cover_url)
                ->copyable();
        });
    }

    public function form(): Form
    {
        return Form::make(Material::class, function (Form $form) {
            $form->text('name', '素材名称')
                ->width(4)
                ->required();
            if ($cover = $form->model()->cover ?? null) {
                $this->updateCover($form, $cover)
                    ->width(4)
                    ->required();
            } else {
                $this->cover($form, 'cover')
                    ->width(4)
                    ->required();
            }
        });
    }

    protected function updateCover(
        Form $form,
        string $cover = null
    ): Form\Field\Image {
        $pathInfo = pathinfo($cover);
        return $form->image('cover', '封面图')
            ->removable(false)
            ->downloadable()
            ->autoUpload()
            ->thumbnail('thumb', config('admin.thumb_size'), config('admin.thumb_size'))
            ->accept($pathInfo['extension'])
            ->move($pathInfo['dirname'], $pathInfo['basename'])
            ->override()
            ->help('如果是更新图片素材，新素材必须和原图片后缀保持一致，如果文件系统开通了CDN缓存，请去 <a href="https://cdnnext.console.aliyun.com/refresh/cache">手动刷新</a> 图片缓存。');
    }
}
