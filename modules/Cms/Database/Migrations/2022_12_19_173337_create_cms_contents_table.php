<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('cms_contents', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('category_id')
                ->index();
            $table->string('title')
                ->comment('标题');
            $table->json('sub_title')
                ->nullable()
                ->comment('标签');
            $table->string('description')
                ->nullable()
                ->comment('简介');
            $table->cover();
            $table->pictures();
            $table->string('video')
                ->nullable()
                ->comment('视频地址');
            $table->longText('content')
                ->comment('内容');
            $table->unsignedInteger('clicks')
                ->default(0)
                ->comment('点击数');
            $table->easyStatus();
            $table->easyApply();
            $table->integer('sort')
                ->default(0)
                ->comment('排序，数字越大越靠前');
            $table->timestamps();
            $table->softDeletes()
                ->index();

            $table->index('created_at');
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('cms_contents');
    }
};
