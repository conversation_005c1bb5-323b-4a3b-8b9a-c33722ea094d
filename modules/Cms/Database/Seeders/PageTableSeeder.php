<?php

namespace Modules\Cms\Database\Seeders;

use Illuminate\Database\Seeder;
use Modules\Cms\Enums\ContentDisplayType;
use Modules\Cms\Models\Page;

class PageTableSeeder extends Seeder
{
    public function run(): void
    {
        $seeds = [
            [
                'slug'    => 'secret',
                'title'   => '隐私条款',
                'content' => '隐私条款',
                'status'  => ContentDisplayType::SHOW,
            ],
            [
                'slug'    => 'protocol',
                'title'   => '服务协议',
                'content' => '服务协议',
                'status'  => ContentDisplayType::SHOW,
            ],
        ];

        foreach ($seeds as $seed) {
            Page::create($seed);
        }
    }
}
