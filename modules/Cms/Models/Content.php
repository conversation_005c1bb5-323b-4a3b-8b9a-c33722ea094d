<?php

namespace Modules\Cms\Models;

use App\Enums\ApplyStatus;
use App\Jobs\Interaction\PostBlogJob;
use App\Models\Model;
use App\Models\SensitiveWord;
use App\Notifications\ContentPassNotification;
use App\Notifications\ContentRejectNotification;
use App\Traits\HasCovers;
use App\Traits\HasEasyApply;
use App\Traits\HasEasyStatus;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Lottery;
use Modules\Interaction\Models\Comment;
use Modules\Interaction\Models\Favorite;
use Modules\Interaction\Models\Like;

class Content extends Model
{
    use HasCovers,
        HasEasyStatus,
        HasEasyApply,
        SoftDeletes;

    const POSITION_TOP          = 1;
    const POSITION_CENTER_LEFT  = 2;
    const POSITION_CENTER_RIGHT = 3;
    const POSITION_UNDER_LEFT   = 4;

    const POSITION_MAP = [
        self::POSITION_TOP          => '首页顶部',
        self::POSITION_CENTER_LEFT  => '首页中左',
        self::POSITION_CENTER_RIGHT => '首页中右',
        self::POSITION_UNDER_LEFT   => '首页底左',
    ];

    protected $table = 'cms_contents';
    protected $casts = [
        'sub_title' => 'json',
    ];

    public function category(): BelongsTo
    {
        return $this->belongsTo(Category::class);
    }

//    public function coverUrl(): Attribute
//    {
//        $imageUrl = $this->getAttribute($this->getCoverField());
//        if (config('storage.AUTO_MAKE_THUMB')) {
//            $imageUrl = Str::beforeLast($imageUrl, '.').'-thumb.'.Str::afterLast($imageUrl, '.');
//            if (! Storage::has($imageUrl)) {
//                $imageUrl = $this->getAttribute($this->getCoverField());
//            }
//        }
//        return new Attribute(
//            get: fn() => $this->parseImageUrl($imageUrl)
//        );
//    }

    public function setClicksAttribute($value): void
    {
        Cache::increment('cms_content_clicks_'.$this->getKey());
        if (Lottery::odds(1, 10)->choose()) {
            $this->attributes['clicks'] = Cache::get('cms_content_clicks_'.$this->getKey(), 0);
        }
    }

    public function getClicksAttribute(): int
    {
        return Cache::get('cms_content_clicks_'.$this->getKey(), 0);
    }

    public function getTitle(): string
    {
        return $this->title;
    }

    public function sensitivePass()
    {
        $this->status       = 1;
        $this->apply_status = ApplyStatus::PASS;
        $this->passed_at    = now();
        $this->save();
        $this->reviewer->notify(new ContentPassNotification($this));
        PostBlogJob::dispatch($this);
    }

    public function sensitiveReject()
    {
        $this->apply_status = ApplyStatus::REJECT;
        $this->rejected_at  = now();
        $this->save();
        $this->reviewer->notify(new ContentRejectNotification($this));
    }

    public function getSensitive()
    {
        return $this->title.'|'.strip_tags($this->description).'|'.strip_tags($this->content);
    }

    public function sensitive(): MorphMany
    {
        return $this->morphMany(SensitiveWord::class, 'target');
    }

    public function comments(): MorphMany
    {
        return $this->morphMany(Comment::class, 'commentable');
    }

    public function likes(): MorphMany
    {
        return $this->morphMany(Like::class, 'likeable');
    }

    public function favorites(): MorphMany
    {
        return $this->morphMany(Favorite::class, 'favoriteable');
    }

    public function tags()
    {
        return $this->belongsToMany(Tag::class, 'cms_taggable');
    }

}
