<?php

namespace Modules\Interaction\Providers;

use Illuminate\Database\Eloquent\Relations\Relation;
use Illuminate\Support\ServiceProvider;
use Modules\Interaction\Models\Comment;

class InteractionServiceProvider extends ServiceProvider
{
    protected string $moduleName = 'Interaction';

    protected string $moduleNameLower = 'interaction';

    public function boot(): void
    {
        Relation::morphMap([
            'interaction_comment' => Comment::class,
        ]);
    }

    public function register(): void
    {
        $this->registerConfig();
    }

    protected function registerConfig(): void
    {
        //        Config::set('horizon.defaults.interaction', [
        //            'connection'          => 'redis',
        //            'queue'               => ['interaction'],
        //            'balance'             => 'auto',
        //            'autoScalingStrategy' => 'time',
        //            'maxProcesses'        => 5,
        //            'tries'               => 1,
        //        ]);
    }

    public function provides(): array
    {
        return [];
    }
}
