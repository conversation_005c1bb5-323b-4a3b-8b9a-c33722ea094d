<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('interaction_follows', function (Blueprint $table) {
            $table->id();
            $table->user();
            $table->morphs('followable');
            $table->timestamp('accepted_at');
            $table->timestamps();
            $table->index(['created_at']);

            $table->index(['followable_type', 'accepted_at']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('interaction_follows');
    }
};
