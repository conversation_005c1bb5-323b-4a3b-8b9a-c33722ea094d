<?php

namespace Modules\Interaction\Http\Controllers\Api;

use App\Facades\Api;
use App\Http\Controllers\ApiController;
use App\Models\User;
use App\Models\ZoneGoods;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Modules\Interaction\Http\Requests\InteractionRequest;
use Modules\Interaction\Http\Requests\SubscribeRequest;
use Modules\Interaction\Http\Resources\SubscribeCollection;
use Modules\Interaction\Http\Resources\SubscribeNoGroupCollection;
use Modules\Interaction\Models\Subscribe;
use Modules\Mall\Models\Category;
use Modules\Mall\Models\Goods;

class SubscribeController extends ApiController
{

    public function appIndex(Request $request): JsonResponse
    {
        $user = Api::user();
        if ($request->hidden_user_id) {
            $user = User::find($request->hidden_user_id);
        }
        $lists = Subscribe::ofUser($user)
            ->when($request->keyword, function (Builder $builder, $keyword) {
                $builder->whereHasMorph('subscribable', [Goods::class], function (Builder $query) use ($keyword) {
                    $query->where('name', 'like', "%{$keyword}%");
                })
                    ->orWhereHasMorph('subscribable', [ZoneGoods::class], function (Builder $query) use ($keyword) {
                        $query->whereHas('goods', function ($q) use ($keyword) {
                            $q->where('name', 'like', "%{$keyword}%");
                        });
                    });
            })
            ->select('*', DB::raw('DATE_FORMAT(created_at,"%Y年%m月%d日") as look_at'))
            ->latest()
            ->paginate($request->limit ?: 10);

        return $this->success(new SubscribeNoGroupCollection($lists, true, false));
    }
    public function index(Request $request): JsonResponse
    {
        $user = Api::user();
        if ($request->hidden_user_id) {
            $user = User::find($request->hidden_user_id);
        }
        $lists = Subscribe::ofUser($user)
            ->when($request->keyword, function (Builder $builder, $keyword) {
                $builder->whereHasMorph('subscribable', [Goods::class], function (Builder $query) use ($keyword) {
                    $query->where('name', 'like', "%{$keyword}%");
                })
                    ->orWhereHasMorph('subscribable', [ZoneGoods::class], function (Builder $query) use ($keyword) {
                        $query->whereHas('goods', function ($q) use ($keyword) {
                            $q->where('name', 'like', "%{$keyword}%");
                        });
                    });
            })
            ->select('*', DB::raw('DATE_FORMAT(created_at,"%Y年%m月%d日") as look_at'))
            ->latest()
            ->paginate($request->limit ?: 10);

        return $this->success(new SubscribeCollection($lists, true, false));
    }

    public function store(SubscribeRequest $request): JsonResponse
    {
        $data      = $request->safe()->merge(['user_id' => Api::id()]);
        $subscribe = Subscribe::where($data->all())->first();

        if ($subscribe) {
            $subscribe->delete();
            return $this->success('DELETED');
        } else {
            Subscribe::create($data->all());
            return $this->success('CREATED');
        }
    }

    public function show(InteractionRequest $request): JsonResponse
    {
        $result = Subscribe::withTarget($request->safe()->only(['target_type', 'target_id']))
            ->ofUser(Api::user())
            ->exists();

        return $this->success([
            'is_subscribed' => $result,
        ]);
    }

    public function destroy(Subscribe $subscribe): JsonResponse
    {
        $this->checkPermission($subscribe);
        $subscribe->delete();
        return $this->success();
    }
}