<?php

namespace Modules\Interaction\Http\Controllers\Admin;

use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;
use Modules\Interaction\Models\Favorite;

class FavoriteController extends AdminController
{
    protected string $title = '收藏记录';

    public function grid(): Grid
    {
        return Grid::make(Favorite::with(['user.info', 'favoriteable'])->latest(), function (Grid $grid) {
            $grid->disableCreateButton();
            $grid->disableBatchDelete();
            $grid->disableEditButton();
            $grid->disableActions();
            $grid->filter(function (Grid\Filter $filter) {
                $filter->like('user.username', '用户名');
                $filter->like('user.info.nickname', '用户昵称');
            });

            $grid->column('用户')
                ->display(fn() => $this->user->showName);
            $grid->column('图片')
                ->display(function () {
                    if (method_exists($this->favoriteable, 'getCover')) {
                        return $this->favoriteable->getCover();
                    }
                })->image('',100);
            $grid->column('标题')
                ->display(function () {
                    if (method_exists($this->favoriteable, 'getTitle')) {
                        return $this->favoriteable->getTitle();
                    } else {
                        return sprintf('%s:%s', $this->favoriteable_type, $this->favoriteable_id);
                    }
                });

            $grid->column('created_at', ' 收藏时间');
        });
    }

}