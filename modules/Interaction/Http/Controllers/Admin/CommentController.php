<?php

namespace Modules\Interaction\Http\Controllers\Admin;

use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;
use Modules\Interaction\Enums\CommentStatus;
use Modules\Interaction\Http\Actions\CommentVerify;
use Modules\Interaction\Http\Renders\CommentDetail;
use Modules\Interaction\Models\Comment;

class CommentController extends AdminController
{
    protected string $title = '评论';

    public function grid(): Grid
    {
        return Grid::make(Comment::with(['user.info', 'commentable'])->latest(), function (Grid $grid) {
            $grid->disableCreateButton();
            $grid->disableBatchDelete(false);
            $grid->disableEditButton();

            $grid->actions(function (Grid\Displayers\Actions $actions) {
                $actions->append(new CommentVerify());
            });

            $grid->filter(function (Grid\Filter $filter) {
                $filter->like('user.username', '用户名');
                $filter->like('user.info.nickname', '用户昵称');
                $filter->equal('status', '状态')
                    ->select(CommentStatus::STATUS_MAP);
            });

            $grid->column('评论用户')
                ->display(fn() => $this->user->showName);
            $grid->column('star', '评分');
            $grid->column('评论对象')
                ->display(function () {
                    if (method_exists($this->commentable, 'getTitle')) {
                        return $this->commentable->getTitle();
                    } else {
                        return sprintf('%s:%s', $this->commentable_type, $this->commentable_id);
                    }
                });
            $grid->column('图片数量')
                ->display(fn() => count($this->pictures ?? []));
            $grid->column('概览')
                ->display('概览')
                ->expand(CommentDetail::make(['key' => $this]));
            $grid->column('status', '状态')
                ->display(fn($status) => $status->toString())
                ->label(CommentStatus::LABEL_MAP);
            $grid->column('created_at', '评论时间');
        });
    }

    public function form(): Form
    {
        return Form::make(Comment::class);
    }
}