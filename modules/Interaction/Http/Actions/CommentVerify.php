<?php

namespace Modules\Interaction\Http\Actions;

use Dcat\Admin\Grid\RowAction;
use Dcat\Admin\Widgets\Modal;
use Modules\Interaction\Http\Forms\CommentVerifyForm;

class CommentVerify extends RowAction
{
    protected string $title = '审核';

    public function render(): string
    {
        return Modal::make()
            ->lg()
            ->centered()
            ->title($this->title)
            ->body(CommentVerifyForm::make()->payload(['comment_id' => $this->getKey()]))
            ->button($this->title);
    }

    protected function authorize($user): bool
    {
        return $user->isAdministrator();
    }
}