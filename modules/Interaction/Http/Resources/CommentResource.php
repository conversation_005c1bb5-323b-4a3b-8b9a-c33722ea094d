<?php

namespace Modules\Interaction\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class CommentResource extends JsonResource
{
    public function __construct($resource, protected bool $showTarget = true, protected bool $showUser = true)
    {
        parent::__construct($resource);
    }

    public function toArray(Request $request): array
    {
        $user = $request->kernel->guestUser();
        return [
            'comment_id'  => $this->id,
            'target'      => $this->when($this->showTarget, [
                'type'  => $this->commentable_type,
                'id'    => $this->commentable_id,
                'title' => method_exists($this->commentable, 'getTitle') ? $this->commentable->getTitle() : '',
            ]),
            'user'        => $this->when($this->showUser, new InteractionUserResource($this->user)),
            'star'        => $this->star,
            'content'     => $this->content,
            'pictures'    => $this->picture_urls,
            'created_at'  => (string) $this->created_at,
            'interaction' => [
                'id'     => $this->getKey(),
                'model'  => $this->getMorphClass(),
                'status' => [
                    'like' => $this->isCommentLike($user),
                    'step' => $this->isCommentStep($user),
                ],
                'like'   => $this->commentLikes()->count(),
                'step'   => $this->commentSteps()->count(),
            ],
            'children'    => CommentChildrenResource::collection($this->children)
        ];
    }
}