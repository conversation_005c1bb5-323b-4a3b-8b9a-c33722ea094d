<?php

namespace Modules\Interaction\Http\Resources;

use App\Http\Resources\BaseCollection;
use Illuminate\Http\Request;

class FollowCollection extends BaseCollection
{
    public function __construct($resource, protected bool $showTarget = true, protected bool $showUser = true)
    {
        parent::__construct($resource);
    }

    public function toArray(Request $request): array
    {
        return [
            'data' => $this->collection->map(function ($item) {
                return new FollowResource($item, $this->showTarget, $this->showUser);
            }),
            'page' => $this->page(),
        ];
    }
}