<?php

namespace Modules\Interaction\Http\Renders;

use Dcat\Admin\Support\LazyRenderable;
use Dcat\Admin\Widgets\Alert;
use Dcat\Admin\Widgets\Tab;
use Modules\Interaction\Models\Comment;

class CommentDetail extends LazyRenderable
{
    public function render(): string
    {
        $comment = Comment::find($this->payload['key']);

        $pictures = $comment->pictureUrls;

        $tab = Tab::make();
        $tab->padding('20px');

        $alert = Alert::make($comment->content, null);
        $alert->info();
        $tab->add('评论内容', $alert, true);

        $ps = '';
        foreach ($pictures as $picture) {
            $ps .= sprintf('<img src="%s" style="width: 100px;margin-right: 10px" />', $picture);
        }

        $tab->add('附带图片', $ps);

        return $tab->withCard();
    }
}