<?php

namespace Modules\Interaction\Http\Forms;

use Dcat\Admin\Contracts\LazyRenderable;
use Dcat\Admin\Http\JsonResponse;
use Dcat\Admin\Traits\LazyWidget;
use Dcat\Admin\Widgets\Form;
use Modules\Interaction\Enums\CommentStatus;
use Modules\Interaction\Models\Comment;

class CommentVerifyForm extends Form implements LazyRenderable
{
    use LazyWidget;

    public function handle(array $input): JsonResponse
    {
        $comment = Comment::find($this->payload['comment_id']);

        $comment->status = $input['result'];
        $comment->save();

        return $this->response()->success('审核操作成功')->refresh();
    }

    public function form(): void
    {
        $comment = Comment::find($this->payload['comment_id']);
        $this->radio('result', '审核结果')
            ->options(CommentStatus::STATUS_MAP)
            ->value($comment->status->value)
            ->required();
    }
}