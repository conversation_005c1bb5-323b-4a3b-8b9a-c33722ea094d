<?php

namespace Modules\Interaction\Http\Requests;

use App\Http\Requests\BaseFormRequest;
use Modules\Interaction\Rules\FavoriteRule;

class FavoriteRequest extends BaseFormRequest
{
    public function rules(): array
    {
        return [
            'favoriteable_type' => 'required|bail|alpha',
            'favoriteable_id'   => [
                'required',
                'bail',
                new FavoriteRule,
            ],
        ];
    }
}
