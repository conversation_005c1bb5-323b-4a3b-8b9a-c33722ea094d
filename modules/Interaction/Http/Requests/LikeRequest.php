<?php

namespace Modules\Interaction\Http\Requests;

use App\Http\Requests\BaseFormRequest;
use Modules\Interaction\Rules\LikeRule;

class LikeRequest extends BaseFormRequest
{
    public function rules(): array
    {
        return [
            'likeable_type' => 'required|bail|alpha',
            'likeable_id'   => [
                'required',
                'bail',
                new LikeRule,
            ],
        ];
    }
}
