<?php

namespace Modules\Interaction\Models;

use App\Events\CommentLikeEvent;
use App\Models\Model;
use App\Traits\BelongsToUser;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\MorphTo;

class CommentLike extends Model
{
    use BelongsToUser;

    protected $table = 'interaction_comment_likes';

    protected static function boot(): void
    {
        parent::boot();
        self::created(function ($like) {
            event(new CommentLikeEvent($like));
        });
    }

    public function target(): MorphTo
    {
        return $this->morphTo();
    }

    public function scopeWithTarget(Builder $query, Model $model): Builder
    {
        return $query->where('target_type', $model->getMorphClass())
            ->where('target_id', $model->getKey());
    }
}
