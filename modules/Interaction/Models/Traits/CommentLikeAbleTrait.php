<?php

namespace Modules\Interaction\Models\Traits;

use App\Models\User;
use Illuminate\Database\Eloquent\Relations\MorphMany;
use Modules\Interaction\Models\CommentLike;
use Modules\Interaction\Models\CommentStep;

trait CommentLikeAbleTrait
{
    protected static function bootCommentLikeAbleTrait(): void
    {
        self::deleting(function ($model) {
            $model->commentLikes()->delete();
            $model->commentSteps()->delete();
        });
    }

    public function commentLikes(): MorphMany
    {
        return $this->morphMany(CommentLike::class, 'target');
    }

    public function commentSteps(): MorphMany
    {
        return $this->morphMany(CommentStep::class, 'target');
    }

    public function commentLikeFormUser(User $user): int
    {
        $this->commentLikes()->firstOrCreate([
            'user_id' => $user->id,
        ]);
        $this->commentSteps()->ofUser($user)->delete();
        return $this->commentLikes()->count();
    }

    public function isCommentLike(?User $user = null)
    {
        return $user ? $this->commentLikes()->ofUser($user)->exists() : false;
    }

    public function isCommentStep(?User $user = null)
    {
        return $user ? $this->commentSteps()->ofUser($user)->exists() : false;
    }

    public function commentStepFormUser(User $user): int
    {
        $this->commentSteps()->firstOrCreate([
            'user_id' => $user->id,
        ]);
        $this->commentLikes()->ofUser($user)->delete();
        return $this->commentSteps()->count();
    }
}