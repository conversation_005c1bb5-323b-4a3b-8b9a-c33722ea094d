<?php

namespace Modules\Interaction\Models;

use App\Models\Model;
use App\Models\User;
use App\Traits\BelongsToUser;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\MorphTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class Follow extends Model
{
    use BelongsToUser, SoftDeletes;

    protected $table = 'interaction_follows';

    protected array $dates = [
        'accepted_at',
    ];

    public function followable(): MorphTo
    {
        return $this->morphTo();
    }

    public function scopeWithType(Builder $query, string $type): Builder
    {
        return $query->where('followable_type', app($type)->getMorphClass());
    }

    public function scopeWithTarget(Builder $query, array $target): Builder
    {
        return $query->where('followable_type', $target['target_type'])
            ->where('followable_id', $target['target_id']);
    }

    public function scopeOfItem(Builder $query, Model|User $item): Builder
    {
        return $query->where('followable_type', $item->getMorphClass())
            ->where('followable_id', $item->getKey());
    }

    /**
     * Notes: 是否是双向关注
     *
     * @Author: 玄尘
     * @Date: 2025/4/2 14:03
     * @return bool
     */
    public function isReverseFollowing(): bool
    {
        return self::where('user_id', $this->followable_id)
            ->where('followable_type', $this->followable_type)
            ->where('followable_id', $this->user_id)
            ->exists();
    }

}
