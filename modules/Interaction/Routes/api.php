<?php

use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Route;
use Modules\Interaction\Http\Controllers\Api\CommentController;
use Modules\Interaction\Http\Controllers\Api\FavoriteController;
use Modules\Interaction\Http\Controllers\Api\FollowController;
use Modules\Interaction\Http\Controllers\Api\InteractionController;
use Modules\Interaction\Http\Controllers\Api\LikeController;
use Modules\Interaction\Http\Controllers\Api\SubscribeController;

Route::group([
    'prefix' => 'data',
], function (Router $router) {
    $router->get('comments', [InteractionController::class, 'comments']);
    $router->get('favorites', [InteractionController::class, 'favorites']);
    $router->get('follows', [InteractionController::class, 'follows']);
    $router->get('likes', [InteractionController::class, 'likes']);
    $router->get('subscribes', [InteractionController::class, 'subscribes']);
});

Route::group([
    'prefix'     => 'comments',
    'middleware' => 'auth:sanctum',
], function (Router $router) {
    $router->get('', [CommentController::class, 'index']);
    $router->post('', [CommentController::class, 'store']);
    $router->delete('{comment}', [CommentController::class, 'destroy']);
});

Route::group([
    'prefix'     => 'favorites',
    'middleware' => 'auth:sanctum',
], function (Router $router) {
    $router->get('', [FavoriteController::class, 'index']);
    $router->post('', [FavoriteController::class, 'store']);
    $router->get('check', [FavoriteController::class, 'show']);
    $router->delete('{favorite}', [FavoriteController::class, 'destroy']);
});

Route::group([
    'prefix'     => 'likes',
    'middleware' => 'auth:sanctum',
], function (Router $router) {
    $router->get('', [LikeController::class, 'index']);
    $router->post('', [LikeController::class, 'store']);
    $router->get('check', [LikeController::class, 'show']);
    $router->delete('{like}', [LikeController::class, 'destroy']);
});

Route::group([
    'prefix'     => 'subscribes',
    'middleware' => 'auth:sanctum',
], function (Router $router) {
    $router->get('app', [SubscribeController::class, 'appIndex']);
    $router->get('', [SubscribeController::class, 'index']);
    $router->post('', [SubscribeController::class, 'store']);
    $router->get('check', [SubscribeController::class, 'show']);
    $router->delete('{subscribe}', [SubscribeController::class, 'destroy']);
});

Route::group([
    'prefix'     => 'follows',
    'middleware' => 'auth:sanctum',
], function (Router $router) {
    $router->get('', [FollowController::class, 'index']);

    $router->get('approved', [FollowController::class, 'approved']);
    $router->get('not_approved', [FollowController::class, 'notApproved']);

    $router->post('reject', [FollowController::class, 'reject']);
    $router->post('accept', [FollowController::class, 'accept']);

    $router->post('', [FollowController::class, 'store']);
    $router->get('check', [FollowController::class, 'show']);
    $router->delete('{follow}', [FollowController::class, 'destroy']);
});
