<?php

namespace Modules\Payment\Providers;

use App\Events\UserCreatedEvent;
use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;
use Modules\Payment\Events\RefundCreatedEvent;
use Modules\Payment\Listeners\RefundCreatedListener;
use Modules\Payment\Listeners\UserCreatedListener;

class EventServiceProvider extends ServiceProvider
{
    protected $listen = [
        UserCreatedEvent::class   => [
            UserCreatedListener::class,
        ],
        RefundCreatedEvent::class => [
            RefundCreatedListener::class
        ]
    ];

    protected $observers = [

    ];
}