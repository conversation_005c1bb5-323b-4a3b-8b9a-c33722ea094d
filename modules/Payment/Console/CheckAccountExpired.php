<?php

namespace Modules\Payment\Console;

use Illuminate\Console\Command;
use Illuminate\Support\Carbon;
use Modules\Payment\Jobs\AccountExpired;
use Modules\Payment\Models\AccountLog;

class CheckAccountExpired extends Command
{
    protected $signature = 'payment:check-account-expired';

    protected $description = '检查明日用户账变的过期数据，并加入到队列里面去处理';

    public function handle(): void
    {
        $logs = AccountLog::whereDate('expired_at', Carbon::tomorrow())->get();

        foreach ($logs as $log) {
            AccountExpired::dispatch($log)->delay($log->expired_at->subSeconds(5));
        }
    }
}