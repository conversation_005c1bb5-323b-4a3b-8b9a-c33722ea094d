<?php

namespace Modules\Payment\Console;

use Illuminate\Console\Scheduling\Schedule;

class Kernel
{
    public function schedule(Schedule $schedule): void
    {
        //        $schedule->command('bill:create day')
        //            ->onOneServer()
        //            ->dailyAt('00:05');
        //        $schedule->command('bill:create week')
        //            ->onOneServer()
        //            ->weekly()
        //            ->mondays()
        //            ->at('00:10');
        //        $schedule->command('bill:create month')
        //            ->onOneServer()
        //            ->monthlyOn(1, '00:20');
        //        $schedule->command('bill:create quarter')
        //            ->onOneServer()
        //            ->quarterlyOn(1, '00:30');
        //        $schedule->command('bill:create year')
        //            ->onOneServer()
        //            ->yearlyOn(1, 1, '01:00');
        # 每天凌晨，将今日要处理的过期积分，进行统计处理
        $schedule->command('payment:check-account-expired')
            ->onOneServer()
            ->daily();
    }
}