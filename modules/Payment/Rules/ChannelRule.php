<?php

namespace Modules\Payment\Rules;

use Closure;
use Illuminate\Contracts\Validation\DataAwareRule;
use Illuminate\Contracts\Validation\ValidationRule;
use Modules\Payment\Enums\Channel;

class ChannelRule implements ValidationRule, DataAwareRule
{
    protected array $data = [];

    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        if (! in_array($this->data['gateway'].'_'.$value, Channel::values())) {
            $fail('支付通道不正确');
        }
    }

    public function setData(array $data): static
    {
        $this->data = $data;

        return $this;
    }
}