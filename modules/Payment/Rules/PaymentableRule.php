<?php

namespace Modules\Payment\Rules;

use App\Facades\Api;
use Closure;
use Exception;
use Illuminate\Contracts\Validation\ValidationRule;
use Illuminate\Database\Eloquent\Relations\Relation;
use Illuminate\Support\Facades\Crypt;
use Modules\Payment\Contracts\Paymentable;

class PaymentableRule implements ValidationRule
{
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        try {
            $decrypted = Crypt::decryptString($value);
        } catch (Exception) {
            $fail('支付对象解析失败');
            return;
        }

        if (! $decrypted) {
            $fail('支付对象解析不正确');
            return;
        }
        $target = explode('|', $decrypted);
        $class  = Relation::getMorphedModel($target[0]);

        if (! class_exists($class)) {
            $fail('支付对象不存在');
            return;
        }
        if (! (new $class instanceof Paymentable)) {
            $fail('支付对象不正确');
            return;
        }
        if (! $class::find($target[1])) {
            $fail('支付对象不存在');
            return;
        }
        if (! $class::find($target[1])->canPay()) {
            $fail('当前对象为不可支付状态');
            return;
        }
        if ($class::find($target[1])->user->isNot(Api::user())) {
            $fail('非当前账户订单');
        }
    }
}