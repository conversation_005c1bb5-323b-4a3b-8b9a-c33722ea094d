<?php

namespace Modules\Payment\Rules;

use App\Facades\Api;
use Closure;
use Illuminate\Contracts\Validation\DataAwareRule;
use Illuminate\Contracts\Validation\ValidationRule;
use Modules\Payment\Models\WithdrawChannel;

class WithdrawAmountRule implements ValidationRule, DataAwareRule
{
    protected array $data = [];

    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        $channel = WithdrawChannel::find($this->data['channel_id']);

        if (empty($channel)) {
            $fail('提现渠道不存在');
            return;
        }

        if ($value < $channel->min) {
            $fail('提现金额不能低于'.$channel->min);
            return;
        }

        if ($value > $channel->max) {
            $fail('提现金额不能高于'.$channel->max);
            return;
        }

        $balance = Api::user()->account->balance;
        if ($value > $balance) {
            $fail('可用余额不足');
        }
    }

    public function setData(array $data): static
    {
        $this->data = $data;

        return $this;
    }
}