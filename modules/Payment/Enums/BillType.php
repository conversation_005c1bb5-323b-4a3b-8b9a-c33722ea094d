<?php

namespace Modules\Payment\Enums;

use App\Traits\EnumMethods;

enum BillType: string
{
    use EnumMethods;

    case DAY     = 'day';
    case WEEK    = 'week';
    case MONTH   = 'month';
    case QUARTER = 'quarter';
    case YEAR    = 'year';

    const TYPE_MAP = [
        self::DAY->value     => '日账单',
        self::WEEK->value    => '周账单',
        self::MONTH->value   => '月账单',
        self::QUARTER->value => '季度账单',
        self::YEAR->value    => '年账单',
    ];

    public function toString(): string
    {
        return self::TYPE_MAP[$this->value];
    }
}