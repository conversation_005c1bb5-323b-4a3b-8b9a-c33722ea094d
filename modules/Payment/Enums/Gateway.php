<?php

namespace Modules\Payment\Enums;

use App\Traits\EnumMethods;

enum Gateway: string
{
    use EnumMethods;

    case ALIPAY    = 'alipay';
    case BALANCE   = 'balance';
    case CHINA_UMS = 'china_ums';
    case JD_QUICK  = 'jd_quick';
    case UNIPAY    = 'unipay';
    case WECHAT    = 'wechat';
    case DOU_GONG  = 'dou_gong';
    case LAKALA    = 'lakala';
    case TZ_BANK   = 'tz_bank';
    case CORPORATE = 'corporate';
    case PAYPAL    = 'paypal';
    case APPLE     = 'apple';
    case SYSTEM    = 'system';

    const GATEWAY_MAP = [
        self::ALIPAY->value    => '支付宝',
        self::BALANCE->value   => '账户余额',
        self::CHINA_UMS->value => '银联商务',
        self::JD_QUICK->value  => '京东快捷',
        self::UNIPAY->value    => '银联支付',
        self::WECHAT->value    => '微信支付',
        self::DOU_GONG->value  => '汇付斗拱',
        self::LAKALA->value    => '拉卡拉',
        self::TZ_BANK->value   => '台州银行',
        self::CORPORATE->value => '对公账户',
        self::PAYPAL->value    => 'PAYPAL',
        self::APPLE->value     => '苹果支付',
        self::SYSTEM->value    => '后台',
    ];

    const ERP_MAP = [
        self::ALIPAY->value  => '支付宝',
        self::BALANCE->value => '储值卡',
        self::WECHAT->value  => '微信',
    ];

    const GATEWAY_LABEL = [
        self::ALIPAY->value    => 'info',
        self::BALANCE->value   => 'pink',
        self::CHINA_UMS->value => 'warning',
        self::JD_QUICK->value  => 'red',
        self::UNIPAY->value    => 'danger',
        self::WECHAT->value    => 'success',
        self::DOU_GONG->value  => 'cyan',
        self::LAKALA->value    => 'blue',
        self::TZ_BANK->value   => 'blue',
        self::CORPORATE->value => 'blue',
        self::PAYPAL->value    => 'blue',
        self::APPLE->value     => 'indigo',
    ];

    public function toString(): string
    {
        return self::GATEWAY_MAP[$this->value];
    }
}