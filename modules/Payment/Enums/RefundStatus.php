<?php

namespace Modules\Payment\Enums;

use App\Traits\EnumMethods;

enum RefundStatus: string
{
    use EnumMethods;

    case INIT     = 'init';
    case PROCESS  = 'process';
    case COMPLETE = 'complete';

    const STATUS_MAP = [
        self::INIT->value     => '初始化',
        self::PROCESS->value  => '退款中',
        self::COMPLETE->value => '退款完成',
    ];

    const STATUS_LABEL = [
        self::INIT->value     => 'default',
        self::PROCESS->value  => 'warning',
        self::COMPLETE->value => 'success',
    ];

    public function toString(): string
    {
        return self::STATUS_MAP[$this->value];
    }
}