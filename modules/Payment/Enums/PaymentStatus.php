<?php

namespace Modules\Payment\Enums;

use App\Traits\EnumMethods;

enum PaymentStatus: string
{
    use EnumMethods;

    case UNPAY       = 'UNPAY';
    case PAID        = 'PAID';
    case PAID_PART   = 'PAID_PART';
    case REFUND      = 'REFUND';
    case REFUND_PART = 'REFUND_PART';

    const STATUS_MAP = [
        self::UNPAY->value       => '未支付',
        self::PAID->value        => '已支付',
        self::PAID_PART->value   => '部分已付',
        self::REFUND->value      => '全额退款',
        self::REFUND_PART->value => '部分退款',
    ];

    const STATUS_LABEL = [
        self::UNPAY->value       => 'default',
        self::PAID->value        => 'success',
        self::PAID_PART->value   => 'success',
        self::REFUND->value      => 'warning',
        self::REFUND_PART->value => 'warning',
    ];

    public function toString(): string
    {
        return self::STATUS_MAP[$this->value];
    }
}