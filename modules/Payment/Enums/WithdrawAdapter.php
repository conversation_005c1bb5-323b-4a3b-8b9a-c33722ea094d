<?php

namespace Modules\Payment\Enums;

use App\Traits\EnumMethods;
use Modules\Payment\Drivers\WithdrawAdapters\AlipayAdapter;
use Modules\Payment\Drivers\WithdrawAdapters\BankAdapter;
use Modules\Payment\Drivers\WithdrawAdapters\WechatAdapter;

enum WithdrawAdapter: string
{
    use EnumMethods;

    case ALIPAY = AlipayAdapter::class;

    case BANK = BankAdapter::class;

    case WECHAT = WechatAdapter::class;

    const ADAPTER_MAP = [
        self::ALIPAY->value => '支付宝',
        self::BANK->value   => '银行卡',
        self::WECHAT->value => '微信支付',
    ];

    const ADAPTER_LABEL = [
        self::ALIPAY->value => 'info',
        self::BANK->value   => 'warning',
        self::WECHAT->value => 'success',
    ];

    public function toString(): string
    {
        return self::ADAPTER_MAP[$this->value];
    }
}