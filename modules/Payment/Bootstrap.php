<?php

namespace Modules\Payment;

use App\Contracts\ModuleBootstrap;
use Illuminate\Support\Facades\Artisan;

class Bootstrap implements ModuleBootstrap
{
    protected static string $menuKey = 'payment-module';

    public static function install(): void
    {
        Artisan::call('migrate', [
            '--path' => 'modules/Payment/Database/Migrations',
        ]);
        Artisan::call('module:seed Payment');
        self::createAdminMenu();
    }

    public static function uninstall(): void
    {
        Artisan::call('migrate:reset', [
            '--path' => 'modules/Payment/Database/Migrations',
        ]);
        self::deleteAdminMenu();
    }

    public static function upgrade(): void
    {
    }

    public static function createAdminMenu(): void
    {
        $menuModel = config('admin.database.menu_model');

        $main = $menuModel::create([
            'parent_id' => 0,
            'order'     => 95,
            'title'     => '支付模块',
            'icon'      => 'fa-pied-piper-pp '.self::$menuKey,
        ]);

        $main->children()->create([
            'order' => 0,
            'title' => '数据看板',
            'icon'  => 'fa-dashboard '.self::$menuKey,
            'uri'   => 'payment',
        ]);

        $order = $main->children()->create([
            'order' => 1,
            'title' => '订单管理',
            'icon'  => 'fa-bars '.self::$menuKey,
        ]);

        $order->children()->createMany([
            [
                'order' => 1,
                'title' => '支付订单',
                'icon'  => 'fa-rmb '.self::$menuKey,
                'uri'   => 'payment/payments',
            ],
            [
                'order' => 2,
                'title' => '退款订单',
                'icon'  => 'fa-strikethrough '.self::$menuKey,
                'uri'   => 'payment/refunds',
            ],
            [
                'order' => 3,
                'title' => '余额支付',
                'icon'  => 'fa-amazon '.self::$menuKey,
                'uri'   => 'payment/balances',
            ],
            [
                'order' => 4,
                'title' => '虚拟支付',
                'icon'  => 'fa-glide-g '.self::$menuKey,
                'uri'   => 'payment/virtuals',
            ],
            [
                'order' => 5,
                'title' => '线下打款',
                'icon'  => 'fa-glide-g '.self::$menuKey,
                'uri'   => 'payment/corporates',
            ],
        ]);

//        $bank = $main->children()->create([
//            'order' => 2,
//            'title' => '银行管理',
//            'icon'  => 'fa-bank '.self::$menuKey,
//        ]);
//
//        $bank->children()->createMany([
//            [
//                'order' => 1,
//                'title' => '银行卡列表',
//                'icon'  => 'fa-credit-card '.self::$menuKey,
//                'uri'   => 'payment/banks/cards',
//            ],
//            [
//                'order' => 2,
//                'title' => '银行列表',
//                'icon'  => 'fa-bank '.self::$menuKey,
//                'uri'   => 'payment/banks',
//            ],
//        ]);

        $account = $main->children()->create([
            'order' => 2,
            'title' => '用户账户',
            'icon'  => 'fa-users '.self::$menuKey,
        ]);

        $account->children()->createMany([
            [
                'order' => 1,
                'title' => '用户账户',
                'icon'  => 'fa-address-card-o '.self::$menuKey,
                'uri'   => 'payment/accounts',
            ],
            [
                'order' => 2,
                'title' => '账变规则',
                'icon'  => 'fa-flag '.self::$menuKey,
                'uri'   => 'payment/accounts/rules',
            ],
            [
                'order' => 3,
                'title' => '账变记录',
                'icon'  => 'fa-bars '.self::$menuKey,
                'uri'   => 'payment/accounts/logs',
            ],
            [
                'order' => 4,
                'title' => '充值订单',
                'icon'  => 'fa-bars '.self::$menuKey,
                'uri'   => 'payment/accounts/recharges',
            ],
            [
                'order' => 5,
                'title' => '账户密钥',
                'icon'  => 'fa-bars '.self::$menuKey,
                'uri'   => 'payment/securities',
            ],
        ]);

//        $bill = $main->children()->create([
//            'order' => 4,
//            'title' => '账单管理',
//            'icon'  => 'fa-file-excel-o '.self::$menuKey,
//        ]);
//
//        $bill->children()->createMany([
//            [
//                'order' => 1,
//                'title' => '日账单',
//                'icon'  => 'fa-bars '.self::$menuKey,
//                'uri'   => 'payment/bills/day',
//            ],
//            [
//                'order' => 2,
//                'title' => '周账单',
//                'icon'  => 'fa-bars '.self::$menuKey,
//                'uri'   => 'payment/bills/week',
//            ],
//            [
//                'order' => 3,
//                'title' => '月账单',
//                'icon'  => 'fa-bars '.self::$menuKey,
//                'uri'   => 'payment/bills/month',
//            ],
//            [
//                'order' => 4,
//                'title' => '季账单',
//                'icon'  => 'fa-bars '.self::$menuKey,
//                'uri'   => 'payment/bills/quarter',
//            ],
//            [
//                'order' => 5,
//                'title' => '年账单',
//                'icon'  => 'fa-bars '.self::$menuKey,
//                'uri'   => 'payment/bills/year',
//            ],
//        ]);

//        $withdraw = $main->children()->create([
//            'order' => 10,
//            'title' => '提现管理',
//            'icon'  => 'fa-cogs '.self::$menuKey,
//        ]);
//
//        $withdraw->children()->createMany([
//            [
//                'order' => 1,
//                'title' => '提现申请',
//                'icon'  => 'fa-bars '.self::$menuKey,
//                'uri'   => 'payment/withdraws',
//            ],
//            [
//                'order' => 2,
//                'title' => '提现渠道',
//                'icon'  => 'fa-bars '.self::$menuKey,
//                'uri'   => 'payment/withdraws/channels',
//            ],
//        ]);
    }

    public static function deleteAdminMenu(): void
    {
        $menuModel = config('admin.database.menu_model');
        $menuModel::where('icon', 'like', '%'.self::$menuKey)->delete();
    }
}