<?php

namespace Modules\Payment\Models;

use App\Models\Model;
use App\Traits\AutoCreateOrderNo;
use App\Traits\BelongsToUser;
use Illuminate\Support\Facades\DB;
use Modules\Payment\Contracts\Paymentable;
use Modules\Payment\Enums\AccountType;
use Modules\Payment\Enums\RechargeStatus;
use Modules\Payment\Traits\HasPayment;

class Recharge extends Model implements Paymentable
{
    use AutoCreateOrderNo,
        BelongsToUser,
        HasPayment;

    protected $table = 'payment_recharges';

    protected $casts = [
        'type'   => AccountType::class,
        'status' => RechargeStatus::class,
    ];

    protected static function boot(): void
    {
        parent::boot();

        self::creating(function ($model) {
            $model->receipts = $model->amount;
        });
    }

    public function getRouteKeyName(): string
    {
        return 'no';
    }

    /**
     * Notes   : 获取支付的标题
     *
     * @Date   : 2023/5/19 10:11
     * <AUTHOR> <Jason.C>
     * @return string
     */
    public function getTitle(): string
    {
        return '充值订单';
    }

    /**
     * Notes   : 获取订单支付金额
     *
     * @Date   : 2023/5/19 10:11
     * <AUTHOR> <Jason.C>
     * @return string
     */
    public function getTotalAmount(): string
    {
        return number_format($this->amount, 2, '.', '');
    }

    /**
     * Notes   : 支付完成
     *
     * @Date   : 2023/5/19 10:10
     * <AUTHOR> <Jason.C>
     * @param  \Modules\Payment\Models\Payment  $payment
     * @return bool
     * @throws \Throwable
     */
    public function paid(Payment $payment): bool
    {
        return DB::transaction(function () {
            $this->user->account->exec('recharge_balance', $this->receipts, null, ['recharge_id' => $this->getKey()]);

            $this->status = RechargeStatus::PAID;
            return $this->save();
        });
    }

    /**
     * Notes   : 订单是否可以支付
     *
     * @Date   : 2023/5/19 10:11
     * <AUTHOR> <Jason.C>
     * @return bool
     */
    public function canPay(): bool
    {
        return $this->status == RechargeStatus::INIT;
    }

    public function canRefund(): bool
    {
        return false;
    }
}
