<?php

namespace Modules\Payment\Models;

use App\Models\Model;
use App\Traits\BelongsToUser;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\SoftDeletes;

class BankCard extends Model
{
    use BelongsToUser,
        SoftDeletes;

    protected $table = 'payment_bank_cards';

    protected $casts = [
        'can_withdraw' => 'boolean',
        'can_quick'    => 'boolean',
    ];

    public function bank(): BelongsTo
    {
        return $this->belongsTo(Bank::class);
    }

    public function setBankAttribute(Bank $bank): void
    {
        $this->attributes['bank_id'] = $bank->getKey();
    }
}
