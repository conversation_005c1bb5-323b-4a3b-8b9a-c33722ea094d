<?php

namespace Modules\Payment\Models;

use App\Models\Model;
use App\Traits\AutoCreateOrderNo;
use App\Traits\MorphToUser;
use Modules\Payment\Enums\Channel;
use Modules\Payment\Enums\Gateway;
use Modules\Payment\Models\Traits\BelongsToPayment;

class Virtual extends Model
{
    use AutoCreateOrderNo,
        BelongsToPayment,
        MorphToUser;

    protected $table = 'payment_virtuals';

    protected $casts = [
        'paid_at' => 'datetime',
        'gateway' => Gateway::class,
        'channel' => Channel::class,
    ];
}
