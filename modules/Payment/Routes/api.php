<?php

use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Route;
use Modules\Payment\Http\Controllers\Api\AccountController;
use Modules\Payment\Http\Controllers\Api\BalanceController;
use Modules\Payment\Http\Controllers\Api\BankCardController;
use Modules\Payment\Http\Controllers\Api\CorporateController;
use Modules\Payment\Http\Controllers\Api\NotifyController;
use Modules\Payment\Http\Controllers\Api\PaymentController;
use Modules\Payment\Http\Controllers\Api\RechargeController;
use Modules\Payment\Http\Controllers\Api\SecurityController;
use Modules\Payment\Http\Controllers\Api\WithdrawController;

Route::group([
    'middleware' => 'auth:sanctum',
], function (Router $router) {
    /**
     * 创建支付订单的统一渠道
     */
    $router->post('create', [PaymentController::class, 'create']);
    $router->get('gateways', [PaymentController::class, 'gateways']);
    $router->post('unify/init', [PaymentController::class, 'unifyInit']);
    $router->post('unify/payment', [PaymentController::class, 'unifyPay']);
    $router->post('unify/query', [PaymentController::class, 'unifyQuery']);
    $router->get('query/{payment}', [PaymentController::class, 'query']);
    /**
     * 余额支付
     */
    $router->get('balance/{balance}', [BalanceController::class, 'info']);
    $router->post('balance/{balance}', [BalanceController::class, 'pay']);
    /**
     * 密钥
     */
    $router->post('security', [SecurityController::class, 'store']);
    $router->put('security', [SecurityController::class, 'update']);
    /**
     * 银行卡管理
     */
    $router->get('banks', [BankCardController::class, 'banks']);
    $router->get('bank/cards', [BankCardController::class, 'cards']);
    $router->post('bank/cards', [BankCardController::class, 'store']);
    $router->delete('bank/cards/{card}', [BankCardController::class, 'destroy']);
    /**
     * 充值
     */
    if (config('payment.CAN_RECHARGE')) {
        $router->get('recharges', [RechargeController::class, 'index']);
        $router->get('recharges/types', [RechargeController::class, 'types']);
        $router->post('recharges', [RechargeController::class, 'create']);
    }
    /**
     * 账户
     */
    $router->get('account', [AccountController::class, 'index']);
    $router->get('account/logs', [AccountController::class, 'logs']);
    $router->get('account/logs/score', [AccountController::class, 'score']);
//    $router->get('account/logs/balance', [AccountController::class, 'balance']);
//    $router->get('account/logs/{type}', [AccountController::class, 'typeLogs']);
    /**
     * 线下打款
     */
    $router->resource('corporates', CorporateController::class);
    /**
     * 提现
     */
    $router->get('withdraws', [WithdrawController::class, 'index']);
    $router->get('withdraws/channels', [WithdrawController::class, 'channels']);
    $router->post('withdraws', [WithdrawController::class, 'store']);
});

