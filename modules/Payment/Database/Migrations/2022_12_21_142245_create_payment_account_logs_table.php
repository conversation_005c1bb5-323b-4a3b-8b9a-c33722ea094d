<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Modules\Payment\Enums\AccountType;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('payment_account_logs', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('account_id')
                ->index();
            $table->unsignedBigInteger('rule_id')
                ->index();
            $table->enum('type', AccountType::values())
                ->index()
                ->comment('变动账户类型');
            $table->decimal('amount', 20)
                ->comment('变动金额');
            $table->decimal('balance', 20)
                ->comment('当期余额');
            $table->boolean('frozen')
                ->default(false);
            $table->timestamp('unfreeze_at')
                ->nullable()
                ->comment('可解冻时间');
            $table->json('source')
                ->nullable();
            $table->dateTime('expired_at')
                ->nullable()
                ->comment('过期时间（秒）');
            $table->boolean('is_expired')
                ->default(false);
            $table->timestamps();
            $table->softDeletes()
                ->index();

            $table->index(['expired_at', 'is_expired']);
            $table->index(['created_at']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('payment_account_logs');
    }
};
