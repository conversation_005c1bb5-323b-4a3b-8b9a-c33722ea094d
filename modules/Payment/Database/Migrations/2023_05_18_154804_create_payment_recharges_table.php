<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Modules\Payment\Enums\AccountType;
use Modules\Payment\Enums\RechargeStatus;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('payment_recharges', function (Blueprint $table) {
            $table->id();
            $table->user();
            $table->string('no', 32)
                ->unique();
            $table->enum('type', AccountType::values())
                ->index()
                ->comment('变动账户类型');
            $table->unsignedDecimal('amount', 20)
                ->comment('充值金额');
            $table->unsignedDecimal('receipts', 20)
                ->nullable()
                ->comment('实际入账');
            $table->enum('status', RechargeStatus::values())
                ->default(RechargeStatus::INIT->value);
            $table->timestamps();

            $table->index(['created_at']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('payment_recharges');
    }
};
