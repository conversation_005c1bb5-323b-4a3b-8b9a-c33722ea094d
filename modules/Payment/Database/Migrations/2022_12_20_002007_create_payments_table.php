<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Modules\Payment\Enums\Channel;
use Modules\Payment\Enums\Gateway;
use Modules\Payment\Enums\PaymentStatus;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('payments', function (Blueprint $table) {
            $table->id();
            $table->user();
            $table->unsignedInteger('combine_id')->nullable()->comment('合单ID');
            $table->morphs('paymentable');
            $table->string('no', 32)
                ->unique()
                ->comment('付款单号,trade_id');
            $table->enum('gateway', Gateway::values())
                ->index()
                ->comment('支付渠道');
            $table->enum('channel', Channel::values())
                ->index()
                ->comment('支付通道,属于支付渠道下的支付方式');
            $table->string('currency', 10)->default('CNY');
            $table->unsignedDecimal('amount', 20)
                ->comment('支付金额');
            $table->enum('status', PaymentStatus::values())
                ->default(PaymentStatus::UNPAY->value)
                ->comment('支付状态');
            $table->dateTime('paid_at')
                ->nullable()
                ->comment('付款时间');
            $table->timestamps();
            $table->softDeletes()
                ->index();

            $table->index(['created_at']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('payments');
    }
};
