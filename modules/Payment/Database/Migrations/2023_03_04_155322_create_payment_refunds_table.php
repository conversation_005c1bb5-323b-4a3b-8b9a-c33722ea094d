<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;
use Modules\Payment\Enums\RefundStatus;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('payment_refunds', function (Blueprint $table) {
            $table->id();
            $table->user();
            $table->morphs('refundable');
            $table->unsignedBigInteger('payment_id')
                ->index();
            $table->string('no', 32)
                ->unique();
            $table->unsignedDecimal('amount', 20)
                ->comment('退款金额');
            $table->enum('status', RefundStatus::values())
                ->default(RefundStatus::INIT->value)
                ->comment('退款状态');
            $table->dateTime('refund_at')
                ->nullable()
                ->comment('退款时间');
            $table->string('remark')
                ->nullable()
                ->comment('备注信息');
            $table->timestamps();
            $table->softDeletes()
                ->index();

            $table->index(['created_at']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('payment_refunds');
    }
};
