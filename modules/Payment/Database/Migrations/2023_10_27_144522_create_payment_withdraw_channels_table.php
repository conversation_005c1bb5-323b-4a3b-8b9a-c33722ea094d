<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration {
    public function up(): void
    {
        Schema::create('payment_withdraw_channels', function (Blueprint $table) {
            $table->id();
            $table->string('name')
                ->comment('渠道名称');
            $table->cover();
            $table->decimal('rate')
                ->default(0)
                ->comment('提现费率');
            $table->decimal('min')
                ->nullable()
                ->comment('最低提现金额');
            $table->decimal('max')
                ->nullable()
                ->comment('最高提现金额');
            $table->boolean('status');
            $table->string('adapter');
            $table->json('params')
                ->nullable();
            $table->timestamps();

            $table->softDeletes();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('payment_withdraw_channels');
    }
};
