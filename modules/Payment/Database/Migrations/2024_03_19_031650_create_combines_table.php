<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;
use Modules\Payment\Enums\Gateway;
use Modules\Payment\Enums\Channel;
use Modules\Payment\Enums\PaymentStatus;
return new class extends Migration
{
    public function up(): void
    {
        Schema::create('payment_combines', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('user_id')->index();
            $table->string('order_no');
            $table->string('transaction_id')->nullable()->index()->comment('三方交易号');
            $table->decimal('total', 20, 2)->unsigned()->comment('支付金额');
            $table->enum('gateway', Gateway::values())
                ->index()
                ->comment('支付渠道');
            $table->enum('channel', Channel::values())
                ->index()
                ->comment('支付通道,属于支付渠道下的支付方式');
            $table->enum('status', PaymentStatus::values())
                ->default(PaymentStatus::UNPAY->value)
                ->comment('支付状态');
            $table->dateTime('paid_at')
                ->nullable()
                ->comment('付款时间');
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('payment_combines');
    }
};
