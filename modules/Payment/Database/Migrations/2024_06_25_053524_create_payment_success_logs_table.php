<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('payment_success_logs', function (Blueprint $table) {
            $table->id();
            $table->unsignedInteger('payment_id')->comment('支付ID');
            $table->unsignedDecimal('total', 20, 2)->default(0)->comment('支付金额');
            $table->string('transaction_id')->nullable()->comment('原支付ID');
            $table->string('openid')->nullable()->comment('支付者OPENID');
            $table->json('pay_json')->nullable()->comment('支付回调内容');
            $table->timestamps();
            $table->index('payment_id');
            $table->index('openid');
            $table->index(['openid', 'payment_id']);
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('payment_success_logs');
    }
};
