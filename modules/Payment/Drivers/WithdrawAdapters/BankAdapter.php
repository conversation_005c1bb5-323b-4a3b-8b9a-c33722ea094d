<?php

namespace Modules\Payment\Drivers\WithdrawAdapters;

use Modules\Payment\Contracts\WithdrawAdapter;

class BankAdapter implements WithdrawAdapter
{
    public function disburse(string $no, float $amount, array $options): bool
    {
        return true;
    }

    public static function fields(): array
    {
        return [
            'name'    => [
                'name'  => '收款人姓名',
                'rules' => 'required',
            ],
            'bank_id' => [
                'name'  => '开户银行',
                'rules' => 'required|numeric|exists:Modules\Payment\Models\Bank,id',
            ],
            'card_no' => [
                'name'  => '银行卡号',
                'rules' => 'required',
            ],
        ];
    }

    public static function params(): array
    {
        return [];
    }
}