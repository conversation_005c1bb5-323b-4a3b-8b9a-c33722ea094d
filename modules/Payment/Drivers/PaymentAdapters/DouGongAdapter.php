<?php

namespace Modules\Payment\Drivers\PaymentAdapters;

use Exception;
use GuzzleHttp\Client;
use GuzzleHttp\Psr7\Response;
use Modules\Payment\Contracts\PaymentAdapter;
use Modules\Payment\Enums\Gateway;
use Modules\Payment\Exceptions\CallbackParamsException;
use Modules\Payment\Models\Payment;
use Psr\Http\Message\ResponseInterface;

class DouGongAdapter implements PaymentAdapter
{
    protected string $apiUrl = 'https://api.huifu.com/v2/';

    protected Client $client;

    protected string $timeStamp;

    protected array $config;

    public function __construct(protected Payment|null $payment)
    {
        $this->client = new Client([
            'base_uri' => $this->apiUrl,
        ]);

        $this->timeStamp = date("Ymd", time());
        $this->config    = $this->getConfig();
    }

    public function getConfig(): array
    {
        return [
            'product_id'       => config('payment.DOU_GONG_PRODUCT_ID'),
            'huifu_id'         => config('payment.DOU_GONG_HUIFU_ID'),
            'seq_id'           => config('payment.DOU_GONG_SEQ_ID'),
            'public_key'       => config('payment.DOU_GONG_PUBLIC_KEY'),
            'private_key'      => config('payment.DOU_GONG_PRIVATE_KEY'),
            'huifu_public_key' => config('payment.DOU_GONG_HUIFU_PUBLIC_KEY'),
            'notify_url'       => route('api.payment.notify', Gateway::DOU_GONG->value),
        ];
    }

    public function wechat(): array
    {
        return $this->post('trade/hosting/payment/preorder', [
            'pre_order_type' => 3,
            'trans_amt'      => $this->formatAmount($this->payment->amount),
            'req_seq_id'     => $this->payment->no,
            'goods_desc'     => $this->payment->getTitle(),
            'miniapp_data'   => [
                'seq_id' => $this->config['seq_id'],
            ],
        ]);
    }

    public function alipay(): array
    {
        return $this->post('trade/hosting/payment/preorder', [
            'pre_order_type' => 2,
            'trans_amt'      => $this->formatAmount($this->payment->amount),
            'req_seq_id'     => $this->payment->no,
            'goods_desc'     => $this->payment->getTitle(),
            'app_data'       => [
                'app_schema' => '',
            ],
        ]);
    }

    protected function formatAmount(float $amount): string
    {
        return number_format($amount, 2, '.', '');
    }

    /**
     * @throws \Exception
     */
    public function callback(): array
    {
        $code = request()->resp_code;
        $sign = request()->sign;
        $data = stripslashes(request()->resp_data);

        return json_decode($data, true);
    }

    public function success(): ResponseInterface
    {
        return new Response(
            200,
            ['Content-Type' => 'application/json'],
            json_encode(['code' => 'SUCCESS', 'message' => lecho("Success")]),
        );
    }

    /**
     * Notes   : 验签
     *
     * @Date   : 2023/4/27 17:09
     * <AUTHOR> <Jason.C>
     * @param  string  $signature
     * @param  string  $data
     * @throws \Exception
     */
    private function verifySignWithPublicKey(string $signature, string $data): void
    {
        $cert = wordwrap($this->config['huifu_public_key'], 64, "\n", true);
        $key  = "-----BEGIN PUBLIC KEY-----\n".$cert."\n-----END PUBLIC KEY-----";

        if (! openssl_verify($data, base64_decode($signature), $key, OPENSSL_ALGO_SHA256)) {
            throw new CallbackParamsException('验签错误');
        }
    }

    private function signWithPrivateKey(array $data): string
    {
        ksort($data);

        $data = json_encode($data, JSON_UNESCAPED_SLASHES | JSON_UNESCAPED_UNICODE);

        $pk  = wordwrap($this->config['private_key'], 64, "\n", true);
        $key = "-----BEGIN PRIVATE KEY-----\n".$pk."\n-----END PRIVATE KEY-----";

        $signature = '';
        try {
            openssl_sign($data, $signature, $key, OPENSSL_ALGO_SHA256);
        } catch (Exception $e) {
            echo $e->getMessage();
        }
        return base64_encode($signature);
    }

    public function query(): array
    {
        return $this->post('trade/payment/scanpay/query', [
            'org_req_date'   => $this->timeStamp,
            'org_req_seq_id' => $this->payment->no,
        ]);
    }

    public function refund(string $refundNo, float $refundAmount, string $desc = ''): array
    {
        return [];
    }

    protected function post(string $uri, array $body): array
    {
        $uri         = ltrim($uri, '/');
        $data        = array_merge([
            'req_date'   => $this->timeStamp,
            'huifu_id'   => $this->config['huifu_id'],
            'notify_url' => $this->config['notify_url'],
        ], $body);
        $requestBody = [
            'sys_id'     => $this->config['huifu_id'],
            'product_id' => $this->config['product_id'],
            'data'       => $data,
            'sign'       => $this->signWithPrivateKey($data),
        ];

        $request = $this->client->post($uri, [
            'json' => $requestBody,
        ]);

        return $this->parseResponseData($request);
    }

    private function parseResponseData(ResponseInterface $request): array
    {
        $response = json_decode($request->getBody()->getContents(), true);
        $data     = $response['data'];

        $this->verifySignWithPublicKey($response['sign'], $data);
        if (isset($data['resp_code']) && ! in_array($data['resp_code'], ['00000100', '00000000'])) {
            throw new Exception($data['resp_desc']);
        }
        unset($data['resp_code']);
        unset($data['resp_desc']);
        return $data;
    }
}
