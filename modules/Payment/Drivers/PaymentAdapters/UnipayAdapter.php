<?php

namespace Modules\Payment\Drivers\PaymentAdapters;

use Exception;
use Modules\Payment\Contracts\PaymentAdapter;
use Modules\Payment\Enums\Gateway;
use Modules\Payment\Exceptions\CallbackParamsException;
use Modules\Payment\Models\Payment;
use Psr\Http\Message\ResponseInterface;
use Yansongda\Pay\Pay;
use Yansongda\Pay\Provider\Unipay;

class UnipayAdapter implements PaymentAdapter
{
    protected Unipay $driver;

    public function __construct(protected Payment|null $payment)
    {
        $this->driver = Pay::unipay();
    }

    public function getConfig(): array
    {
        return [
            'logger' => [
                'enable'   => config('payment.LOGGER_ENABLE'),
                'file'     => storage_path('logs/pay.log'),
                'level'    => 'debug',
                'type'     => 'daily',
                'max_file' => 30,
            ],
            'unipay' => [
                'default' => [
                    'mch_id'                  => config('payment.UNIPAY_MCH_ID'),
                    'mch_cert_path'           => config('payment.UNIPAY_MCH_CERT_PATH'),
                    'mch_cert_password'       => config('payment.UNIPAY_MCH_CERT_PASSWORD'),
                    'unipay_public_cert_path' => config('payment.UNIPAY_PUBLIC_CERT_PATH'),
                    'return_url'              => config('payment.UNIPAY_RETURN_URL'),
                    'notify_url'              => route('api.payment.notify', Gateway::UNIPAY->value),
                ],
            ],
        ];
    }

    protected function prepareOrderParams(array $extends = []): array
    {
        $base = [
            'txnTime' => date('YmdHis'),
            'txnAmt'  => $this->payment->amount,
            'orderId' => $this->payment->no,
        ];

        return array_merge($base, $extends);
    }

    public function web(): array
    {
        return $this->driver
            ->web($this->prepareOrderParams())
            ->toArray();
    }

    public function wap()
    {
        return $this->driver
            ->wap($this->prepareOrderParams())
            ->toArray();
    }

    public function scan()
    {
        return $this->driver
            ->scan($this->prepareOrderParams())
            ->toArray();
    }

    public function pos()
    {
        return $this->driver
            ->pos($this->prepareOrderParams(['qrNo' => '']))
            ->toArray();
    }

    /**
     * @throws \Exception
     */
    public function callback(): array
    {
        try {
            return $this->driver->callback()->toArray();
        } catch (Exception $exception) {
            throw new CallbackParamsException('回调参数解析错误');
        }
    }

    public function success(): ResponseInterface
    {
        return $this->driver->success();
    }

    public function query(): array
    {
        return [];
    }

    public function refund(string $refundNo, float $refundAmount, string $desc = ''): array
    {
        return [];
    }
}