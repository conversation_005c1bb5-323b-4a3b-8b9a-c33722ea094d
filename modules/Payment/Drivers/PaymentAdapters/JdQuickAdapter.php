<?php

namespace Modules\Payment\Drivers\PaymentAdapters;

use GuzzleHttp\Psr7\Response;
use Modules\Payment\Contracts\PaymentAdapter;
use Modules\Payment\Enums\Gateway;
use Modules\Payment\Exceptions\CallbackParamsException;
use Modules\Payment\Models\Payment;
use Psr\Http\Message\ResponseInterface;

class JdQuickAdapter implements PaymentAdapter
{
    public function __construct(protected Payment|null $payment)
    {
    }

    public function getConfig(): array
    {
        return [
            'notify_url' => route('api.payment.notify', Gateway::JD_QUICK->value),
        ];
    }

    /**
     * @throws \Exception
     */
    public function callback(): array
    {
        $token     = request()->header('token');
        $timestamp = request()->header('timestamp');
        $body      = request()->all();

        $body = json_encode($body, JSON_UNESCAPED_UNICODE);
        $body = str_replace("null", "\"\"", $body);

        $string = "secretKey={$this->config['secret_key']}&timestamp={$timestamp}&body={$body}";

        $checkString = strtoupper(sha1($string));
        if ($checkString != $token) {
            throw new CallbackParamsException('验签错误');
        } else {
            return request()->all();
        }
    }

    public function success(): ResponseInterface
    {
        return new Response(200, [], 'SUCCESS');
    }

    public function query(): array
    {
        return [];
    }

    public function refund(string $refundNo, float $refundAmount, string $desc = ''): array
    {
        return [];
    }
}