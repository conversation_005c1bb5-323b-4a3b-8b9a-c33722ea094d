<?php

namespace Modules\Payment\Drivers\PaymentAdapters;

use GuzzleHttp\Psr7\Response;
use Modules\Payment\Contracts\PaymentAdapter;
use Modules\Payment\Models\Payment;
use Psr\Http\Message\ResponseInterface;

class CorporateAdapter implements PaymentAdapter
{
    public function __construct(protected Payment|null $payment)
    {
    }

    public function getConfig(): array
    {
        return [
            'company_name' => config('payment.CORPORATE_COMPANY_NAME'),
            'bank_name'    => config('payment.CORPORATE_BANK_NAME'),
            'card_no'      => config('payment.CORPORATE_BANK_CARD_NO'),
        ];
    }

    public function bank(): array
    {
        return [
            'company_name' => config('payment.CORPORATE_COMPANY_NAME'),
            'bank_name'    => config('payment.CORPORATE_BANK_NAME'),
            'card_no'      => config('payment.CORPORATE_BANK_CARD_NO'),
            'amount'       => $this->payment->amount,
            'payment_no'   => $this->payment->no,
        ];
    }

    public function callback(): array
    {
        return [];
    }

    public function success(): ResponseInterface
    {
        return new Response(
            200,
            ['Content-Type' => 'application/json'],
            json_encode(['code' => 'SUCCESS', 'message' => lecho("Success")]),
        );
    }

    public function query(): array
    {
        return [];
    }

    public function refund(string $refundNo, float $refundAmount, string $desc = ''): array
    {
        return [];
    }
}
