<?php

namespace Modules\Payment\Drivers;

use Exception;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Validator;
use Modules\Payment\Models\WithdrawChannel;

class WithdrawFactory
{
    public function __construct(protected WithdrawChannel $channel)
    {
    }

    /**
     * @throws \Exception
     */
    public function disburse(string $no, float $amount, array $options)
    {
        if (! $this->channel->status) {
            throw new Exception('提现渠道不可用');
        }

        $adapter = new $this->channel->adapter;

        return $adapter->disburse($no, $amount, $options);
    }

    /**
     * Notes   : 检查前台传来的数据正确与否
     *
     * @Date   : 2023/10/30 11:34
     * <AUTHOR> <Jason.C>
     * @param  array  $data
     * @return array[bool, string, array]
     */
    public function checkRequestFields(array $data): array
    {
        $params = $this->channel->adapter::fields();
        return $this->verifyData($data, $params);
    }

    /**
     * Notes   : 检测后台填写的参数正确与否
     *
     * @Date   : 2023/10/30 11:04
     * <AUTHOR> <Jason.C>
     * @param  array  $data
     * @return array[bool, string, array]
     */
    public function checkRequestParams(array $data): array
    {
        $params = $this->channel->adapter::params();
        return $this->verifyData($data, $params);
    }

    private function verifyData(array $data, array $validates): array
    {
        $rules      = array_combine(array_keys($validates), Arr::pluck($validates, 'rules'));
        $attributes = array_combine(array_keys($validates), Arr::pluck($validates, 'name'));
        $messages   = array_combine(array_keys($validates), Arr::pluck($validates, 'messages'));
        $validator  = Validator::make($data, $rules, $messages, $attributes);

        if ($validator->fails()) {
            return [
                false, $validator->errors()->first(), null,
            ];
        }

        return [
            true, 'success', $validator->safe()->toArray(),
        ];
    }
}