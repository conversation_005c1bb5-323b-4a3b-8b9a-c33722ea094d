<?php

namespace Modules\Payment\Http\Requests;

use App\Http\Requests\BaseFormRequest;

class SecurityInitRequest extends BaseFormRequest
{
    public function rules(): array
    {
        return [
            'password' => 'bail|required|regex:/[0-9]{6}/',
            'repass'   => 'bail|required|same:password',
        ];
    }

    public function messages(): array
    {
        return [
            'password.required' => '支付密码必须填写',
            'password.regex'    => '支付密码只能使用6位数字',
            'repass.required'   => '重复密码必须填写',
            'repass.same'       => '两次输入的密码不一致',
        ];
    }
}