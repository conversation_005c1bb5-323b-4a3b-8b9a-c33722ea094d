<?php

namespace Modules\Payment\Http\Requests;

use App\Http\Requests\BaseFormRequest;

class UnifyInitRequest extends BaseFormRequest
{
    public function rules(): array
    {
        return [
            'order_class' => 'required',
            'order_ids'   => 'bail|required|array',
        ];
    }

    public function messages(): array
    {
        return [
            'order_class.required' => '订单类参数错误',
            'order_ids.required'   => '订单参数错误',
            'order_ids.array'      => '订单参数错误',
        ];
    }
}
