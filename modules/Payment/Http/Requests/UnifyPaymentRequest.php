<?php

namespace Modules\Payment\Http\Requests;

use App\Http\Requests\BaseFormRequest;
use Illuminate\Validation\Rule;
use Modules\Payment\Enums\Gateway;

class UnifyPaymentRequest extends BaseFormRequest
{
    public function rules(): array
    {
        return [
            'order_class' => 'required',
            'order_ids'   => 'bail|required|array',
            'gateway'     => [
                'bail',
                'required',
                Rule::in(Gateway::values()),
            ],
            'channel'     => [
                'required',
            ],
            //            'security'    => [
            //                'bail',
            //                'nullable',
            //                'required_if:gateway,'.Gateway::BALANCE->value,
            //                config('user.STRONG_PASSWORD') ? Password::min(8)->mixedCase()->numbers() : Password::min(6),
            //            ]
        ];
    }

    public function messages(): array
    {
        return [
            'order_class.required' => '订单类参数错误',
            'order_ids.required'   => '订单参数错误',
            'order_ids.array'      => '订单参数错误',
            'gateway.required'     => '请选择支付渠道',
            'gateway.in'           => '支付渠道错误',
            'channel.required'     => '请传入支付平台',
            //            'security.required_if' => '余额支付请输入支付密码',
            //            'security.min'         => '支付密码最少8位',
            //            'security.mixed'       => '支付密码格式错误',
        ];
    }
}
