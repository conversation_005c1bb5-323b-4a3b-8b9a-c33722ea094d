<?php

namespace Modules\Payment\Http\Requests;

use App\Http\Requests\BaseFormRequest;

class UnifyQueryRequest extends BaseFormRequest
{
    public function rules(): array
    {
        return [
            'payment_class' => 'required',
            'payment_id'    => 'required',
        ];
    }

    public function messages(): array
    {
        return [
            'payment_class.required' => '订单类参数错误',
            'payment_id.required'    => '订单参数错误',
        ];
    }
}