<?php

namespace Modules\Payment\Http\Renders;

use Dcat\Admin\Support\LazyRenderable;
use Dcat\Admin\Widgets\Card;
use Modules\Payment\Models\Refund;

class RefundResultRender extends LazyRenderable
{
    public function render(): string
    {
        $refund  = Refund::find($this->payload['refund_id']);
        $payment = $refund->payment;
        $value   = $payment->getAdapter()->refundQuery($refund->no);

        return Card::make('<pre>'.json_encode($value, JSON_PRETTY_PRINT | JSON_UNESCAPED_UNICODE).'</pre>');
    }
}