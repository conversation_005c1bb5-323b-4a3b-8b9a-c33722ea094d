<?php

namespace Modules\Payment\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Modules\Payment\Drivers\PaymentFactory;

class GatewayResource extends JsonResource
{
    public function toArray(Request $request): array
    {

        return [
            'gateway'  => $this->resource->value,
            'name'     => $this->resource->toString(),
            'channels' => PaymentFactory::enabledChannels($this->resource),
        ];
    }
}