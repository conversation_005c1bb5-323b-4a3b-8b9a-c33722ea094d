<?php

namespace Modules\Payment\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class WithdrawResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'no'          => $this->no,
            'channel'     => new WithdrawChannelResource($this->channel),
            'amount'      => $this->amount,
            'tax'         => $this->tax,
            'take'        => $this->take,
            'status'      => $this->status,
            'status_text' => $this->status->toString(),
            'paid_at'     => (string) $this->paid_at,
            'created_at'  => (string) $this->created_at,
        ];
    }
}