<?php

namespace Modules\Payment\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Crypt;

class PaymentableResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'paymentable' => Crypt::encryptString($this->getMorphClass().'|'.$this->id),
            'no'          => $this->no,
            'total'       => $this->getTotalAmount(),
        ];
    }
}