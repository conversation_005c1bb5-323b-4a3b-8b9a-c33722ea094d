<?php

namespace Modules\Payment\Http\Forms;

use Dcat\Admin\Contracts\LazyRenderable;
use Dcat\Admin\Http\JsonResponse;
use Dcat\Admin\Traits\LazyWidget;
use Dcat\Admin\Widgets\Form;
use Exception;
use Modules\Payment\Enums\CorporateStatus;
use Modules\Payment\Models\Corporate;

class CorporateAuditForm extends Form implements LazyRenderable
{
    use LazyWidget;

    public function handle(array $input): JsonResponse
    {
        try {
            $corporate = Corporate::find($this->payload['corporate_id']);

            $status = $input['status'];
            $remark = $input['remark'];

            if ($status == CorporateStatus::PASS->value) {
                $corporate->pass($remark);
            }
            if ($status == CorporateStatus::REFUSE->value) {
                $corporate->refuse($remark);
            }

            return $this->response()->success('审核成功')->refresh();
        } catch (Exception $exception) {
            return $this->response()->error($exception->getMessage());
        }
    }

    public function form(): void
    {
        $this->radio('status', '审核状态')
            ->options(CorporateStatus::STATUS_AUDIT_MAP)
            ->required();
        $this->textarea('remark', '备注信息');
        $this->password('password', '操作密码')
            ->rules('required|current_password:admin')
            ->help('请输入当前账户密码以验证您的身份')
            ->required();
    }

}