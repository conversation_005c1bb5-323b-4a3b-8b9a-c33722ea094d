<?php

namespace Modules\Payment\Http\Forms;

use Dcat\Admin\Admin;
use Dcat\Admin\Contracts\LazyRenderable;
use Dcat\Admin\Http\JsonResponse;
use Dcat\Admin\Traits\LazyWidget;
use Dcat\Admin\Widgets\Form;
use Modules\Payment\Models\Account;
use Modules\Payment\Models\AccountRule;

class AdjustmentForm extends Form implements LazyRenderable
{
    use LazyWidget;

    /**
     * @throws \Modules\Payment\Exceptions\RuleException
     */
    public function handle(array $input): JsonResponse
    {
        extract($input);

        if ($total == 0) {
            return $this->response()->error('调整额度不能为0');
        }

        $account = Account::find($this->payload['accountId']);
        $rule    = AccountRule::find($rule_id);

        $extDay = blank($input['ext_datetime']) ? $input['ext_day'] : $input['ext_datetime'];
        $result = $account->exec($rule, $total, $extDay, [
            'user'   => Admin::user()->getKey(),
            'remark' => $remark,
        ]);

        if ($result) {
            return $this->response()->success('操作成功')->refresh();
        } else {
            return $this->response()->error('操作失败');
        }
    }

    public function form(): void
    {
        $this->confirm('确定要调整账户余额么？');
        $this->select('rule_id', '执行规则')
            ->ajax(route('admin.payment.rules.ajax'))
            ->required();
        $this->currency('total', '调整金额')
            ->default(0)
            ->required();
        $this->number('ext_day', '有效期(天)')
            ->default(0)
            ->help('0为永久有效');
        $this->date('ext_datetime', '指定日期(天)')
            ->help('指定日期优先级大于有效天数');
        $this->text('remark', '备注(前端展示)');
        $this->password('password', '操作密码')
            ->rules('required|current_password:admin')
            ->help('请输入当前账户密码以验证您的身份')
            ->required();
    }
}