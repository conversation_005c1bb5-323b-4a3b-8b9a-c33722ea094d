<?php

namespace Modules\Payment\Http\Actions;

use Dcat\Admin\Actions\Response;
use Dcat\Admin\Grid\RowAction;
use Modules\Payment\Models\AccountLog;

class UnFreezeAccountLog extends RowAction
{
    protected string $title = '手动解冻';

    /**
     * @throws \Exception
     */
    public function handle(): Response
    {
        $log = AccountLog::find($this->getKey());

        if ($log->unfreeze()) {
            return $this->response()->success('解冻成功')->refresh();
        } else {
            return $this->response()->error('解冻失败');
        }
    }

    public function confirm(): array
    {
        return [
            '解冻记录',
            '确认要解冻这条记录么？一旦操作不可逆！',
        ];
    }

    protected function authorize($user): bool
    {
        return $user->isAdministrator();
    }
}