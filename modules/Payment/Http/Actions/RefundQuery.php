<?php

namespace Modules\Payment\Http\Actions;

use Dcat\Admin\Grid\RowAction;
use Dcat\Admin\Widgets\Modal;
use Modules\Payment\Http\Renders\RefundResultRender;

class RefundQuery extends RowAction
{
    protected string $title = '退款查询';

    public function render(): string
    {
        return Modal::make()
            ->lg()
            ->centered()
            ->title($this->title)
            ->body(RefundResultRender::make()->payload(['refund_id' => $this->getKey()]))
            ->button($this->title);
    }

    protected function authorize($user): bool
    {
        return $user->isAdministrator();
    }
}