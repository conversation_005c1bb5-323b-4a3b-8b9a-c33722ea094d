<?php

namespace Modules\Payment\Http\Actions;

use Dcat\Admin\Grid\RowAction;
use Dcat\Admin\Widgets\Modal;
use Modules\Payment\Http\Forms\AdjustmentForm;

class AdjustmentAccount extends RowAction
{
    protected string $title = '账户调账';

    public function render(): string
    {
        return Modal::make()
            ->lg()
            ->centered()
            ->title($this->getRow()->user->showName.' '.$this->title)
            ->body(AdjustmentForm::make()->payload(['accountId' => $this->getKey()]))
            ->button($this->title);
    }

    protected function authorize($user): bool
    {
        return $user->isAdministrator();
    }
}