<?php

namespace Modules\Payment\Http\Actions;

use Dcat\Admin\Grid\RowAction;
use Dcat\Admin\Widgets\Modal;
use Modules\Payment\Http\Renders\PaymentResultRender;

class PaymentQuery extends RowAction
{
    protected string $title = '支付查询';

    public function render(): string
    {
        return Modal::make()
            ->lg()
            ->centered()
            ->title($this->title)
            ->body(PaymentResultRender::make()->payload(['payment_id' => $this->getKey()]))
            ->button($this->title);
    }

    protected function authorize($user): bool
    {
        return $user->isAdministrator();
    }
}