<?php

namespace Modules\Payment\Http\Controllers\Api;

use App\Facades\Api;
use App\Http\Controllers\ApiController;
use Illuminate\Http\JsonResponse;
use Modules\Payment\Http\Requests\BalancePaymentRequest;
use Modules\Payment\Http\Resources\BalancePayInfoResource;
use Modules\Payment\Models\Balance;

class BalanceController extends ApiController
{
    /**
     * Notes   : 获取支付订单信息
     *
     * @Date   : 2023/4/27 16:22
     * <AUTHOR> <Jason.C>
     * @param  \Modules\Payment\Models\Balance  $balance
     * @return \Illuminate\Http\JsonResponse
     * @throws \Exception
     */
    public function info(Balance $balance): JsonResponse
    {
        $this->checkPermission($balance->account);

        if (! Api::user()->security->password) {
            return $this->failed('未设置支付密码');
        }

        return $this->success(new BalancePayInfoResource($balance));
    }

    /**
     * Notes   : 通过交易密码支付，会返回支付结果的查询地址，支付为异步，防止重复支付
     *
     * @Date   : 2023/3/28 11:14
     * <AUTHOR> <Jason.C>
     * @param  \Modules\Payment\Models\Balance  $balance
     * @return \Illuminate\Http\JsonResponse
     * @throws \Exception
     */
    public function pay(Balance $balance): JsonResponse
    {
        $this->checkPermission($balance->account);

        $balance->pay();

        return $this->success('支付请求已提交，请等待支付结果');
    }
}