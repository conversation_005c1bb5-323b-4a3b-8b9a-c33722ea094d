<?php

namespace Modules\Payment\Http\Controllers\Api;

use App\Facades\Api;
use App\Http\Controllers\ApiController;
use Exception;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\Relation;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Crypt;
use Modules\Payment\Drivers\PaymentFactory;
use Modules\Payment\Enums\Channel;
use Modules\Payment\Enums\CorporateStatus;
use Modules\Payment\Enums\Gateway;
use Modules\Payment\Http\Requests\CorporateEditRequest;
use Modules\Payment\Http\Requests\CorporateRequest;
use Modules\Payment\Http\Resources\CorporateResource;
use Modules\Payment\Models\Corporate;
use Modules\Payment\Models\Payment;
use Modules\User\Models\IdentityOrder;

class CorporateController extends ApiController
{
    public function index(Request $request)
    {
        $model_type = $request->model_type;// order 商城单  identity_order 身份单
        $corporate  = Corporate::where('orderable_type', $model_type)
            ->whereHas('payment', function (Builder $builder) {
                $builder->where('user_id', Api::id());
            })
            ->latest()
            ->whereIn('status', [
                CorporateStatus::INIT,
                CorporateStatus::REFUSE,
            ])
            ->first();

        $data = null;
        if ($corporate) {
            $model_id = $corporate->orderable->id;
            if ($corporate->orderable instanceof IdentityOrder) {
                $model_id = $corporate->orderable->identity_id;
            }
            $data = [
                'state'    => $corporate->orderable->getCorporateStatus(),
                'model_id' => $model_id,
            ];
        }

        return $this->success($data);
    }

    /**
     * Notes: 前置条件
     *
     * @Author: 玄尘
     * @Date: 2023/9/8 9:57
     */
    public function create(Request $request): JsonResponse
    {
        $paymentable = $request->paymentable;
        if (! $paymentable) {
            return $this->failed('缺少paymentable');
        }
        $decrypted   = Crypt::decryptString($request->paymentable);
        $target      = explode('|', $decrypted);
        $class       = Relation::getMorphedModel($target[0]);
        $paymentable = $class::find($target[1]);
        $params      = $paymentable->getPaymentParams(Gateway::CORPORATE->value, Channel::CORPORATE_BANK->value);

        return $this->success($params);
    }

    /**
     * Notes: 提交线下打款信息
     *
     * @Author: 玄尘
     * @Date: 2023/9/8 10:14
     */
    public function store(CorporateRequest $request): JsonResponse
    {
        try {
            $payment_no = $request->safe()->payment_no;
            $payment    = Payment::where('no', $payment_no)->first();
            $exists     = Corporate::where([
                    'payment_id'     => $payment->id,
                    'orderable_type' => $payment->paymentable_type,
                    'orderable_id'   => $payment->paymentable_id,
                ])
                ->whereIn('status', [CorporateStatus::INIT])
                ->exists();
            if ($exists) {
                throw new Exception('您已经提交过请勿重复提交');
            }
            $factory = new PaymentFactory($payment);
            Corporate::create([
                    'payment_id'     => $payment->id,
                    'orderable_type' => $payment->paymentable_type,
                    'orderable_id'   => $payment->paymentable_id,
                    'name'           => $request->safe()->name ?? '',
                    'amount'         => $request->safe()->amount,
                    'bank_no'        => $request->safe()->bank_no,
                    'cover'          => $request->safe()->cover,
                    'source'         => $factory->getPaymentParams(),
                    'status'         => CorporateStatus::INIT->value,
                ]);
            return $this->success('提交成功,请等待审核');
        } catch (Exception $exception) {
            return $this->failed($exception->getMessage());
        }
    }

    /**
     * Notes: 查看打款凭证
     *
     * @Author: 玄尘
     * @Date: 2023/9/8 10:35
     * @param  \Modules\Payment\Models\Corporate  $corporate
     * @return \Illuminate\Http\JsonResponse
     */
    public function show(Corporate $corporate): JsonResponse
    {
        return $this->success(new CorporateResource($corporate));
    }

    /**
     * Notes: 编辑前置
     *
     * @Author: 玄尘
     * @Date: 2023/9/8 10:52
     * @param  \Modules\Payment\Models\Corporate  $corporate
     * @return \Illuminate\Http\JsonResponse
     * @throws \Exception
     */
    public function edit(Corporate $corporate): JsonResponse
    {
        $payment = $corporate->payment;
        $factory = new PaymentFactory($payment);

        $data = [
            'info'    => new CorporateResource($corporate),
            'payment' => $factory->getPaymentParams(),
        ];
        return $this->success($data);
    }

    /**
     * Notes: 编辑
     *
     * @Author: 玄尘
     * @Date: 2023/9/8 10:59
     */
    public function update(CorporateEditRequest $request, Corporate $corporate): JsonResponse
    {
        try {
            $corporate->update([
                'name'    => $request->safe()->name ?? '',
                'amount'  => $request->safe()->amount,
                'bank_no' => $request->safe()->bank_no,
                'cover'   => $request->safe()->cover,
                'remark'  => '',
                'status'  => CorporateStatus::INIT->value,
            ]);
            return $this->success('编辑成功，请等待审核');
        } catch (Exception $exception) {
            return $this->failed($exception->getMessage());
        }
    }
}