<?php

namespace Modules\Payment\Http\Controllers\Api;

use App\Facades\Api;
use App\Http\Controllers\ApiController;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Modules\Payment\Enums\AccountType;
use Modules\Payment\Http\Requests\RechargeRequest;
use Modules\Payment\Http\Resources\PaymentableResource;
use Modules\Payment\Http\Resources\RechargeCollection;
use Modules\Payment\Models\Recharge;

class RechargeController extends ApiController
{
    /**
     * Notes   : 用户储值记录
     *
     * @Date   : 2023/10/25 15:30
     * <AUTHOR> <Jason.C>
     * @param  \Illuminate\Http\Request  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(Request $request): JsonResponse
    {
        $result = Recharge::ofUser(Api::user())
            ->select(['no', 'type', 'amount', 'receipts', 'status', 'created_at'])
            ->latest()
            ->paginate($request->limit);

        return $this->success(new RechargeCollection($result));
    }

    /**
     * Notes   : 获取个人账户的类型
     *
     * @Date   : 2023/5/19 12:03
     * <AUTHOR> <Jason.C>
     * @return \Illuminate\Http\JsonResponse
     */
    public function types(): JsonResponse
    {
        return $this->success(AccountType::ACCOUNT_TYPE_MAP);
    }

    /**
     * Notes   : 创建支付订单
     *
     * @Date   : 2023/5/23 11:16
     * <AUTHOR> <Jason.C>
     * @param  \Modules\Payment\Http\Requests\RechargeRequest  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function create(RechargeRequest $request): JsonResponse
    {
        $type = AccountType::tryFrom($request->safe()->type);

        if (! in_array($request->safe()->type, config('payment.CAN_RECHARGE_TYPES'))) {
            return $this->failed(sprintf('暂时不允许[%s]类型的储值', $type->toString()));
        }

        $order = Recharge::create([
            'user'   => Api::user(),
            'type'   => $type,
            'amount' => $request->safe()->amount,
        ]);

        return $this->success(new PaymentableResource($order));
    }
}
