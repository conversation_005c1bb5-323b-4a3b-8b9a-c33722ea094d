<?php

namespace Modules\Payment\Http\Controllers\Api;

use App\Facades\Api;
use App\Http\Controllers\ApiController;
use Illuminate\Contracts\Database\Eloquent\Builder as WithBuilder;
use Illuminate\Http\JsonResponse;
use Modules\Payment\Http\Requests\BankStoreRequest;
use Modules\Payment\Http\Resources\BankCardResource;
use Modules\Payment\Http\Resources\BankResource;
use Modules\Payment\Models\Bank;
use Modules\Payment\Models\BankCard;

class BankCardController extends ApiController
{
    /**
     * Notes   : 可用的银行列表
     *
     * @Date   : 2023/10/25 15:28
     * <AUTHOR> <Jason.C>
     * @return \Illuminate\Http\JsonResponse
     */
    public function banks(): JsonResponse
    {
        $banks = Bank::select(['id', 'name', 'short', 'cover'])
            ->ofEnabled()
            ->ordered()
            ->get();

        return $this->success(BankResource::collection($banks));
    }

    /**
     * Notes   : 我的银行卡
     *
     * @Date   : 2023/4/18 15:09
     * <AUTHOR> <Jason.C>
     * @return \Illuminate\Http\JsonResponse
     */
    public function cards(): JsonResponse
    {
        $cards = BankCard::ofUser(Api::user())
            ->select(['id', 'name', 'card_no', 'can_withdraw', 'can_quick', 'created_at', 'created_at', 'bank_id'])
            ->with([
                'bank' => function (WithBuilder $query) {
                    $query->select(['id', 'name', 'short', 'cover']);
                }
            ])
            ->latest()
            ->get();

        return $this->success(BankCardResource::collection($cards));
    }

    /**
     * Notes   : 新增银行卡
     *
     * @Date   : 2023/4/18 15:09
     * <AUTHOR> <Jason.C>
     * @param  \Modules\Payment\Http\Requests\BankStoreRequest  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function store(BankStoreRequest $request): JsonResponse
    {
        BankCard::create([
            'user'         => Api::user(),
            'bank_id'      => $request->bank_id,
            'name'         => $request->name,
            'card_no'      => $request->card_no,
            'can_withdraw' => $request->can_withdraw ?? false,
            'can_quick'    => $request->can_quick ?? false,
        ]);
        return $this->success();
    }

    /**
     * Notes   : 删除银行卡
     *
     * @Date   : 2023/4/18 15:09
     * <AUTHOR> <Jason.C>
     * @param  \Modules\Payment\Models\BankCard  $card
     * @return \Illuminate\Http\JsonResponse
     * @throws \Exception
     */
    public function destroy(BankCard $card): JsonResponse
    {
        $this->checkPermission($card);

        return $this->success($card->delete());
    }
}