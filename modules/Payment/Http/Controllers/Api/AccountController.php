<?php

namespace Modules\Payment\Http\Controllers\Api;

use App\Facades\Api;
use App\Http\Controllers\ApiController;
use Illuminate\Contracts\Database\Eloquent\Builder as WithBuilder;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Modules\Payment\Enums\AccountType;
use Modules\Payment\Http\Requests\AccountLogRequest;
use Modules\Payment\Http\Resources\AccountLogCollection;
use Modules\Payment\Http\Resources\AccountResource;

class AccountController extends ApiController
{
    /**
     * Notes   : 我的账户
     *
     * @Date   : 2023/5/24 14:58
     * <AUTHOR> <Jason.C>
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(): JsonResponse
    {
        $account = Api::user()->account;

        return $this->success(new AccountResource($account));
    }

    public function score(Request $request): JsonResponse
    {
        $account = Api::user()->account;
        $logs    = $account->logs()
            ->where('type', 'score')
            ->latest()
            ->paginate($request->limit ?: 10);

        return $this->success([
            'sum'   => [
                'balance' => amountFormat($account->score),
                'today'   => amountFormat($account->logs()
                    ->where('type', 'score')
                    ->whereDate('created_at', Carbon::today())
                    ->where('amount', '>', 0)
                    ->sum('amount')),
                'total'   => amountFormat($account->logs()
                    ->where('type', 'score')
                    ->where('amount', '>', 0)
                    ->sum('amount')),
            ],
            'lists' => new AccountLogCollection($logs),
        ]);
    }

    /**
     * Notes   : 账户记录
     *
     * @Date   : 2023/6/15 10:49
     * <AUTHOR> <Jason.C>
     * @param  \Modules\Payment\Http\Requests\AccountLogRequest  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function logs(AccountLogRequest $request): JsonResponse
    {
        $type    = $request->type;
        $sTime   = $request->s_time;
        $eTime   = $request->e_time;
        $sAmount = $request->s_amount;
        $eAmount = $request->e_amount;

        $result = Api::user()->account->logs()
            ->with('rule')
            ->select([
                'rule_id', 'type', 'amount', 'balance', 'frozen', 'source',
                'unfreeze_at', 'expired_at', 'is_expired', 'created_at',
            ])
            ->with([
                'rule' => function (WithBuilder $query) {
                    $query->select(['id', 'name', 'remark']);
                },
            ])
            ->when($type, function (Builder $builder, $type) {
                $type = AccountType::tryFrom($type);
                if (! is_null($type)) {
                    $builder->where('type', $type);
                }
            })
            ->when($sTime, function (Builder $builder, $sTime) {
                $builder->where('created_at', '>=', $sTime);
            })
            ->when($eTime, function (Builder $builder, $eTime) {
                $builder->where('created_at', '<=', $eTime);
            })
            ->when($sAmount, function (Builder $builder, $sAmount) {
                $builder->where('amount', '>=', $sAmount);
            })
            ->when($eAmount, function (Builder $builder, $eAmount) {
                $builder->where('amount', '<=', $eAmount);
            })
            ->latest()
            ->paginate();

        return $this->success(new AccountLogCollection($result));
    }

    public function balance(Request $request): JsonResponse
    {
        $account = Api::user()->account;
        $logs    = $account->logs()
            ->where('type', 'balance')
            ->latest()
            ->paginate($request->limit ?: 10);
        return $this->success([
            'sum'   => [
                'balance' => $account->balance,
                'today'   => $account->logs()
                    ->where('type', 'balance')
                    ->whereDate('created_at', now()->toDateString())
                    ->where('amount', '>', 0)
                    ->sum('amount'),
                'total'   => $account->logs()
                    ->where('type', 'balance')
                    ->where('amount', '>', 0)
                    ->sum('amount'),
            ],
            'lists' => new AccountLogCollection($logs),
        ]);
    }

    /**
     * Notes   : 分类型的账户记录
     *
     * @Date   : 2023/10/25 15:29
     * <AUTHOR> <Jason.C>
     * @param  \Modules\Payment\Enums\AccountType  $type
     * @return \Illuminate\Http\JsonResponse
     */
    public function typeLogs(AccountType $type): JsonResponse
    {
        $result = Api::user()->account->logs()
            ->where('type', $type)
            ->with('rule')
            ->select([
                'rule_id', 'type', 'amount', 'balance', 'frozen', 'source',
                'unfreeze_at', 'expired_at', 'is_expired', 'created_at',
            ])
            ->with([
                'rule' => function (WithBuilder $query) {
                    $query->select(['id', 'name', 'remark']);
                },
            ])
            ->latest()
            ->paginate();
        return $this->success(new AccountLogCollection($result));
    }
}
