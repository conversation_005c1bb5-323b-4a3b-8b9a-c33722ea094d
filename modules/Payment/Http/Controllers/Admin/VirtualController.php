<?php

namespace Modules\Payment\Http\Controllers\Admin;

use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;
use Modules\Payment\Drivers\PaymentFactory;
use Modules\Payment\Enums\Gateway;
use Modules\Payment\Models\Virtual;

class VirtualController extends AdminController
{
    protected string $title = '虚拟支付';

    public function grid(): Grid
    {
        return Grid::make(Virtual::with(['user', 'payment'])->latest(), function (Grid $grid) {
            $grid->disableCreateButton();
            $grid->disableActions();
            $grid->disableRowSelector();

            $grid->filter(function (Grid\Filter $filter) {
                $filter->like('payment.no', '支付单号');
                $filter->like('user.username', '操作用户');
                $filter->like('user.info.nickname', '用户昵称');
                $filter->equal('gateway', '网关')
                    ->select(Gateway::GATEWAY_MAP);
                $filter->between('amount', '金额');
                $filter->like('remark', '备注');
                $filter->between('paid_at', '支付时间')
                    ->datetime();
                $filter->between('created_at', '创建时间')
                    ->datetime();
            });

            $grid->quickSearch('payment.no', 'user.username', 'user.info.nickname')
                ->placeholder('支付单号/用户名/用户昵称');

            $grid->column('payment.no', '支付单号')
                ->copyable();
            $grid->column('操作用户')
                ->display(fn() => $this->user->showName);
            $grid->column('gateway', '网关')
                ->display(fn() => $this->gateway->toString())
                ->label();
            $grid->column('渠道')
                ->display(fn() => PaymentFactory::channels($this->gateway)[$this->channel->toDriver()] ?? 'UNKNOWN')
                ->dot();
            $grid->column('amount', '金额');
            $grid->column('remark', '备注');
            $grid->column('paid_at', '支付时间');
            $grid->column('created_at');
        });
    }
}
