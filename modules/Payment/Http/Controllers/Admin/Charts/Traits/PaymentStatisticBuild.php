<?php
namespace Modules\Payment\Http\Controllers\Admin\Charts\Traits;

use Carbon\Carbon;
use Illuminate\Support\Facades\DB;
use Modules\Payment\Enums\PaymentStatus;
use Modules\Payment\Models\Payment;

trait PaymentStatisticBuild
{
    protected function paymentDaysCount(int $day): array
    {
        // 日期
        for ($i = $day; $i >= 0; $i--) {
            $date = Carbon::now()->subDay($i)->format("Y-m-d");
            $dates[$date] = [
                "payment_date"  => $date,
                "payment_count" => 0,
                "total_amount"  => 0,
                "income_amount" => 0,
                "refund_amount" => 0,
            ];
        }
        // 日期订单数
        $where = [
            Carbon::now()->today()->subDay($day),
            Carbon::now()
        ];
        $selectRow = [
            'DATE_FORMAT(created_at,"%Y-%m-%d") as date_day',
            'COUNT(IF(status <> "'.PaymentStatus::UNPAY->value.'",true,null)) as payment_count',
            'SUM(IF(status = "'.PaymentStatus::PAID->value.'"   or status = "'.PaymentStatus::PAID_PART->value.'"  ,amount,0)) as income_amount',
            'SUM(IF(status = "'.PaymentStatus::REFUND->value.'" or status = "'.PaymentStatus::REFUND_PART->value.'",amount,0)) as refund_amount',
        ];
        $dateCount = Payment::whereBetween('created_at', $where)
            ->select(DB::raw(implode(',', $selectRow)))
            ->whereNotIn('status', [1])
            ->groupBy('date_day')
            ->get();
        // 日期订单数赋值
        foreach ($dateCount as $item) {
            if (isset($dates[$item->date_day])) {
                $dates[$item->date_day]['payment_count']   = $item->payment_count;
                $dates[$item->date_day]['total_amount']  = bcadd($item->income_amount, $item->refund_amount, 2);
                $dates[$item->date_day]['income_amount'] = $item->income_amount;
                $dates[$item->date_day]['refund_amount'] = $item->refund_amount;
            }
        }
        // 待返回数据处理
        $data = [
            [ 'name' => '支付订单数', 'data' => [], 'type' => 'bar' ],
            [ 'name' => '收款总金额', 'data' => [] ],
            [ 'name' => '实收总金额', 'data' => [] ],
            [ 'name' => '退款总金额', 'data' => [] ],
        ];
        foreach ($dates ?? [] as $item) {
            $categories[]      = $item['payment_date'];
            $data[0]['data'][] = $item['payment_count'];
            $data[1]['data'][] = $item['total_amount'];
            $data[2]['data'][] = $item['income_amount'];
            $data[3]['data'][] = $item['refund_amount'];
        }

        return [
            'data'       => $data,
            'categories' => $categories ?? [],
        ];
    }
}
