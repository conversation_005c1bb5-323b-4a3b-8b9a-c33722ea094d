<?php

namespace Modules\Payment\Http\Controllers\Admin;

use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;
use Dcat\Admin\Layout\Content;
use Modules\Payment\Drivers\PaymentFactory;
use Modules\Payment\Enums\Channel;
use Modules\Payment\Enums\Gateway;
use Modules\Payment\Enums\PaymentStatus;
use Modules\Payment\Enums\RefundStatus;
use Modules\Payment\Http\Actions\PaymentQuery;
use Modules\Payment\Http\Actions\PaymentRefund;
use Modules\Payment\Http\Actions\VirtualPay;
use Modules\Payment\Models\Payment;
use Modules\Payment\Models\Virtual;

class PaymentController extends AdminController
{
    protected string $title = '支付订单';

    public function grid(): Grid
    {
        $model = Payment::with(['user.info', 'paymentable'])
            ->withCount(['refunds', 'virtuals'])
            ->withSum([
                'refunds' => function ($q) {
                    $q->where('status', RefundStatus::COMPLETE);
                }
            ], 'amount')
            ->latest();
        return Grid::make($model, function (Grid $grid) {
            $grid->disableRowSelector();
            $grid->disableCreateButton();
            $grid->disableDeleteButton();
            $grid->disableEditButton();

            $grid->actions(function (Grid\Displayers\Actions $actions) {
                if ($actions->row->status == PaymentStatus::UNPAY) {
                    $actions->append(new VirtualPay);
                } else {
                    $actions->append(new PaymentQuery);
                }
                if (in_array($actions->row->status,
                        [
                            PaymentStatus::PAID, PaymentStatus::PAID_PART, PaymentStatus::REFUND_PART
                        ]) && $actions->row->gateway != Gateway::PAYPAL) {
                    $actions->append(new PaymentRefund);
                }
            });

            $grid->filter(function (Grid\Filter $filter) {
                $filter->like('no', '支付单号');
                $filter->like('user.username', '下单用户');
                $filter->like('user.info.nickname', '用户昵称');
                $filter->equal('gateway', '支付网关')
                    ->select(Gateway::GATEWAY_MAP);
                $filter->equal('status', '支付状态')
                    ->select(PaymentStatus::STATUS_MAP);
                $filter->between('paid_at', '付款时间')
                    ->datetime();
                $filter->between('created_at', '下单时间')
                    ->datetime();
            });

            $grid->quickSearch('no', 'user.username', 'user.info.nickname')
                ->placeholder('订单编号/用户名/用户昵称');
            $grid->column('id', 'ID');
            $grid->column('no', '支付单号')
                ->copyable();
            $grid->column('支付用户')
                ->display(fn() => $this->user->showName);
            $grid->column('paymentable_type', '支付对象')
                ->display(fn() => $this->paymentable?->getTitle())
                ->label();
            $grid->column('gateway', '支付网关')
                ->display(fn($status) => $status->toString())
                ->label(Gateway::GATEWAY_LABEL);
            $grid->column('channel', '通道')
                ->display(fn($channel) => $channel->toString())
                ->label(Channel::CHANNEL_LABEL);
            $grid->column('amount', '订单总额');
            $grid->column('refunds_sum_amount', '退款总额');
            $grid->column('status', '支付状态')
                ->display(fn($status) => $status->toString())
                ->label(PaymentStatus::STATUS_LABEL);
            $grid->column('paid_at', '付款时间');
            $grid->column('refunds_count', '退款单')
                ->link(fn() => route('admin.payment.refunds', ['payment_id' => $this->id]));
            $grid->column('virtuals_count', '虚拟支付')
                ->link(fn() => route('admin.payment.payments.virtuals', ['payment' => $this]));
            $grid->column('created_at');
        });
    }

    /**
     * Notes   : 虚拟支付
     *
     * @Date   : 2023/4/26 10:31
     * <AUTHOR> <Jason.C>
     * @param  \Dcat\Admin\Layout\Content  $content
     * @param  \Modules\Payment\Models\Payment  $payment
     * @return \Dcat\Admin\Layout\Content
     */
    public function virtuals(Content $content, Payment $payment)
    {
        return $content
            ->header($payment->no)
            ->description('手工支付记录')
            ->body(Grid::make(Virtual::where('payment_id', $payment->getKey()),
                function (Grid $grid) {
                    $grid->disableCreateButton();
                    $grid->disableActions();
                    $grid->disableRowSelector();

                    $grid->column('操作用户')
                        ->display(fn() => $this->user->showName);
                    $grid->column('gateway', '网关')
                        ->display(fn() => $this->gateway->toString())
                        ->label();
                    $grid->column('渠道')
                        ->display(fn() => PaymentFactory::channels($this->gateway)[$this->channel])
                        ->dot();
                    $grid->column('amount', '金额');
                    $grid->column('remark', '备注');
                    $grid->column('paid_at', '支付时间');
                    $grid->column('created_at');
                }));
    }
}
