<?php

namespace Modules\Payment\Http\Controllers\Admin;

use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;
use Modules\Payment\Enums\AccountType;
use Modules\Payment\Http\Actions\AdjustmentAccount;
use Modules\Payment\Http\Actions\InitAccountTool;
use Modules\Payment\Models\Account;

class AccountController extends AdminController
{
    protected string $title = '用户账户';

    public function grid(): Grid
    {
        return Grid::make(Account::with(['user.info'])->latest('id'), function (Grid $grid) {
            $grid->disableCreateButton();
            $grid->disableEditButton();
            $grid->disableDeleteButton();
            $grid->disableRowSelector();
            $typeMap = AccountType::ACCOUNT_TYPE_MAP;

            $grid->filter(function (Grid\Filter $filter) use ($typeMap) {
                $filter->like('user.username', '用户名称');
                $filter->like('user.info.nickname', '用户昵称');
                foreach ($typeMap as $type => $name) {
                    $filter->between($type, $name);
                }
                $filter->between('updated_at', '更新时间')
                    ->datetime();
            });

            $grid->quickSearch(['id', 'user.username', 'user.info.nickname'])
                ->placeholder('用户名称/用户昵称');

            $grid->tools([new InitAccountTool]);
            $grid->actions([new AdjustmentAccount]);

            $grid->column('用户名称')
                ->display(fn() => $this->user->showName);
            foreach ($typeMap as $type => $name) {
                $grid->column($type, $name)
                    ->sortable();
            }
            $grid->column('updated_at');
        });
    }
}
