<?php

use Illuminate\Routing\Router;
use Illuminate\Support\Facades\Route;
use Modules\User\Http\Controllers\Admin\CertificationController;
use Modules\User\Http\Controllers\Admin\DashboardController;
use Modules\User\Http\Controllers\Admin\DepartmentApplyController;
use Modules\User\Http\Controllers\Admin\DepartmentController;
use Modules\User\Http\Controllers\Admin\DepartmentJoinController;
use Modules\User\Http\Controllers\Admin\DeviceController;
use Modules\User\Http\Controllers\Admin\IdentityController;
use Modules\User\Http\Controllers\Admin\IdentityLogController;
use Modules\User\Http\Controllers\Admin\IdentityOrderController;
use Modules\User\Http\Controllers\Admin\IndexController;
use Modules\User\Http\Controllers\Admin\InvoiceController;
use Modules\User\Http\Controllers\Admin\LogController;
use Modules\User\Http\Controllers\Admin\PermissionController;
use Modules\User\Http\Controllers\Admin\RoleController;
use Modules\User\Http\Controllers\Admin\SmsController;
use Modules\User\Http\Controllers\Admin\TokenController;
use Modules\User\Http\Controllers\Admin\UserIdentityController;
use Modules\User\Http\Controllers\Admin\UserReservationController;

Route::get('/', [DashboardController::class, 'index']);

Route::get('users/ajax', [IndexController::class, 'ajax'])->name('users.ajax');
Route::get('users/identities', [UserIdentityController::class, 'index']);
Route::resource('reservation', UserReservationController::class);
Route::resource('users', IndexController::class);
Route::get('identities/orders', [IdentityOrderController::class, 'index']);
Route::get('identities/logs', [IdentityLogController::class, 'index']);
Route::get('identities/ajax', [IdentityController::class, 'ajax'])->name('identities.ajax');
Route::resource('identities', IdentityController::class);
Route::get('certifications', [CertificationController::class, 'index']);

Route::get('departments/applies', [DepartmentApplyController::class, 'index']);
Route::get('departments/joins', [DepartmentJoinController::class, 'index']);

Route::get('departments/ajax', [DepartmentController::class, 'ajax'])->name('departments.ajax');
Route::resource('departments', DepartmentController::class);
Route::resource('invoice', InvoiceController::class);
# 权限
Route::get('roles/ajax', [RoleController::class, 'ajax'])->name('roles.ajax');
Route::resource('roles', RoleController::class);
Route::resource('permissions', PermissionController::class);

Route::group([
    'as'     => 'safety.',
    'prefix' => 'safety',
], function (Router $router) {
    $router->get('devices', [DeviceController::class, 'index']);
    $router->get('logs', [LogController::class, 'index']);
    $router->get('tokens', [TokenController::class, 'index']);
    $router->delete('tokens/{token}', [TokenController::class, 'destroy']);
});

Route::group([
    'as'     => 'sms.',
    'prefix' => 'sms',
], function (Router $router) {
    $router->get('logs', [SmsController::class, 'index']);
});
