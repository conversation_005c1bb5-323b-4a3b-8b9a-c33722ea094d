<?php

namespace Modules\User\Notifications;

use App\Models\User;
use Illuminate\Database\Eloquent\Model;
use Modules\Notification\Contracts\BaseNotification;
use Modules\Notification\Messages\DatabaseMessage;

class NewChildNode extends BaseNotification
{
    public function __construct(protected User $user)
    {
    }

    public static function getTitle(): string
    {
        return '新用户推荐成功';
    }

    public function toDatabase(Model $notifiable): array|DatabaseMessage
    {
        return new DatabaseMessage('推荐新用户【{username}】成功', [
            'username' => hideMobilePhoneNo($this->user->username),
            'user_id'  => $this->user->id,
        ]);
    }
}
