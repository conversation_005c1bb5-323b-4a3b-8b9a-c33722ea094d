<?php

namespace Modules\User\Notifications;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Modules\Notification\Contracts\BaseNotification;
use Modules\Notification\Messages\DatabaseMessage;
use Modules\User\Models\Identity;

class IdentityChangedNotification extends BaseNotification
{
    public function __construct(protected ?Identity $before, protected Identity $after)
    {
    }

    public static function getTitle(): string
    {
        return lecho('Notice of Identity Change');
    }

    public function toDatabase(Model $notifiable): array|DatabaseMessage
    {
        return new DatabaseMessage('您的身份已变更，变更前：【{before}】，变更后：【{after}】，变更时间：{time}。',
            [
                'before' => $this->before ? lecho($this->before->name) : lecho('none'),
                'after'  => lecho($this->after->name),
                'time'   => Carbon::now()->format('Y-m-d H:i:s'),
            ]);
    }
}