<?php

namespace Modules\User\Notifications;

use Illuminate\Database\Eloquent\Model;
use Modules\Notification\Contracts\BaseNotification;
use Modules\Notification\Messages\DatabaseMessage;
use Modules\User\Models\DepartmentJoin;

class NewJoinNotification extends BaseNotification
{
    public function __construct(protected DepartmentJoin $join)
    {
    }

    public static function getTitle(): string
    {
        return '新组织成员申请';
    }

    public function toDatabase(Model $notifiable): array|DatabaseMessage
    {
        return new DatabaseMessage('您的组织【{department}】有新的成员【{username}】申请加入，申请原由：{description}。', [
            'department'  => $this->join->department->name,
            'username'    => $this->join->user->username,
            'description' => $this->join->description,
        ]);
    }
}