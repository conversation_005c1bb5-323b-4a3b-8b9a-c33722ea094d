<?php

namespace Modules\User\Http\Resources;

use App\Facades\Api;
use App\Models\CardItem;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\DB;
use Modules\Cms\Models\Content;
use Modules\Coupon\Models\Collect;
use Modules\Interaction\Models\Subscribe;
use Modules\Mall\Enums\OrderStatus;
use Modules\Mall\Models\Order;
use Modules\User\Models\UserSignRule;

class UserPcInfoResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        $user = Api::user();

        $cmsCategoryId = 3;
        $cmsContent    = Content::ofEnabled()
            ->ofPassed()
            ->where('category_id', $cmsCategoryId)
            ->orderByDesc('created_at')
            ->select('id as content_id', 'title')
            ->limit(3)
            ->get();

        $daySign       = $this->signs()->ofDay(now())->first();
        $continuousDay = $this->signs()
            ->where('sign_at', '>', now()->subDay()->startOfDay())
            ->orderByDesc('sign_at')
            ->value('continuous_day') ?: 0;

        $orderCount = collect(Order::ofUser($user)
            ->whereNull('source->batch_id')
            ->select('status', DB::raw('count(1) as total'))
            ->groupBy('status')
            ->get()
            ->toArray());

        $orderData = [];
        foreach (OrderStatus::CLIENT_MAP as $key => $value) {
            if ($key == OrderStatus::DELIVERING->value) {
                continue;
            }
            if ($key == OrderStatus::PAID->value) {
                $total = $orderCount->whereIn('status', OrderStatus::CLIENT_WAIT_DELIVER)->sum('total');
            } elseif ($key == OrderStatus::SIGNED->value) {
                $total = $orderCount->where('status', $key)->whereNull('source->looked')->value('total') ?? 0;
            } else {
                $total = $orderCount->where('status', $key)->value('total') ?? 0;
            }

            $orderData[] = [
                'status'      => $key,
                'status_name' => $value,
                'total'       => $total,
            ];
        }
        // data 头部插入默认数组
        array_unshift($orderData, [
            'status'      => '',
            'status_name' => '全部',
            'total'       => $orderCount->sum('total')
        ]);
        $timeBetween = [now()->startOfMonth()->startOfWeek(), now()->endOfMonth()->endOfWeek()];
        $signed      = $user->signs()
            ->ofBetween($timeBetween)
            ->select('*', DB::raw('DATE_FORMAT(sign_at,"%Y-%m-%d") as sign_at_day'))
            ->get();
        $daies       = [];
        for ($time = $timeBetween[0]; $time < $timeBetween[1]; $time->addDay()) {
            $daies[] = [
                'date'          => $time->toDateString(),
                'day'           => $time->day,
                'current_month' => $time->isCurrentMonth(),
                'is_sign'       => (bool) $signed->where('sign_at_day', $time->toDateString())->first(),
                'today'         => $time->isCurrentDay(),
            ];
        }
        $daySign = $user->signs()->ofDay(now())->first();
        $tasks   = UserSignRule::ofEnabled()
            ->orderBy('day')
            ->latest()
            ->get()
            ->map(function ($item) use ($user) {
                return [
                    'title'    => $item->day > 1 ? '连续签到' : '签到',
                    'day'      => $item->day,
                    'only_one' => (bool) $item->only_one,
                    'coupon'   => $item->coupon ? [
                        'name'   => $item->coupon->name,
                        'number' => $item->number,
                    ] : (object) [],
                    'score'    => $item->score,
                    'complete' => $user->signLogs()
                        ->when($item->only_one === 0, function (Builder $builder) {
                            $builder->ofDay();
                        })
                        ->ofRule($item)
                        ->exists(),
                ];
            })
            ->toArray();

        return [
            'username'    => hideMobilePhoneNo($this->username),
            'bind_wechat' => $this->getUnionId() ? true : false,
            'sign'        => [
                'daies'             => $daies,
                'total'             => $this->signs()->count(),
                'can_sign'          => ! $daySign,
                'continuous_day'    => $daySign->continuous_day ?? 0,
                'task_time_between' => null,
                'task'              => $tasks,
            ],
            'account'     => [
                'balance' => $this->erpBalance->getBalance(),
                'score'   => $this->account->score,
            ],
            'news'        => [
                'category_id' => $cmsCategoryId,
                'content'     => $cmsContent->toArray(),
            ],
            'order'       => $orderData,
            'count'       => [
                'coupon'   => $this->coupons()->where('status', Collect::STATUS_INIT)->count(),
                'look'     => Subscribe::where('user_id', $this->id)->count(),
                'recharge' => $this->vip_card()->where('status', CardItem::STATUS_USED)->count(),
            ],
            'invite_code' => app('user.hashids')->encode($this->id),
            'info'        => [
                'user_id'  => $this->id,
                'username' => $this->username,
                'nickname' => $this->info?->nickname,
                'avatar'   => $this->info?->avatar_url,
                'gender'   => $this->info?->gender,
                'union_id' => $this->getUnionId,
                'birthday' => (string) $this->info->birthday?->toDateString(),
            ],
        ];
    }
}