<?php

namespace Modules\User\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class DepartmentJoinResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'join_id'     => $this->id,
            'user'        => new UserBaseInfoResource($this->user),
            'department'  => new DepartmentResource($this->department),
            'approver'    => $this->approver?->showName,
            'description' => $this->description,
            'reason'      => $this->reason,
            'status'      => $this->status,
            'status_text' => $this->status->toString(),
            'created_at'  => (string) $this->created_at,
            'updated_at'  => (string) $this->updated_at,
        ];
    }
}