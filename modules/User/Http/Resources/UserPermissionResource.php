<?php

namespace Modules\User\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Spatie\Permission\Models\Role;

class UserPermissionResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'title' => $this->desc,
            'role'  => Role::where('id', $this->resource->pivot->role_id)->first()->name,
        ];
    }
}