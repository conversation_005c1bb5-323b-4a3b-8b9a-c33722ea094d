<?php

namespace Modules\User\Http\Resources;

use App\Http\Resources\BaseCollection;
use Illuminate\Http\Request;
use Modules\Company\Http\Resources\Api\Company\CompanyBaseInfoResource;

class DepartmentCollection extends BaseCollection
{
    public function toArray(Request $request): array
    {
        return [
            'data' => $this->collection->map(function ($item) {
                return [
                    'department_id'  => $item->id,
                    'company'        => new CompanyBaseInfoResource($item->company),
                    'name'           => $item->name,
                    'remark'         => $item->remark,
                    'status'         => $item->status,
                    'children_count' => $item->children_count,
                    'allow_user'     => $item->allow_user,
                ];
            }),
            'page' => $this->page(),
        ];
    }

}