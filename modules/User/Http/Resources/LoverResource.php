<?php

namespace Modules\User\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Modules\User\Enums\LoveTag;

class LoverResource extends JsonResource
{
    public function toArray(Request $request)
    {
        return [
            'lover_id'      => $this->id,
            'target_id'     => $this->target_id,
            'target_mobile' => hideMobilePhoneNo($this->target_mobile),
            'nickname'      => ($this->target?->info->nickname) ?: '尚未注册',
            'avatar'        => $this->target?->info->avatar_url,
            'tag'           => $this->tag,
            'tag_text'      => $this->tag->toString(),
            'created_at'    => $this->created_at->toDateString(),
        ];
    }
}