<?php

namespace Modules\User\Http\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class LoginDeviceResource extends JsonResource
{
    public function toArray($request): array
    {
        return [
            'login_device_id' => $this->resource->id,
            'name'            => $this->resource->name,
            'device_id'       => $this->resource->device_id,
            'created_at'      => (string) $this->resource->created_at,
            'latest_at'       => (string) $this->resource->updated_at,
        ];
    }
}
