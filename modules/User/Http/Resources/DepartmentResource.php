<?php

namespace Modules\User\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Modules\Company\Http\Resources\Api\Company\CompanyBaseInfoResource;

class DepartmentResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'department_id'       => $this->id,
            'name'                => $this->name,
            'company'             => new CompanyBaseInfoResource($this->company),
            'remark'              => $this->remark,
            'status'              => $this->status,
            'is_business'         => $this->is_business,
            'is_assistant'        => $this->is_assistant,
            'is_update_knowledge' => $this->is_update_knowledge,
            'is_watermark'        => $this->is_watermark,
            'is_download'         => $this->is_download,
            'is_password'         => $this->is_password,
            'created_at'          => (string) $this->created_at,
        ];
    }
}