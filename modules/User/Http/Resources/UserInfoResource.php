<?php

namespace Modules\User\Http\Resources;

use App\Models\CardItem;
use App\Models\CustomerService;
use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;
use Modules\Cms\Models\Content;
use Modules\Cms\Models\Material;
use Modules\Coupon\Models\Collect;
use Modules\Interaction\Models\Subscribe;
use Modules\Mall\Enums\OrderStatus;

class UserInfoResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        $identity                = $this->identities->first();
        $cmsCategoryId           = 3;
        $cmsContent              = Content::ofEnabled()
            ->ofPassed()
            ->where('category_id', $cmsCategoryId)
            ->orderByDesc('created_at')
            ->select('id as content_id', 'title')
            ->limit(3)
            ->get();
        $platformCustomerService = CustomerService::where('is_platform', 1)->first();
        $platformCustomer        = [
            'has'     => false,
            'message' => '暂无客服',
            'corp_id' => '',
            'url'     => '',
        ];
        if ($platformCustomerService) {
            $app        = app('wechat.work');
            $account    = $app->getAccount();
            $wechatWork = $app->getClient();
            $response   = $wechatWork->postJson('/cgi-bin/kf/add_contact_way', [
                'open_kfid' => $platformCustomerService->open_kfid,
            ]);
            $data       = $response->toArray();

            if ($response->isSuccessful()) {
                $platformCustomer = [
                    'has'     => true,
                    'message' => $platformCustomerService->name,
                    'corp_id' => $account->getCorpId(),
                    'url'     => $data['url'],
                ];
            } else {
                $platformCustomer = [
                    'has'     => false,
                    'message' => $data['errmsg'],
                    'corp_id' => '',
                    'url'     => '',
                ];
            }
        }

        $daySign = $this->signs()->ofDay(now())->first();

        $continuousDay = $this->signs()
            ->where('sign_at', '>', now()->subDay()->startOfDay())
            ->orderByDesc('sign_at')
            ->value('continuous_day') ?: 0;
        $bigPayCms     = Material::where('name', 'dakehu')->orderByDesc('created_at')->first();
        return [
            'username'          => hideMobilePhoneNo($this->username),
            'bind_wechat'       => $this->getUnionId() ? true : false,
            'sign'              => [
                'can'            => ! $daySign,//是否可以签到true显示签到，false已签到
                'continuous_day' => $continuousDay,//连续签到*天
            ],
            'account'           => [
                'balance' => $this->erpBalance->getBalance(),
                'score'   => $this->account->score,
            ],
            'news'              => [
                'category_id' => $cmsCategoryId,
                'content'     => $cmsContent->toArray(),
            ],
            'big_pay'           => [
                'count'  => 0,
                'cover'  => $bigPayCms?->cover_url,
                'path'   => 'pages/mydd/mydd',
                'app_id' => 'wx0ccd7c0d3538627b',
                'org_id' => 'gh_23c74de5efef',
            ],
            'order'             => [
                'init'     => $this->orders()->whereNull('source->batch_id')->clientOfStatus(OrderStatus::INIT->value)
                    ->count(),
                'paid'     => $this->orders()->whereNull('source->batch_id')->clientOfStatus(OrderStatus::PAID->value)
                    ->count(),
                'deliverd' => $this->orders()->whereNull('source->batch_id')
                    ->clientOfStatus(OrderStatus::DELIVERED->value)->count(),
                'signed'   => $this->orders()->whereNull('source->batch_id')
                    ->clientOfStatus(OrderStatus::SIGNED->value)
                    ->whereNull('source->looked')
                    ->count(),
                'refund'   => $this->refunds()->statusType('process')->count(),
            ],
            'count'             => [
                'coupon'   => $this->coupons()->where('status', Collect::STATUS_INIT)->count(),
                'look'     => Subscribe::where('user_id', $this->id)->count(),
                'recharge' => $this->vip_card()->where('status', CardItem::STATUS_USED)->count(),
            ],
            'invite_code'       => app('user.hashids')->encode($this->id),
            'info'              => [
                'user_id'  => $this->id,
                'username' => $this->username,
                'nickname' => $this->info?->nickname,
                'avatar'   => $this->info?->avatar_url,
                'gender'   => $this->info?->gender,
                'union_id' => $this->getUnionId,
                'birthday' => (string) $this->info->birthday?->toDateString(),
            ],
            'identity'          => new UserIdentityResource($identity),
            'platform_customer' => $platformCustomer,
        ];
    }
}
