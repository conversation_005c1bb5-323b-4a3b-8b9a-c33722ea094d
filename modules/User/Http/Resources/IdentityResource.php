<?php

namespace Modules\User\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class IdentityResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'identity_id' => $this->id,
            'name'        => $this->name,
            'description' => $this->description,
            'cover'       => $this->cover_url,
            'price'       => $this->price,
            'is_default'  => $this->is_default,
            'is_unique'   => $this->is_unique,
            'days'        => $this->days,
            'conditions'  => $this->conditions,
            'rules'       => $this->rules,
            'card'        => $this->parseImageUrl($this->card ?? null),
        ];
    }
}