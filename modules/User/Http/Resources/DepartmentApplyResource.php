<?php

namespace Modules\User\Http\Resources;

use Illuminate\Http\Request;
use Illuminate\Http\Resources\Json\JsonResource;

class DepartmentApplyResource extends JsonResource
{
    public function toArray(Request $request): array
    {
        return [
            'apply_id'      => $this->id,
            'name'          => $this->name,
            'apply_text'    => $this->apply_text,
            'apply_status'  => $this->apply_status,
            'reject_reason' => $this->reject_reason,
            'passed_at'     => (string) $this->passed_at,
            'rejected_at'   => (string) $this->rejected_at,
            'created_at'    => (string) $this->created_at,
        ];
    }
}