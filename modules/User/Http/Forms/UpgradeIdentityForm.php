<?php

namespace Modules\User\Http\Forms;

use App\Models\User;
use Dcat\Admin\Contracts\LazyRenderable;
use Dcat\Admin\Http\JsonResponse;
use Dcat\Admin\Traits\LazyWidget;
use Dcat\Admin\Widgets\Form;
use Illuminate\Support\Arr;
use Modules\User\Enums\IdentityChannel;
use Modules\User\Models\Identity;

class UpgradeIdentityForm extends Form implements LazyRenderable
{
    use LazyWidget;

    public function handle(array $input): JsonResponse
    {
        foreach (Identity::whereIn('id', Arr::wrap($input['identity_id']))->get() as $identity) {
            $identity->entry(
                User::find($this->payload['userId']),
                IdentityChannel::SYSTEM,
                1,
                ['remark' => $input['remark']]
            );
        }

        return $this->response()->success('身份调整成功')->refresh();
    }

    public function form(): void
    {
        if (config('user.CAN_HAS_MANY_IDENTITY')) {
            $this->multipleSelect('identity_id', '新身份')
                ->options(function ($identityId) {
                    if ($identityId) {
                        return Identity::whereIn('id', $identityId)->pluck('name', 'id');
                    } else {
                        return [];
                    }
                })
                ->help('多身份的时候，移除身份有些问题。')
                ->ajax(route('admin.user.identities.ajax'));
        } else {
            $this->select('identity_id', '新身份')
                ->options(Identity::class)
                ->ajax(route('admin.user.identities.ajax'));
        }
        $this->textarea('remark', '备注信息');

        $this->password('password', '操作密码')
            ->rules('required|current_password:admin')
            ->help('请输入当前账户密码以验证您的身份')
            ->required();
    }

    public function default(): array
    {
        $user = User::find($this->payload['userId']);

        if (config('user.CAN_HAS_MANY_IDENTITY')) {
            return [
                'identity_id' => $user->identities->pluck('id'),
            ];
        } else {
            return [
                'identity_id' => $user->identities->first()?->id,
            ];
        }
    }
}