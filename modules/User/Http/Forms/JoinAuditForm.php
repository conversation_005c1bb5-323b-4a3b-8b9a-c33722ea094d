<?php

namespace Modules\User\Http\Forms;

use App\Enums\ApplyStatus;
use Dcat\Admin\Admin;
use Dcat\Admin\Contracts\LazyRenderable;
use Dcat\Admin\Http\JsonResponse;
use Dcat\Admin\Traits\LazyWidget;
use Dcat\Admin\Widgets\Form;
use Modules\User\Models\DepartmentJoin;

class JoinAuditForm extends Form implements LazyRenderable
{
    use LazyWidget;

    public function handle(array $input): JsonResponse
    {
        $apply = DepartmentJoin::find($this->payload['join_id']);

        if ($apply->status != ApplyStatus::INIT) {
            return $this->response()->error('当前状态无法操作');
        }

        switch ($input['result']) {
            case 'pass':
                $apply->pass(Admin::user());
                break;
            case 'reject':
                $apply->reject(Admin::user(), $input['reason']);
        }

        return $this->response()->success('操作成功')->refresh();
    }

    public function form(): void
    {
        $this->radio('result', '审核结果')
            ->options([
                ApplyStatus::PASS->value   => '审核通过',
                ApplyStatus::REJECT->value => '驳回申请',
            ])
            ->default(ApplyStatus::PASS->value)
            ->required()
            ->when(ApplyStatus::REJECT->value, function () {
                $this->textarea('reason', '驳回原因');
            });
        $this->password('password', '操作密码')
            ->rules('required|current_password:admin')
            ->help('请输入当前账户密码以验证您的身份')
            ->required();
    }
}