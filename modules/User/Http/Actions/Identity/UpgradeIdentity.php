<?php

namespace Modules\User\Http\Actions\Identity;

use Dcat\Admin\Grid\RowAction;
use Dcat\Admin\Widgets\Modal;
use Modules\User\Http\Forms\UpgradeIdentityForm;

class UpgradeIdentity extends RowAction
{
    protected string $title = '调整身份';

    public function render(): string
    {
        return Modal::make()
            ->lg()
            ->centered()
            ->title($this->title)
            ->body(UpgradeIdentityForm::make()->payload(['userId' => $this->getKey()]))
            ->button($this->title);
    }

    protected function authorize($user): bool
    {
        return $user->isAdministrator();
    }
}