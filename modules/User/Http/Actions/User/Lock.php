<?php

namespace Modules\User\Http\Actions\User;

use App\Models\User;
use Dcat\Admin\Actions\Response;
use Dcat\Admin\Grid\RowAction;

class Lock extends RowAction
{
    protected string $title = '锁定用户';

    public function handle(): Response
    {
        $user = User::find($this->getKey());

        $user->is_lock = true;

        if ($user->save()) {
            return $this->response()->success('锁定成功')->refresh();
        } else {
            return $this->response()->error('锁定失败');
        }
    }

    public function confirm(): array
    {
        return [
            '锁定用户',
            '确定要锁定当前用户么?'
        ];
    }

    protected function authorize($user): bool
    {
        return $user->isAdministrator() || $user->can(str_replace('\\', '_', get_called_class()));
    }
}