<?php

namespace Modules\User\Http\Actions\User;

use Dcat\Admin\Actions\Response;
use Dcat\Admin\Grid\Tools\AbstractTool;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Artisan;
use Spatie\Permission\Models\Permission;

class InitPermissionTable extends AbstractTool
{
    protected string $title = '初始化权限';

    public function handle(): Response
    {
        try {
            Artisan::call('route:list -v --path=api/ --except-vendor --json');
            $routes = Artisan::output();
            $routes = json_decode($routes, true);

            $list = Arr::where($routes, function ($item) {
                return in_array('check-permission', $item['middleware']);
            });

            foreach ($list as $item) {
                $method = explode('|', $item['method'])[0];
                Permission::firstOrCreate(['name' => $method.'@'.$item['uri']]);
            }

            return $this->response()->success('操作成功')->refresh();
        } catch (Exception $exception) {
            return $this->response()->error($exception->getMessage());
        }
    }

    public function confirm(): array
    {
        return ['确定要初始化所有权限么?'];
    }

    protected function authorize($user): bool
    {
        return $user->isAdministrator();
    }
}