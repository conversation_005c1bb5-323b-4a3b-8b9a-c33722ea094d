<?php

namespace Modules\User\Http\Actions\Department;

use Dcat\Admin\Grid\RowAction;
use Dcat\Admin\Widgets\Modal;
use Modules\User\Http\Forms\DepartmentAuditForm;

class DepartmentAudit extends RowAction
{
    protected string $title = '审核';

    public function render(): string
    {
        return Modal::make()
            ->lg()
            ->centered()
            ->title($this->title)
            ->body(DepartmentAuditForm::make()->payload(['apply_id' => $this->getKey()]))
            ->button($this->title);
    }

    protected function authorize($user): bool
    {
        return $user->isAdministrator();
    }
}