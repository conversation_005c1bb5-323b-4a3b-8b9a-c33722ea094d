<?php

namespace Modules\User\Http\Controllers\Api\User;

use App\Facades\Api;
use App\Http\Controllers\ApiController;
use App\Models\User;
use Illuminate\Http\JsonResponse;
use Modules\User\Http\Requests\InviteBindRequest;
use Modules\User\Http\Resources\InviteCodeResource;
use Modules\User\Http\Resources\ParentUserResource;
use Modules\User\Http\Resources\SlaveCollection;

class InviteController extends ApiController
{
    /**
     * Notes   : 我的邀请码
     *
     * @Date   : 2023/8/10 10:32
     * <AUTHOR> <Jason.C>
     * @return \Illuminate\Http\JsonResponse
     */
    public function index(): JsonResponse
    {
        $user = Api::user();

        return $this->success(new InviteCodeResource($user));
    }

    /**
     * Notes   : 邀请码绑定
     *
     * @Date   : 2022/12/27 16:28
     * <AUTHOR> <Jason.C>
     * @param  \Modules\User\Http\Requests\InviteBindRequest  $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function bind(InviteBindRequest $request): JsonResponse
    {
        if ($request->safe()->has('code')) {
            $code = $request->safe()->code;
            if (strlen($code) == 11 && is_numeric($code)) {
                $parentId = User::where('username', $code)->value('id');
            } else {
                $parentId = app('user.hashids')->decode($code)[0];
            }
        }

        $user = Api::user();

        if ($user->relation->parent_id) {
            if (config('user.CAN_CHANGE_PARENT_SELF')) {
                $user->relation->changeParent($parentId);
                return $this->success();
            } else {
                return $this->failed('您已经有上级用户了，不允许重复绑定');
            }
        } else {
            if (config('user.CAN_BIND_PARENT_SELF')) {
                $user->relation->changeParent($parentId);
                return $this->success();
            } else {
                return $this->failed('不允许自行绑定');
            }
        }
    }

    /**
     * Notes   : 我的上级
     *
     * @Date   : 2023/8/10 10:36
     * <AUTHOR> <Jason.C>
     * @return \Illuminate\Http\JsonResponse
     */
    public function parent(): JsonResponse
    {
        $user = Api::user();

        return $this->success(new ParentUserResource($user->relation->parent));
    }

    /**
     * Notes   : 我的下级
     *
     * @Date   : 2023/8/4 16:51
     * <AUTHOR> <Jason.C>
     */
    public function slaves(): JsonResponse
    {
        $user = Api::user();
        $data = $user->relation
            ->children()
            ->with(['info', 'identities'])
            ->paginate();

        return $this->success(new SlaveCollection($data));
    }
}
