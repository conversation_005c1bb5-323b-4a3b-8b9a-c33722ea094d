<?php

namespace Modules\User\Http\Controllers\Api\User;

use App\Facades\Api;
use App\Http\Controllers\ApiController;
use App\Models\CardItem;
use App\Packages\XinHuaERP\Exceptions\XinHuaErpException;
use App\Packages\XinHuaERP\XinHuaERP;
use Exception;
use Illuminate\Http\Request;
use Illuminate\Support\Str;
use Modules\User\Http\Requests\CardActivateRequest;
use Modules\User\Http\Requests\CardActivateSendRequest;
use Modules\User\Http\Resources\UserVipCardCollection;

class CardController extends ApiController
{

    public function result(Request $request)
    {
        $no = $request->no;
        if ($no) {
            $card = CardItem::where('no', $no)->first();
            if ($card->status == CardItem::STATUS_USED) {
                return $this->success([
                    'code'    => 1,
                    'message' => '会员卡激活成功',
                ]);
            } else {
                return $this->success([
                    'code'    => 0,
                    'message' => '等待',
                ]);
            }
        } else {
            return $this->success([
                'code'    => 0,
                'message' => '等待',
            ]);
        }
    }

    public function activate(CardActivateRequest $request)
    {
        try {
            $user = Api::user();
            $data = [
                'can'         => true,
                'bind_wechat' => true,
                'message'     => '请确认信息',
                'name'        => '',
                'amount'      => '0.00',
                'union_id'    => $user->getUnionId(),
                'original_id' => 'gh_23c74de5efef',
                'target_mini' => 'wx0ccd7c0d3538627b',
                'target_path' => 'pages/GETID/GETID?ID='.$user->getUnionId(),
                'version'     => 'trial',
            ];
            if (blank($user->getUnionId())) {
                $data['can']         = false;
                $data['bind_wechat'] = false;
                $data['message']     = '请先绑定微信信息';
                return $this->success($data);
            }
            $cardNo         = $request->no;
            $cardSecret     = $request->secret;
            $name           = $request->name;
            $result         = XinHuaERP::user()->getCardRecharge($user->getUnionId(), $cardNo, $cardSecret);
            $data['name']   = $result['姓名'];
            $data['amount'] = $result['金额'];
            if (trim($data['name']) != trim($name)) {
                return $this->failed('会员卡姓名不匹配');
            }
            return $this->success($data);
        } catch (XinHuaErpException $exception) {
            if (Str::contains($exception->getMessage(), '对应关系不存在')) {
                $data['can']         = false;
                $data['bind_wechat'] = true;
                $data['message']     = '请前往绑定您的会员信息';
                return $this->success($data);
            }
            return $this->failed($exception->getMessage());
        } catch (Exception $exception) {
            return $this->failed($exception->getMessage());
        }
    }

    public function activateSend(CardActivateSendRequest $request)
    {
        $cardNo     = $request->no;
        $cardSecret = $request->secret;
        $name       = $request->name;
        $amount     = $request->amount;
        $user       = Api::user();
        try {
            $result = XinHuaERP::user()->updateCardRecharge($user->getUnionId(), $cardNo, $cardSecret, $name, $amount);
            CardItem::create([
                'user_id'  => $user->id,
                'batch_id' => 1,
                'no'       => $cardNo,
                'secret'   => $cardSecret,
                'mobile'   => $user->username,
                'amount'   => $amount,
                'name'     => $name,
                'status'   => CardItem::STATUS_USED,
            ]);
            return $this->success([
                'message' => $result['SUCCESS'],
            ]);
        } catch (Exception $exception) {
            return $this->failed('充值失败');
        }
    }

    public function lists(Request $request)
    {
        $user  = Api::user();
        $lists = $user->vip_card()
            ->where('status', CardItem::STATUS_USED)
            ->orderByDesc('updated_at')
            ->paginate((int) $request->limit ?: 20);
        return $this->success(new UserVipCardCollection($lists));
    }

    public function temp()
    {
        $card = CardItem::where('status', CardItem::STATUS_INIT)
            ->inRandomOrder()
            ->first();
        return $this->success([
            'no'       => $card->no,
            'secret'   => $card->secret,
            'amount'   => $card->amount,
            'identity' => $card->identityModel->name ?: '无',
        ]);
    }
}