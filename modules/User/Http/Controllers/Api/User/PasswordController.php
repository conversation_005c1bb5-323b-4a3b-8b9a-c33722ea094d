<?php

namespace Modules\User\Http\Controllers\Api\User;

use App\Facades\Api;
use App\Http\Controllers\ApiController;
use Modules\User\Extensions\Sms\DefaultMessage;
use Modules\User\Extensions\Sms\ForgotMessage;
use Modules\User\Http\Requests\ForgotPasswordRequest;
use Modules\User\Models\Sms;

class PasswordController extends ApiController
{

    public function login(ForgotPasswordRequest $request)
    {
        $user           = Api::user();
        $user->password = $request->safe()->password;
        $user->save();
        return $this->success();
    }

    public function sms()
    {
        $user    = Api::user();
        $message = new ForgotMessage();
        Sms::where('mobile', $user->username)
            ->where('channel', 'FORGOT')
            ->where('used', 0)
            ->update(['used' => 2]);

        $smsId = Sms::send($user->username, $message);
        return $this->success([
            'sms_id' => $smsId,
        ]);
    }

    public function security(ForgotPasswordRequest $request)
    {
        $user = Api::user();
        if (!$user->security) {
            $user->security()->create([
                'password' => $request->safe()->password,
            ]);
        } else {
            $security           = $user->security;
            $security->password = $request->safe()->password;
            $security->save();
        }

        return $this->success();
    }
}