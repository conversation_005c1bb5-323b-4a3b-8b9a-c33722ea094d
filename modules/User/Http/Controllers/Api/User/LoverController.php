<?php

namespace Modules\User\Http\Controllers\Api\User;

use App\Facades\Api;
use App\Http\Controllers\ApiController;
use App\Models\User;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Http\Request;
use Modules\User\Enums\LoveTag;
use Modules\User\Http\Requests\Lover\LoverCreateRequest;
use Exception;
use Modules\User\Http\Requests\Lover\UpdateTagRequest;
use Modules\User\Http\Resources\LoverCollection;
use Modules\User\Http\Resources\LoverResource;
use Modules\User\Models\UserLoveAccount;

class LoverController extends ApiController
{
    public function tags()
    {
        $tags = [];
        foreach (LoveTag::LOVER_MAP as $key => $value) {
            $tags[] = [
                'key'   => $key,
                'value' => $value,
            ];
        }
        return $this->success($tags);
    }

    public function show(UserLoveAccount $lover)
    {
        $this->checkPermission($lover);
        return $this->success(LoverResource::make($lover));
    }

    public function store(LoverCreateRequest $request)
    {
        $user = Api::user();
        try {
            if ($user->username == $request->username) {
                return $this->failed('不可添加自己为亲情号');
            }
            $lover = $user->lovers()->where('target_mobile', $request->username)->first();
            if ($lover) {
                return $this->failed('对方已是您的亲情号');
            }
            $target = User::where('username', $request->username)->first();
            $user->lovers()->create([
                'target_id'     => $target->id ?? null,
                'target_mobile' => $request->username,
                'tag'           => $request->tag,
            ]);
            // 简化写法
            $target?->lovers()->create([
                'target_id'     => $user->id,
                'target_mobile' => $user->username,
                'tag'           => LoveTag::OTHER,
            ]);
            return $this->success(lecho('Binding successful'));
        } catch (Exception) {
            return $this->failed('绑定失败');
        }
    }

    public function index(Request $request)
    {
        $user   = Api::user();
        $lovers = $user->lovers()
            ->when($request->keyword, function (Builder $builder, $keyword) {
                $builder->where('target_mobile', 'like', "%{$keyword}%");
            })
            ->when($request->tag, function (Builder $builder, $tag) {
                $builder->where('tag', $tag);
            })
            ->latest()
            ->paginate((int) $request->limit ?: 20);
        return $this->success(new LoverCollection($lovers));
    }

    public function updateTag(UserLoveAccount $lover, UpdateTagRequest $request)
    {
        $this->checkPermission($lover);
        if ($lover) {
            $lover->tag = $request->tag;
            if ($lover->save()) {
                return $this->success('调整成功');
            } else {
                return $this->failed('调整失败');
            }
        } else {
            return $this->failed('参数错误');
        }
    }

    public function remove(UserLoveAccount $lover)
    {
        $this->checkPermission($lover);
        if ($lover) {
            try {
                $lover->reMove();
                return $this->success('解绑成功');
            } catch (Exception) {
                return $this->failed('解绑失败');
            }
        } else {
            return $this->failed('参数错误');
        }
    }
}
