<?php

namespace Modules\User\Http\Controllers\Api\Safety;

use App\Facades\Api;
use App\Http\Controllers\ApiController;
use Illuminate\Http\JsonResponse;
use Modules\User\Http\Resources\LoginDeviceResource;
use Modules\User\Http\Resources\LoginLogCollection;
use Modules\User\Models\LoginDevice;

class DeviceController extends ApiController
{
    public function devices(): JsonResponse
    {
        $user = Api::user();
        $list = $user->loginDevices()
            ->select(['id', 'name', 'device_id', 'created_at', 'updated_at'])
            ->latest()
            ->get();
        return $this->success(LoginDeviceResource::collection($list));
    }

    /**
     * Notes   : 删除登录设备
     *
     * @Date   : 2023/4/27 16:18
     * <AUTHOR> <Jason.C>
     * @param  \Modules\User\Models\LoginDevice  $device
     * @return \Illuminate\Http\JsonResponse
     * @throws \Exception
     */
    public function delete(LoginDevice $device): JsonResponse
    {
        $this->checkPermission($device);

        $device->delete();
        return $this->success();
    }

    /**
     * Notes   : 登录记录
     *
     * @Date   : 2023/10/25 10:12
     * <AUTHOR> <Jason.C>
     * @return \Illuminate\Http\JsonResponse
     */
    public function logs(): JsonResponse
    {
        $user = Api::user();
        $logs = $user->loginLogs()
            ->select(['user_agent', 'device_id', 'ip', 'created_at'])
            ->latest()
            ->paginate();
        return $this->success(new LoginLogCollection($logs));
    }
}