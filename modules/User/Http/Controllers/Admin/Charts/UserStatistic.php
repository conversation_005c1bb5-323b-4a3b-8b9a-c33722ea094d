<?php

namespace Modules\User\Http\Controllers\Admin\Charts;

use Dcat\Admin\Widgets\ApexCharts\Chart;
use Modules\User\Http\Controllers\Admin\Charts\Traits\UserStatisticBuild;

class UserStatistic extends Chart
{
    use UserStatisticBuild;

    public function __construct($containerSelector = null, $options = [])
    {
        $this->setUpOptions();
    }

    /**
     * 初始化图表配置
     */
    protected function setUpOptions()
    {
        $this->options([
            'colors' => ['#0000FF', '#00FF00', '#F0F0F0'],
            'chart'  => [
                'type'   => 'line',
                'height' => 660,
            ],
        ]);
    }

    /**
     * Notes: 处理图表数据
     *
     * @Author: 丁明远
     * @Date: 2023/5/23 0023 下午 13:43
     */
    protected function buildData(): void
    {
        // 执行你的数据查询逻辑
        $buildData  = $this->userDaysCount(7);
        $data       = $buildData['data'];
        $categories = $buildData['categories'];

        $this->withData($data);
        $this->withCategories($categories);
    }

    /**
     * Notes: 设置图表数据
     *
     * @Author: 丁明远
     * @Date: 2023/5/23 0023 下午 13:43
     * @param  array  $data
     * @return UserStatistic
     */
    public function withData(array $data): UserStatistic
    {
        return $this->option('series', $data);
    }

    /**
     * Notes: 设置图表类别
     *
     * @Author: 丁明远
     * @Date: 2023/5/23 0023 下午 13:42
     * @param  array  $data
     * @return UserStatistic
     */
    public function withCategories(array $data): UserStatistic
    {
        return $this->option('xaxis.categories', $data);
    }

    /**
     * Notes: 渲染图表
     *
     * @Author: 丁明远
     * @Date: 2023/5/23 0023 下午 13:41
     * @return string
     * @throws \Throwable
     */
    public function render(): string
    {
        $this->buildData();
        return parent::render();
    }
}
