<?php

namespace Modules\User\Http\Controllers\Admin;

use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;
use Modules\Mall\Models\OrderInvoice;
use Modules\User\Models\InvoiceTitle;

class InvoiceController extends AdminController
{
    protected string $title = '用户发票抬头管理';

    public function grid(): Grid
    {
        return Grid::make(InvoiceTitle::withCount([
            'logs',
            'logs as logs_init' => function ($query) {
                return $query->where('status', OrderInvoice::STATUS_INIT);
            }
        ])
            ->withSum('logs', 'price')
            ->withSum([
                'logs as logs_init_price' => function ($query) {
                    return $query->where('status', OrderInvoice::STATUS_INIT);
                }
            ], 'price')->latest(), function (Grid $grid) {
            $grid->disableCreateButton();
            $grid->disableActions();
            $grid->column('id', 'ID');
            $grid->column('channel', '发票类型')->using(InvoiceTitle::CHANNEL);
            $grid->column('type', '抬头类型')->using(InvoiceTitle::TYPE);
            $grid->column('title', '抬头');
            $grid->column('no', '纳税人识别号');
            $grid->column('logs_count', '累计开票')->append('/张');
            $grid->column('logs_sum_price', '累计金额')->prepend('&yen; ')->append(' 元');
            $grid->column('logs_init', '申请中')->append('/张');
            $grid->column('logs_init_price', '申请中金额')->prepend('&yen; ')->append(' 元');
            $grid->column('account_bank', '开户行');
            $grid->column('account_no', '开户行账号');
            $grid->column('reg_mobile', '注册电话');
            $grid->column('full_address', '地址');
        });
    }
}