<?php

namespace Modules\User\Http\Controllers\Admin;

use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;
use Modules\User\Models\Sms;

class SmsController extends AdminController
{
    protected string $title = '短信';

    public function grid(): Grid
    {
        return Grid::make(Sms::latest(), function (Grid $grid) {
            $grid->disableActions();
            $grid->disableCreateButton();
            $grid->disableRowSelector();
            $grid->disableBatchActions();

            $grid->quickSearch(['mobile', 'channel', 'gateway'])
                ->placeholder('手机号码/获取渠道/发送通道');

            $grid->filter(function (Grid\Filter $filter) {
                $filter->equal('mobile', '手机号码');
                $filter->equal('channel', '获取渠道');
                $filter->equal('gateway', '发送通道');
                $filter->equal('used', '使用状态')
                    ->select([0 => '未使用', 1 => '已使用']);
                $filter->between('created_at', '发送时间')
                    ->datetime();
            });

            $grid->column('mobile', '手机号码');
            $grid->column('channel', '获取渠道');
            $grid->column('gateway', '发送通道');
            $grid->column('content', '短信内容');
            $grid->column('used', '使用状态')
                ->bool();
            $grid->column('created_at', '发送时间');
        });
    }
}