<?php

namespace Modules\User\Http\Controllers\Admin;

use App\Enums\ApplyStatus;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;
use Modules\User\Http\Actions\Department\JoinAudit;
use Modules\User\Models\DepartmentJoin;

class DepartmentJoinController extends AdminController
{
    protected string $title = '人员加入申请';

    public function grid(): Grid
    {
        return Grid::make(DepartmentJoin::with(['department', 'user', 'approver'])->latest(), function (Grid $grid) {
            $grid->disableDeleteButton();
            $grid->disableCreateButton();
            $grid->disableEditButton();

            $grid->actions(function (Grid\Displayers\Actions $actions) {
                if ($actions->row->status == ApplyStatus::INIT) {
                    $actions->append(new JoinAudit);
                }
            });

            $grid->filter(function (Grid\Filter $filter) {
                $filter->like('department.name', '部门名称');
                $filter->like('user.username', '申请用户');
                $filter->equal('status')
                    ->select(ApplyStatus::STATUS_MAP);
                $filter->between('created_at')
                    ->datetime();
            });

            $grid->column('department.name', '部门名称');
            $grid->column('user', '申请用户')
                ->display(fn($user) => $user->showName);
            $grid->column('approver', '审核用户')
                ->display(fn($user) => $user?->showName);
            $grid->column('description', '申请说明');
            $grid->column('reason', '驳回原因');
            $grid->column('status', '状态')
                ->using(ApplyStatus::STATUS_MAP);
            $grid->column('created_at');
        });
    }
}