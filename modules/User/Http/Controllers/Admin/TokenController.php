<?php

namespace Modules\User\Http\Controllers\Admin;

use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;
use Lara<PERSON>\Sanctum\PersonalAccessToken;

class TokenController extends AdminController
{
    protected string $title = '凭证';

    public function grid(): Grid
    {
        return Grid::make(PersonalAccessToken::with(['tokenable.info'])->latest(), function (Grid $grid) {
            $grid->disableCreateButton();
            $grid->disableEditButton();
            $grid->disableBatchDelete(false);

            $grid->quickSearch(['tokenable.username', 'tokenable.info.nickname', 'name'])
                ->placeholder('用户名/用户昵称/TOKEN名称');

            $grid->filter(function (Grid\Filter $filter) {
                $filter->like('tokenable.username', '用户名');
                $filter->like('tokenable.info.nickname', '用户昵称');
                $filter->like('name', 'TOKEN名称');
                $filter->between('last_used_at', '最后使用')
                    ->datetime();
                $filter->between('expires_at', '过期时间')
                    ->datetime();
                $filter->between('created_at', '创建时间')
                    ->datetime();
            });

            $grid->column('登录用户')
                ->display(fn() => $this->tokenable->showName);
            $grid->column('name', 'TOKEN名称');
            $grid->column('abilities', '权限')
                ->implode('-');
            $grid->column('last_used_at', '最后使用')
                ->display(fn() => $this->last_used_at);
            $grid->column('expires_at', '过期时间')
                ->display(fn() => $this->expires_at);
            $grid->column('created_at', '创建时间')
                ->display(fn() => $this->created_at);
        });
    }

    public function form(): Form
    {
        return Form::make(PersonalAccessToken::class);
    }
}