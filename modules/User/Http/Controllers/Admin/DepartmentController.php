<?php

namespace Modules\User\Http\Controllers\Admin;

use App\Admin\Traits\WithUploads;
use App\Models\Company;
use Dcat\Admin\Form;
use Dcat\Admin\Grid;
use Dcat\Admin\Http\Controllers\AdminController;
use Illuminate\Http\Request;
use Illuminate\Pagination\LengthAwarePaginator;
use Modules\User\Http\Renderable\DepartmentUserTable;
use Modules\User\Models\Department;

class DepartmentController extends AdminController
{
    use WithUploads;

    protected string $title = '部门';

    public function grid(): Grid
    {
        return Grid::make(Department::withCount(['users']), function (Grid $grid) {
            $grid->disableRowSelector();
            $grid->model()->with('company');
            
            $grid->filter(function (Grid\Filter $filter) {
                $filter->scope('trashed', '回收站')->onlyTrashed();

                $filter->like('name', '部门名称');
                $filter->between('created_at', '创建时间')
                    ->datetime();
            });

            $grid->quickSearch(['name', 'alias', 'code', 'pinyin'])
                ->placeholder('部门名称/编号/简称');

            $grid->column('cover', 'LOGO')
                ->thumb(32);
            $grid->column('name', '部门名称')->tree();
            $grid->column('name', '部门名称');
            $grid->column('company.company_name', '所属公司');
            $grid->column('code', '部门编号');
            $grid->column('alias', '简称');
            $grid->column('order', '排序')
                ->orderable();
            $grid->column('status', '状态')
                ->bool();
            $grid->column('allow_user', '允许用户')
                ->bool();
            $grid->column('users_count', '用户数量')
                ->modal('部门用户', function () {
                    return DepartmentUserTable::make()->payload(['department_id' => $this->id]);
                });
            $grid->column('created_at');
        });
    }

    public function form(): Form
    {
        return Form::make(Department::class, function (Form $form) {
            $form->text('name', '部门名称')
                ->required();
            $form->select('company_id', '所属公司')
                ->options(function () {
                    return Company::pluck('company_name', 'id')->toArray();
                });
//            $form->text('code', '部门编号')
//                ->required()
//                ->rules('unique:Modules\User\Models\Department,code,'.$form->model()->id);
//            $form->text('alias', '简称');
//            $form->select('parent_id', '上级部门')
//                ->default(0)
//                ->options(Department::selectOptions())
//                ->required();
            $this->cover($form, 'cover', 'LOGO')
                ->width(4);
            $form->switch('status', '状态')
                ->default(true);
            $form->switch('allow_user', '允许用户');
        });
    }

    public function ajax(Request $request): LengthAwarePaginator
    {
        $q = $request->get('q');

        return Department::ofEnabled()
            ->where('name', 'like', "%$q%")
            ->orWhere('pinyin', 'like', "%$q%")
            ->orWhere('alias', 'like', "%$q%")
            ->paginate(10, ['id', 'name as text']);
    }
}