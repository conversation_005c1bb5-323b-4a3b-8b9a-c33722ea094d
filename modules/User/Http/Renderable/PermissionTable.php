<?php

namespace Modules\User\Http\Renderable;

use Dcat\Admin\Grid;
use Dcat\Admin\Grid\LazyRenderable;
use Spatie\Permission\Models\Permission;

class PermissionTable extends LazyRenderable
{
    public function grid(): Grid
    {
        $model = Permission::class;

        return Grid::make($model, function (Grid $grid) {
            $grid->paginate(10);

            $grid->quickSearch(['id', 'username']);
            $grid->disableActions();
            $grid->column('desc', '功能');
            $grid->column('name', '路由');
        });
    }
}