<?php

namespace Modules\User\Http\Requests;

use App\Http\Requests\BaseFormRequest;
use Modules\User\Rules\BindInviteCodeRule;

class InviteBindRequest extends BaseFormRequest
{
    public function rules(): array
    {
        return [
            'code' => [
                'required',
                'bail',
                new BindInviteCodeRule(),
            ],
        ];
    }

    public function messages(): array
    {
        return [
            'code.required' => '邀请码必须填写',
            'code.size'     => '邀请码长度应为:size位',
        ];
    }
}