<?php

namespace Modules\User\Http\Requests;

use App\Enums\ApplyStatus;
use App\Http\Requests\BaseFormRequest;

class DepartmentJoinAuditRequest extends BaseFormRequest
{
    public function rules(): array
    {
        return [
            'status' => [
                'required',
                'bail',
                'in:'.ApplyStatus::PASS->value.','.ApplyStatus::REJECT->value,
            ],
            'reason' => 'required_if:status,'.ApplyStatus::REJECT->value,
        ];
    }

    public function messages(): array
    {
        return [
            'status.required'    => '审核结果必须选择',
            'status.in'          => '审核结果不支持',
            'reason.required_if' => '拒绝原因必须填写',
        ];
    }
}
