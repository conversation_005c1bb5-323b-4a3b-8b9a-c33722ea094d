<?php

namespace Modules\User\Http\Requests;

use App\Enums\ApplyStatus;
use App\Facades\Api;
use App\Http\Requests\BaseFormRequest;
use Closure;
use Modules\User\Models\Department;
use Modules\User\Models\DepartmentJoin;

class DepartmentJoinRequest extends BaseFormRequest
{
    public function rules(): array
    {
        return [
            'department_id' => [
                'bail',
                'required',
                'integer',
                'exists:Modules\User\Models\Department,id',
                function (string $attribute, mixed $value, Closure $fail) {
                    $department = Department::find($value);
                    if ($department->users()->where('user_id', Api::id())->exists()) {
                        $fail('您已是当前部门成员，请勿重复申请');
                        return;
                    }
                    if (! $department->status) {
                        $fail('部门不存在');
                        return;
                    }
                    if (! $department->allow_user) {
                        $fail('当前部门不接受人员申请');
                        return;
                    }
                    if (DepartmentJoin::ofUser(Api::user())->where('department_id', $value)
                        ->where('status', ApplyStatus::INIT)->exists()) {
                        $fail('您有正在审核中的申请，请勿重复提交');
                        return;
                    }
                    if (DepartmentJoin::ofUser(Api::user())->where('department_id', $value)
                        ->where('status', ApplyStatus::PASS)->exists()) {
                        $fail('您已有审核通过的申请，请勿重复提交');
                    }
                },
            ],
            'description'   => 'bail|required|max:255,min:10',
        ];
    }

    public function messages(): array
    {
        return [
            'department_id.required' => '申请部门必须选择',
            'department_id.integer'  => '部门选择有误',
            'department_id.exists'   => '部门不存在',
            'description.required'   => '申请原因必须填写',
            'description.max'        => '申请原因最多:max字符',
            'description.min'        => '申请原因最少:min字符',
        ];
    }
}