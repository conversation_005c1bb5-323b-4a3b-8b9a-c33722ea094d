<?php

namespace Modules\User\Http\Requests\Invoice;

use App\Http\Requests\BaseFormRequest;
use Illuminate\Validation\Rule;
use Modules\User\Models\InvoiceTitle;

class CreateRequest extends BaseFormRequest
{
    public function rules(): array
    {
        return [
            'channel'      => [
                'required',
                'bail',
                Rule::in(array_keys(InvoiceTitle::CHANNEL)),
            ],
            'type'         => [
                'required',
                'bail',
                Rule::in(array_keys(InvoiceTitle::TYPE)),
            ],
            'title'        => 'required',
            'no'           => 'required_if:type,'.InvoiceTitle::TYPE_COMPANY,
            'account_bank' => 'required_if:channel,'.InvoiceTitle::SPECIAL,
            'account_no'   => 'required_if:channel,'.InvoiceTitle::SPECIAL,
            'reg_mobile'   => 'required_if:channel,'.InvoiceTitle::SPECIAL,
            'email'        => 'required|email',
        ];
    }

    public function messages()
    {
        return [
            'channel.required'         => '请选择发票类型',
            'channel.in'               => '发票类型错误',
            'type.required'            => '请选择主体类型',
            'type.in'                  => '主体类型错误',
            'title.required'           => '请输入发票抬头',
            'no.required_if'           => '企业请输入纳税人识别号',
            'account_bank.required_if' => '增值税专用发票请填写开户银行',
            'account_no.required_if'   => '增值税专用发票请填写开户行账号',
            'reg_mobile.required_if'   => '增值税专用发票请填写注册手机号',
            'email.required'           => '电子邮箱必须填写',
            'email.email'              => '电子邮箱格式错误',
        ];
    }
}
