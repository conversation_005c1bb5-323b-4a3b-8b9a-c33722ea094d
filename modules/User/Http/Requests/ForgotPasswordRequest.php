<?php

namespace Modules\User\Http\Requests;

use App\Http\Requests\BaseFormRequest;
use Illuminate\Validation\Rules\Password;
use Modules\User\Rules\MobileRule;

class ForgotPasswordRequest extends BaseFormRequest
{
    public function rules(): array
    {
        return [
            'password'              => [
                'required',
                'bail',
                'confirmed',
                config('user.STRONG_PASSWORD') ? Password::min(8)->mixedCase()->numbers() : Password::min(6),
            ],
            'password_confirmation' => [
                'required',
            ],
        ];
    }

    public function messages(): array
    {
        return [
            'password.required'              => '密码必须填写',
            'password.confirmed'             => '重复密码错误',
            'password_confirmation.required' => '请输入重复密码',
        ];
    }
}
