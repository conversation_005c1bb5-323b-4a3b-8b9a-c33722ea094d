<?php

namespace Modules\User\Http\Requests\Lover;

use App\Http\Requests\BaseFormRequest;
use Illuminate\Validation\Rule;
use Modules\User\Enums\LoveTag;
use Modules\User\Rules\MobileRule;

class LoverCreateRequest extends BaseFormRequest
{
    public function rules(): array
    {
        return [
            'username' => [
                'required',
                'bail',
                new MobileRule(),
            ],
            'tag'    => [
                'required',
                'bail',
                Rule::in(LoveTag::values()),
            ]
        ];
    }

    public function messages(): array
    {
        return [
            'mobile.required' => '请输入亲情号手机号',
            'tag.required'    => '请选择标签',
            'tag.in'          => '标签不正确',
        ];
    }
}
