<?php

namespace Modules\User\Http\Requests\Auth;

use App\Http\Requests\BaseFormRequest;
use Modules\User\Rules\MobileRule;

class SmsRegisterRequest extends BaseFormRequest
{
    public function rules(): array
    {
        return [
            'username' => [
                'bail',
                'required',
                new MobileRule(),
                'unique:App\Models\User',
            ],
        ];
    }

    public function messages(): array
    {
        return [
            'username.required' => '手机号必须填写',
            'username.unique'   => '手机号已注册',
        ];
    }
}
