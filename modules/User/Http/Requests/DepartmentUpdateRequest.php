<?php

namespace Modules\User\Http\Requests;

use App\Http\Requests\BaseFormRequest;

class DepartmentUpdateRequest extends BaseFormRequest
{
    public function rules(): array
    {
        return [
            'name'  => 'required|bail|min:4|max:32',
            'cover' => 'sometimes|bail|required',
            'alias' => 'nullable',
        ];
    }

    public function messages(): array
    {
        return [
            'name.required'  => '部门名称必须填写',
            'name.min'       => '部门名称最少:min字符',
            'name.max'       => '部门名称最大:max字符',
            'cover.required' => '部门logo必须上传',
        ];
    }
}
