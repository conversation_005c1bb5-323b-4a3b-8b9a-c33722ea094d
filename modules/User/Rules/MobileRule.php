<?php

namespace Modules\User\Rules;

use Closure;
use Illuminate\Contracts\Validation\ValidationRule;

class MobileRule implements ValidationRule
{
    public function validate(string $attribute, mixed $value, Closure $fail): void
    {
        if (strlen($value) != 11) {
            $fail('手机号码不正确');
        }
        $regx = "/^1[3-9]\d{9}$/";
        if (! preg_match($regx, $value)) {
            $fail('手机号码不正确');
        }
    }
}
