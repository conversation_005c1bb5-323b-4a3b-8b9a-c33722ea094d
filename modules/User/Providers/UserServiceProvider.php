<?php

namespace Modules\User\Providers;

use App\Models\Configuration;
use App\Models\User;
use DeviceDetector\DeviceDetector;
use Illuminate\Cache\RateLimiting\Limit;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\RateLimiter;
use Illuminate\Support\ServiceProvider;
use Modules\User\Http\Middleware\CheckForgotSms;
use Modules\User\Http\Middleware\CheckPermission;
use Modules\User\Http\Middleware\CheckSms;
use Modules\User\Models\Certification;
use Modules\User\Models\Identity;
use Modules\User\Models\IdentityOrder;
use Modules\User\Models\LoginDevice;
use Modules\User\Models\LoginLog;
use Modules\User\Models\Relation;
use Modules\User\Models\UserIdentity;
use Modules\User\Models\UserInfo;
use Modules\User\Models\UserSign;
use Modules\User\Models\UserSignLog;
use Overtrue\EasySms\EasySms;
use Vinkla\Hashids\Facades\Hashids;

class UserServiceProvider extends ServiceProvider
{
    protected string $moduleName = 'User';

    protected string $moduleNameLower = 'user';

    public function boot(): void
    {
        $this->bootRelations();
    }

    private function bootRelations(): void
    {
        User::resolveRelationUsing('info', fn(User $user) => $user->hasOne(UserInfo::class));
        User::resolveRelationUsing('signs', fn(User $user) => $user->hasMany(UserSign::class));
        User::resolveRelationUsing('signLogs', fn(User $user) => $user->hasMany(UserSignLog::class));
        User::resolveRelationUsing('lastSign',
            fn(User $user) => $user->hasOne(UserSign::class)->orderByDesc('sign_at'));
        User::resolveRelationUsing('loginDevices', fn(User $user) => $user->hasMany(LoginDevice::class));
        User::resolveRelationUsing('loginLogs', fn(User $user) => $user->hasMany(LoginLog::class));
        User::resolveRelationUsing('relation', fn(User $user) => $user->hasOne(Relation::class)->withDefault());
        User::resolveRelationUsing('certification', fn(User $user) => $user->hasOne(Certification::class));
        //        User::resolveRelationUsing('departments', function (User $user) {
        //            return $user->belongsToMany(Department::class, 'user_department')
        //                ->withPivot(['position'])
        //                ->using(UserDepartment::class)
        //                ->withTimestamps();
        //        });
        User::resolveRelationUsing('identities', function (User $user) {
            return $user->belongsToMany(Identity::class, 'user_identity')
                ->withPivot(['started_at', 'ended_at', 'serial', 'score', 'period', 'last_day'])
                ->using(UserIdentity::class)
                ->withTimestamps();
        });

        \Illuminate\Database\Eloquent\Relations\Relation::morphMap([
            'identifyOrder' => IdentityOrder::class,
        ]);
    }

    public function register(): void
    {
        $this->registerConfig();

        $this->app->singleton('user.hashids', function () {
            return Hashids::connection('invite');
        });
        $this->app->singleton('user.sms', function () {
            return new EasySms(config('user.sms'));
        });
        $this->app->singleton('user.device', function () {
            $dd = new DeviceDetector(request()->header('user-agent'));
            $dd->parse();
            return $dd;
        });
        // 注册路由中间件
        $this->app->singleton('check-sms', CheckSms::class);
        $this->app->singleton('check-forgot-sms', CheckForgotSms::class);
        $this->app->singleton('check-permission', CheckPermission::class);

        RateLimiter::for('sms', function (Request $request) {
            return Limit::perMinute(config('user.SMS_RATE_LIMITER', 2))->by($request->ip());
        });
    }

    private function registerConfig(): void
    {
        Configuration::registerModuleConfig($this->moduleName);

        $this->mergeConfigFrom(
            module_path($this->moduleName, 'Config/sms.php'), 'user.sms'
        );
        $this->mergeConfigFrom(
            module_path($this->moduleName, 'Config/hashids.php'), 'hashids'
        );

        Config::set('horizon.defaults.user', [
            'connection'          => 'redis',
            'queue'               => ['CARD'],
            'balance'             => 'auto',
            'autoScalingStrategy' => 'time',
            'maxProcesses'        => 1,
            'tries'               => 1,
        ]);

        app('config')->set('logging.channels.relation', [
            'driver' => 'daily',
            'path'   => storage_path('logs/relations/change.log'),
            'level'  => config('logging.channels.daily.level'),
            'days'   => 0,
        ]);
    }

    public function provides(): array
    {
        return ['user.hashids', 'user.sms', 'user.device'];
    }
}
