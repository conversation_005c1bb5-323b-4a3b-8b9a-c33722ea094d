<?php

namespace Modules\User\Listeners;

use App\Models\Module;
use Modules\User\Events\DepartmentApplyRejected;
use Modules\User\Notifications\DepartmentApplyNotification;

class DepartmentApplyRejectedListener extends UserBaseListener
{
    public function handle(DepartmentApplyRejected $event): void
    {
        $apply = $event->apply;

        if (Module::isEnabled('notification')) {
            $apply->user->notify(new DepartmentApplyNotification($apply));
        }
    }
}