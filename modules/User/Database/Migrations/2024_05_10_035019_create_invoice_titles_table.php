<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('user_invoice_titles', function (Blueprint $table) {
            $table->id();
            $table->unsignedInteger('user_id');
            $table->string('channel')->comment('发票类型');
            $table->string('type')->comment('个人企业');
            $table->string('title')->comment('抬头');
            $table->string('no')->nullable()->comment('纳税人识别号');
            $table->string('account_bank')->nullable()->comment('开户行');
            $table->string('account_no')->nullable()->comment('开户行账号');
            $table->string('reg_mobile')->nullable()->comment('注册电话');
            $table->unsignedBigInteger('province_id')->nullable()->comment('省');
            $table->unsignedBigInteger('city_id')->nullable()->comment('市');
            $table->unsignedBigInteger('district_id')->nullable()->comment('区');
            $table->string('address')->nullable()->comment('详细地址');
            $table->boolean('default')->default(0);
            $table->timestamps();
            $table->softDeletes();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('user_invoice_titles');
    }
};
