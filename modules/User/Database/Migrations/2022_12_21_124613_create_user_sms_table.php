<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('user_sms', function (Blueprint $table) {
            $table->uuid('id')
                ->primary();
            $table->string('mobile', 16)
                ->index();
            $table->string('channel', 16)
                ->index();
            $table->string('gateway', 16)
                ->default('debug');
            $table->string('content')
                ->nullable();
            $table->boolean('used')
                ->default(0);
            $table->timestamps();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('user_sms');
    }
};
