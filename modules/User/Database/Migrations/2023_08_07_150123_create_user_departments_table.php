<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('user_departments', function (Blueprint $table) {
            $table->comment('部门列表');
            $table->id();
            $table->unsignedBigInteger('parent_id')
                ->index()
                ->default(0);
            $table->string('code')
                ->nullable()
                ->comment('编号');
            $table->string('name');
            $table->string('pinyin')
                ->nullable();
            $table->cover();
            $table->string('alias')
                ->nullable()
                ->comment('简称');
            $table->remark();
            $table->boolean('status')
                ->default(0)
                ->comment('状态');
            $table->boolean('allow_user')
                ->default(0)
                ->comment('非终极部门，允许存在用户');
            $table->integer('order')
                ->default(0);
            $table->timestamps();
            $table->softDeletes();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('user_departments');
    }
};
