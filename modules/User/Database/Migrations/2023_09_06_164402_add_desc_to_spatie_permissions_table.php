<?php

use Illuminate\Support\Facades\Schema;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Database\Migrations\Migration;

return new class extends Migration
{
    public function up(): void
    {
        Schema::table('spatie_permissions', function (Blueprint $table) {
            $table->string('desc')->after('guard_name')->nullable();
        });
    }

    public function down(): void
    {
        Schema::table('spatie_permissions', function (Blueprint $table) {
            $table->dropColumn('desc');
        });
    }
};
