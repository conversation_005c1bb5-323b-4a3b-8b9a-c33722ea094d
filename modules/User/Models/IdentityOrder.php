<?php

namespace Modules\User\Models;

use App\Models\Model;
use App\Traits\AutoCreateOrderNo;
use App\Traits\BelongsToUser;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Modules\Payment\Contracts\Paymentable;
use Modules\Payment\Models\Payment;
use Modules\Payment\Models\Traits\MorphOneCorporate;
use Modules\Payment\Traits\HasPayment;
use Modules\User\Enums\IdentityChannel;
use Modules\User\Enums\IdentityOrderStatus;
use Modules\User\Events\IdentityOrderPaid;

class IdentityOrder extends Model implements Paymentable
{
    use AutoCreateOrderNo,
        BelongsToUser,
        MorphOneCorporate,
        HasPayment;

    public string $orderNoPrefix = 'IO';
    protected     $table         = 'user_identity_orders';
    protected     $casts         = [
        'status' => IdentityOrderStatus::class,
        'ext'    => 'json',
    ];

    public function getRouteKeyName(): string
    {
        return 'no';
    }

    public function getNo()
    {
        return $this->no;
    }

    public function identity(): BelongsTo
    {
        return $this->belongsTo(Identity::class);
    }

    public function setIdentityAttribute(Identity $identity): void
    {
        $this->attributes['identity_id'] = $identity->getKey();
    }

    public function getOrderNumber()
    {
        return $this->ext['score'] ?? '';
    }

    /**
     * Notes   : 改变订单支付状态及后续操作
     *
     * @Date   : 2023/3/22 14:52
     * <AUTHOR> <Jason.C>
     * @param  \Modules\Payment\Models\Payment  $payment
     * @return bool
     */
    public function paid(Payment $payment): bool
    {
        $this->identity->entry($this->user, IdentityChannel::SUBSCRIBE,
            $this->qty, ['order_no' => $this->no]);
        event(new IdentityOrderPaid($this));
        $this->status = IdentityOrderStatus::PAID;
        return $this->save();
    }

    public function canPay(): bool
    {
        return $this->status === IdentityOrderStatus::UNPAY;
    }

    public function getTotalAmount(): string
    {
        return number_format($this->amount, 2, '.', '');
    }

    public function getTitle(): string
    {
        return $this->ext['title'] ?? '';
    }

    /**
     * Notes   : 身份订阅，是否可退款，默认全部都不可以退
     *
     * @Date   : 2023/10/17 9:42
     * <AUTHOR> <Jason.C>
     * @return bool
     */
    public function canRefund(): bool
    {
        return false;
    }
}
