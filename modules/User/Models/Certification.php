<?php

namespace Modules\User\Models;

use App\Models\Model;
use App\Traits\BelongsToUser;
use Modules\User\Enums\CertificationStatus;

class Certification extends Model
{
    use BelongsToUser;

    protected $table = 'user_certifications';

    protected $casts = [
        'status' => CertificationStatus::class,
    ];

    /**
     * Notes   : 通过认证
     *
     * @Date   : 2023/3/16 18:09
     * <AUTHOR> <Jason.C>
     * @return bool
     */
    public function pass(): bool
    {
        $this->status = CertificationStatus::PASS;
        return $this->save();
    }
}
