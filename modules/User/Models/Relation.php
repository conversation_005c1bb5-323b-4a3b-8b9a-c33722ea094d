<?php

namespace Modules\User\Models;

use App\Models\Model;
use App\Models\User;
use App\Traits\BelongsToUser;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasManyThrough;
use Illuminate\Support\Facades\DB;
use RuntimeException;

class Relation extends Model
{
    use BelongsToUser;

    protected $table = 'user_relations';

    protected $primaryKey = 'user_id';

    public function parent(): BelongsTo
    {
        return $this->belongsTo(User::class, 'parent_id')
            ->withDefault(function (User $user) {
                $user->info = new UserInfo([
                    'user_id'  => 0,
                    'nickname' => '无',
                ]);
            });
    }

    public function children(): HasManyThrough
    {
        return $this->hasManyThrough(
            User::class,
            Relation::class,
            'parent_id',
            'id',
            'user_id',
            'user_id'
        );
    }

    /**
     * Notes   : 修改上级用户
     *
     * @Date   : 2022/12/27 20:24
     * <AUTHOR> <Jason.C>
     * @param  int  $parentId
     * @return bool
     * @throws \Exception|\Throwable
     */
    public function changeParent(int $parentId): bool
    {
        if ($this->user_id == $parentId) {
            throw new RuntimeException('不能绑定自己', 500);
        }

        if ($parentId != 0 && ! User::where('id', $parentId)->exists()) {
            throw new RuntimeException('上级用户不存在', 500);
        }

        if (Relation::where('user_id', $parentId)->where('line', 'like', "%,".$this->user_id.",%")->exists()) {
            throw new RuntimeException('不能绑定自己的下级用户', 500);
        }

        return DB::transaction(function () use ($parentId) {
            $change = [
                'parent_id' => 0,
                'layer'     => 1,
                'line'      => '0,',
            ];
            $line   = $this->line;
            $layer  = $this->layer;

            if ($parentId) {
                $parent = User::find($parentId);

                $change = [
                    'parent_id' => $parentId,
                    'layer'     => $parent->relation->layer + 1,
                    'line'      => $parent->relation->line.$parentId.',',
                ];
            }
            $this->update($change);

            DB::update(
                "UPDATE ".$this->getTable()." SET line=CONCAT(?,SUBSTRING(line,LENGTH(?)+1)),layer=layer-? WHERE line LIKE ?",
                [
                    $change['line'],
                    $line,
                    $layer - $change['layer'],
                    "%,".$this->user_id.",%",
                ]
            );

            return true;
        });
    }

    /**
     * Notes   : 获取下级数量
     *
     * @Date   : 2022/12/27 22:37
     * <AUTHOR> <Jason.C>
     * @param  int  $layer  层级数
     * @return int
     */
    public function getChildrenCount(int $layer = 0): int
    {
        return $this
            ->when($layer, function ($q, $v) {
                $q->where('layer', $this->layer + $v);
            })
            ->where('line', 'like', "%,".$this->user_id.",%")
            ->count();
    }

    /**
     * Notes   : 获取顶点数量
     *
     * @Date   : 2022/12/28 09:32
     * <AUTHOR> <Jason.C>
     */
    public static function getVertexCount(): int
    {
        return self::where('parent_id', 0)->count();
    }
}
