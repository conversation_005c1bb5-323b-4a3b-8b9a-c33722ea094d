<?php

namespace Modules\User\Models;

use App\Traits\BelongsToUser;
use Dcat\Admin\Traits\HasDateTimeFormatter;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\Pivot;

class UserDepartment extends Pivot
{
    use BelongsToUser,
        HasDateTimeFormatter;

    protected $table = 'user_department';

    public function department(): BelongsTo
    {
        return $this->belongsTo(Department::class);
    }
}
