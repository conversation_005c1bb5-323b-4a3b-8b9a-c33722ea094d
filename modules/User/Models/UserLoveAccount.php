<?php

namespace Modules\User\Models;

use App\Models\Model;
use App\Models\User;
use App\Traits\BelongsToUser;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Modules\User\Enums\LoveTag;

class UserLoveAccount extends Model
{

    use BelongsToUser;

    protected $casts = [
        'tag' => LoveTag::class,
    ];

    public function reMove()
    {
        UserLoveAccount::where('user_id', $this->target_id)
            ->where('target_id', $this->user_id)
            ->delete();
        $this->delete();
    }

    public function target(): BelongsTo
    {
        return $this->belongsTo(User::class, 'target_id');
    }

    public function scopeOfTarget(Builder $query, User|int $user): void
    {
        if ($user instanceof User) {
            $query->where('target_id', $user->getKey());
        } else {
            $query->where('target_id', $user);
        }
    }

}
