<?php

namespace Modules\User\Models;

use App\Models\Model;
use App\Models\User;
use App\Traits\BelongsToUser;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Illuminate\Database\Eloquent\SoftDeletes;
use Modules\Mall\Models\OrderInvoice;
use Modules\Mall\Traits\HasRegions;

class InvoiceTitle extends Model
{
    use BelongsToUser, SoftDeletes, HasRegions;

    const ORDINARY     = 'ordinary';
    const SPECIAL      = 'special';
    const CHANNEL      = [
        self::ORDINARY => '增值税普通票',
        self::SPECIAL  => '增值税专用票',
    ];
    const TYPE_PERSON  = 'person';
    const TYPE_COMPANY = 'company';
    const TYPE         = [
        self::TYPE_PERSON  => '个人',
        self::TYPE_COMPANY => '企业',
    ];
    protected $table = 'user_invoice_titles';

    protected static function boot()
    {
        parent::boot();
        self::saved(function ($model) {
            if ($model->default) {
                InvoiceTitle::where('user_id', $model->user_id)
                    ->where('default', 1)
                    ->where('id', '!=', $model->id)
                    ->update([
                        'default' => 0,
                    ]);
            }
        });
    }

    public function logs(): HasMany
    {
        return $this->hasMany(OrderInvoice::class, 'invoice_id');
    }

    public function getChannelTextAttribute(): string
    {
        return self::CHANNEL[$this->channel] ?? '';
    }

    public function getTypeTextAttribute(): string
    {
        return self::TYPE[$this->type] ?? '';
    }

    public function scopeOfType(Builder $query, string $type): void
    {
        $query->where('type', $type);
    }

    public function scopeOfChannel(Builder $query, string $channel): void
    {
        $query->where('channel', $channel);
    }

    public function scopeOfTitle(Builder $query, string $title): void
    {
        $query->where('title', $title);
    }

    public function scopeOfNo(Builder $query, string $no): void
    {
        $query->where('title', $no);
    }

    public function getFullAddressAttribute(): string
    {
        return sprintf(
            '%s %s %s %s',
            $this->province->name ?? '',
            $this->city->name ?? '',
            $this->district->name ?? '',
            $this->address
        );
    }
}
