<?php

namespace Modules\User\Models;

use App\Enums\ApplyStatus;
use App\Models\Model;
use App\Traits\BelongsToUser;
use App\Traits\MorphToApprover;
use Exception;
use Illuminate\Contracts\Auth\Authenticatable;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Modules\User\Events\DepartmentJoinCreated;
use Modules\User\Events\DepartmentJoinPassed;
use Modules\User\Events\DepartmentJoinRejected;

class DepartmentJoin extends Model
{
    use BelongsToUser,
        MorphToApprover;

    protected $table = 'user_department_joins';

    protected $casts = [
        'status' => ApplyStatus::class,
    ];

    protected $dispatchesEvents = [
        'created' => DepartmentJoinCreated::class,
    ];

    public function department(): BelongsTo
    {
        return $this->belongsTo(Department::class);
    }

    /**
     * Notes   : 审核通过
     *
     * @Date   : 2023/9/7 13:25
     * <AUTHOR> <Jason.C>
     * @param  \Illuminate\Contracts\Auth\Authenticatable  $user
     * @return bool
     * @throws \Exception
     */
    public function pass(Authenticatable $user): bool
    {
        if ($this->status !== ApplyStatus::INIT) {
            throw new Exception('当前状态不可操作', 500);
        }

        $this->approver = $user;
        $this->status   = ApplyStatus::PASS;
        $result         = $this->save();

        DepartmentJoinPassed::dispatch($this);
        return $result;
    }

    /**
     * Notes   : 拒绝
     *
     * @Date   : 2023/9/7 13:26
     * <AUTHOR> <Jason.C>
     * @param  \Illuminate\Contracts\Auth\Authenticatable  $user
     * @param  string|null  $reason
     * @return bool
     * @throws \Exception
     */
    public function reject(Authenticatable $user, ?string $reason = null): bool
    {
        if ($this->status !== ApplyStatus::INIT) {
            throw new Exception('当前状态不可操作', 500);
        }

        $this->approver = $user;
        $this->status   = ApplyStatus::REJECT;
        $this->reason   = $reason;
        $result         = $this->save();

        DepartmentJoinRejected::dispatch($this);
        return $result;
    }
}
