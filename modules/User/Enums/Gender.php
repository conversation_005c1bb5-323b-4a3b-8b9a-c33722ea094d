<?php

namespace Modules\User\Enums;

use App\Traits\EnumMethods;

enum Gender: string
{
    use EnumMethods;

    case SECRET = 'S';
    case MALE = 'M';
    case FEMALE = 'F';

    const GENDER_MAP = [
        self::SECRET->value => '保密',
        self::MALE->value   => '男性',
        self::FEMALE->value => '女性',
    ];

    public function toString(): string
    {
        return self::GENDER_MAP[$this->value];
    }
}