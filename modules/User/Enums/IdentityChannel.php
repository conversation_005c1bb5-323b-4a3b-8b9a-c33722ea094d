<?php

namespace Modules\User\Enums;

use App\Traits\EnumMethods;

enum IdentityChannel: string
{
    use EnumMethods;

    case AUTO      = 'Auto';
    case REG       = 'Reg';
    case SUBSCRIBE = 'Subscribe';
    case SYSTEM    = 'System';
    case CARD      = 'Card';

    const CHANNEL_MAP = [
        self::AUTO->value      => '自动变更',
        self::REG->value       => '注册默认',
        self::SUBSCRIBE->value => '付费订阅',
        self::SYSTEM->value    => '后台变更',
        self::CARD->value      => '会员卡激活',
    ];

    const CHANNEL_LABEL = [
        self::AUTO->value      => 'info',
        self::REG->value       => 'default',
        self::SUBSCRIBE->value => 'danger',
        self::SYSTEM->value    => 'warning',
        self::CARD->value      => 'success',
    ];

    public function toString(): string
    {
        return self::CHANNEL_MAP[$this->value];
    }
}