<?php

namespace Modules\Notification\Http\Resources;

use App\Http\Resources\BaseCollection;

class NotificationCollection extends BaseCollection
{
    public function toArray($request): array
    {
        return [
            'data' => $this->collection->map(function ($item) {
                return new NotificationResource($item);
            }),
            'page' => $this->page(),
        ];
    }
}
