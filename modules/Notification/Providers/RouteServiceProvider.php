<?php

namespace Modules\Notification\Providers;

use Illuminate\Support\Facades\Route;
use Illuminate\Foundation\Support\Providers\RouteServiceProvider as ServiceProvider;

class RouteServiceProvider extends ServiceProvider
{
    protected string $moduleNamespace = 'Modules\Notification\Http\Controllers';

    public function map(): void
    {
        $this->mapAdminRoutes();
        $this->mapApiRoutes();
        $this->mapOfficeRoutes();
    }

    protected function mapAdminRoutes(): void
    {
        Route::as(config('admin.route.as').'notification.')
            ->domain(config('admin.route.domain'))
            ->middleware(config('admin.route.middleware'))
            ->namespace($this->moduleNamespace.'\Admin')
            ->prefix(config('admin.route.prefix').'/notification')
            ->group(__DIR__.'/../Routes/admin.php');
    }

    protected function mapApiRoutes(): void
    {
        Route::as(config('api.route.as').'notification.')
            ->domain(config('api.route.domain'))
            ->middleware(config('api.route.middleware'))
            ->namespace($this->moduleNamespace.'\Api')
            ->prefix(config('api.route.prefix').'/notification')
            ->group(__DIR__.'/../Routes/api.php');
    }

    protected function mapOfficeRoutes(): void
    {
        Route::as(config('office.route.as').'cms.')
            ->domain(config('office.route.domain'))
            ->middleware(config('office.route.middleware'))
            ->namespace($this->moduleNamespace.'\Office')
            ->prefix(config('office.route.prefix').'/cms')
            ->group(__DIR__.'/../Routes/office.php');
    }
}
