<?php

namespace Modules\Notification\Providers;

use App\Models\Configuration;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\ServiceProvider;

class NotificationServiceProvider extends ServiceProvider
{
    protected string $moduleName = 'Notification';

    protected string $moduleNameLower = 'notification';

    public function boot(): void
    {
    }

    public function register(): void
    {
        $this->registerConfig();
    }

    public function provides(): array
    {
        return [];
    }

    private function registerConfig(): void
    {
        Configuration::registerModuleConfig($this->moduleName);

        Config::set('horizon.defaults.notification', [
            'connection'          => 'redis',
            'queue'               => ['notification'],
            'balance'             => 'auto',
            'autoScalingStrategy' => 'time',
            'maxProcesses'        => 10,
            'tries'               => 1,
        ]);
    }
}
