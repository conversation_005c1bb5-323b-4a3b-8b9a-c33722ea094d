<?php

namespace Modules\Notification\Contracts;

use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Notifications\Notification;
use Laravel\Horizon\Contracts\Silenced;
use Modules\Notification\Channels\IMChannel;
use Modules\Notification\Messages\DatabaseMessage;
use Modules\Notification\Messages\IMMessage;

abstract class BaseNotification extends Notification implements ShouldQueue, Silenced
{
    /**
     * 队列驱动
     *
     * @var string
     */
    public string $connection = 'redis';

    /**
     * 队列名称
     *
     * @var string
     */
    public string $queue = 'notification';

    /**
     * 延时秒数
     *
     * @var int
     */
    public int $delay = 0;

    /**
     * 重试次数
     *
     * @var int
     */
    public int $tries = 1;

    /**
     * Notes   : 为了给用户展示的
     *
     * @Date   : 2023/5/11 17:01
     * <AUTHOR> <Jason.C>
     * @return string
     */
    abstract public static function getTitle(): string;

    /**
     * Notes   : 通知渠道，默认数据库通知
     *
     * @Date   : 2023/5/11 17:02
     * <AUTHOR> <Jason.C>
     * @param  \Illuminate\Database\Eloquent\Model  $notifiable  一般情况下，这个都是User模型，可以根据User模型的判定，来给他不同的通知渠道
     * @return array
     */
    public function via(Model $notifiable): array
    {
        return ['database', IMChannel::class];
    }

    public function toDatabase(Model $notifiable): DatabaseMessage|array
    {
        return [];
    }

    public function toIM(Model $notifiable): IMMessage|array
    {
        return [];
    }

}