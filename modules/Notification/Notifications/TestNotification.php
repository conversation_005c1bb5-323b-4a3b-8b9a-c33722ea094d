<?php

namespace Modules\Notification\Notifications;

use Illuminate\Database\Eloquent\Model;
use Modules\Notification\Channels\JPushChannel;
use Modules\Notification\Contracts\BaseNotification;
use Modules\Notification\Messages\DatabaseMessage;

class TestNotification extends BaseNotification
{
    public static function getTitle(): string
    {
        return '测试消息';
    }

    public function via(Model $notifiable): array
    {
        return method_exists($notifiable, 'isAdministrator') ? ['database'] : ['database', JPushChannel::class];
    }

    public function toDatabase(Model $notifiable): DatabaseMessage|array
    {
        return new DatabaseMessage('【{name}】', [
            'name' => rand(1, 999999),
        ]);
    }

    public function toJPush(): array
    {
        return [

        ];
    }
}