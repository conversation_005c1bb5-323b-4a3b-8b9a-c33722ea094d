<?php

namespace Modules\Notification\Messages;

class IMMessage
{
    protected string $title;
    protected string $content;
    protected array  $userIds;

    /**
     * @param  string  $title  消息标题
     * @param  string  $content  消息内容
     * @param  array  $userIds  接收用户ID数组,为空表示全部发送
     */
    public function __construct(string $title, string $content, array $userIds = [])
    {
        $this->title   = $title;
        $this->content = $content;
        $this->userIds = $userIds;
    }

    public function getTitle(): string
    {
        return $this->title;
    }

    public function getContent(): string
    {
        return $this->content;
    }

    public function getUserIds(): array
    {
        return $this->userIds;
    }

    public function toArray(): array
    {
        return [
            'title'   => $this->title,
            'content' => $this->content,
            'userIds' => $this->userIds,
        ];
    }
} 