<?php

namespace Modules\Notification\Messages;

use Illuminate\Support\Str;

/**
 * 数据库消息
 */
class DatabaseMessage
{
    protected ?string $template;

    protected ?array $args;

    public function __construct(?string $template = null, array $args = [])
    {
        $this->template = $template;
        $this->args     = $args;
    }

    /**
     * Notes   : 设置消息使用的模板
     * 如果使用 %s 来作为模板标识，那么参数应按照顺序来传递
     * 如果使用 {a} 的模板标签方式来作为模板标识，那么会匹配相应的标签作为内容替换
     *
     * @Date   : 2023/9/8 13:45
     * <AUTHOR> <Jason.C>
     * @param  string  $template
     * @return $this
     */
    public function setTemplate(string $template): static
    {
        $this->template = $template;
        return $this;
    }

    /**
     * Notes   : 设置消息模板解析的文件
     *
     * @Date   : 2023/9/8 13:45
     * <AUTHOR> <Jason.C>
     * @param  array  $args
     * @return $this
     */
    public function setArgs(array $args): static
    {
        $this->args = $args;
        return $this;
    }

    /**
     * Notes   : 通过这个魔术方法，给到发送数据库通知需要的 data 的值
     *
     * @Date   : 2023/9/8 13:27
     * <AUTHOR> <Jason.C>
     * @param $name
     * @return string[]|null
     */
    public function __get($name)
    {
        if ($name == 'data') {
            $data = [
                'content' => $this->parseTemplate(),
            ];
            return array_merge($data, $this->args);
        } else {
            return null;
        }
    }

    protected function parseTemplate(): string
    {
        if (Str::contains($this->template, '%s')) {
            return sprintf($this->template, ...array_values($this->args));
        } elseif (preg_match('/\{\S*\}/U', $this->template)) {
            foreach ($this->args as $key => $item) {
                $this->template = Str::replace('{'.$key.'}', $item, $this->template);
            }
            return $this->template;
        } else {
            return $this->template;
        }
    }
}