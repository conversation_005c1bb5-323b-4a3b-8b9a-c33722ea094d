{"openapi": "3.1.0", "info": {"title": "LaravelX", "description": "", "version": "1.0.0"}, "tags": [{"name": "文件上传"}], "paths": {"/storage/upload": {"post": {"summary": "直接上传", "x-apifox-folder": "文件上传", "x-apifox-status": "developing", "deprecated": false, "description": "", "tags": ["文件上传"], "parameters": [{"name": "Authorization", "in": "header", "description": "", "example": "Bearer {{token}}", "schema": {"type": "string"}}, {"name": "X-Device-Id", "in": "header", "description": "", "example": "Jason-apifox", "schema": {"type": "string"}}, {"name": "Accept", "in": "header", "description": "", "example": "application/json", "schema": {"type": "string"}}, {"name": "X-Request-Time", "in": "header", "description": "", "example": "20230110153223", "schema": {"type": "integer"}}, {"name": "X-Device-Type", "in": "header", "description": "", "example": "H5", "schema": {"type": "string"}}, {"name": "User-Agent", "in": "header", "description": "", "example": "Mozilla/5.0 (iPhone; CPU iPhone OS 8_3 like Mac OS X) AppleWebKit/600.1.4 (KHTML, like Gecko) Version/8.0 Mobile/12F70 Safari/600.1.4", "schema": {"type": "string"}}], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"file": {"type": "string", "format": "binary"}}, "required": ["file"]}}}}, "responses": {"200": {"description": "成功", "content": {"application/json": {"schema": {"type": "object", "properties": {}, "x-apifox-ignore-properties": [], "x-apifox-orders": []}}}}}, "x-run-in-apifox": "https://apifox.com/web/project/2118912/apis/api-57200351-run"}}, "/storage/uploads": {"post": {"summary": "多文件上传", "x-apifox-folder": "文件上传", "x-apifox-status": "developing", "deprecated": false, "description": "", "tags": ["文件上传"], "parameters": [{"name": "Authorization", "in": "header", "description": "", "example": "Bearer {{token}}", "schema": {"type": "string"}}, {"name": "X-Device-Id", "in": "header", "description": "", "example": "Jason-apifox", "schema": {"type": "string"}}, {"name": "Accept", "in": "header", "description": "", "example": "application/json", "schema": {"type": "string"}}, {"name": "X-Request-Time", "in": "header", "description": "", "example": "20230110153223", "schema": {"type": "integer"}}, {"name": "X-Device-Type", "in": "header", "description": "", "example": "H5", "schema": {"type": "string"}}, {"name": "User-Agent", "in": "header", "description": "", "example": "Mozilla/5.0 (iPhone; CPU iPhone OS 8_3 like Mac OS X) AppleWebKit/600.1.4 (KHTML, like Gecko) Version/8.0 Mobile/12F70 Safari/600.1.4", "schema": {"type": "string"}}], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"files[]": {"type": "string", "format": "binary"}}, "required": ["files[]"]}}}}, "responses": {"200": {"description": "成功", "content": {"application/json": {"schema": {"type": "object", "properties": {}, "x-apifox-ignore-properties": [], "x-apifox-orders": []}}}}}, "x-run-in-apifox": "https://apifox.com/web/project/2118912/apis/api-110037513-run"}}}, "components": {"schemas": {}}, "servers": []}