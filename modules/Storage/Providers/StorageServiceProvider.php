<?php

namespace Modules\Storage\Providers;

use App\Models\Configuration;
use Illuminate\Filesystem\FilesystemAdapter;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\ServiceProvider;
use League\Flysystem\Filesystem;
use Modules\Storage\Adapters\CosAdapter;
use Modules\Storage\Adapters\OssAdapter;
use Modules\Storage\Adapters\QiNiuAdapter;

class StorageServiceProvider extends ServiceProvider
{
    protected string $moduleName = 'Storage';

    public function boot(): void
    {
        $this->registerDefaultDisk();
    }

    public function register(): void
    {
        $this->registerConfig();
    }

    protected function registerConfig(): void
    {
        Configuration::registerModuleConfig($this->moduleName);
        if (Config::get('store-admin')) {
            Config::set('store-admin.upload.disk', Config::get('storage.FILESYSTEM_DISK'));
        }
        Config::set('filesystems.default', Config::get('storage.FILESYSTEM_DISK'));

        Config::set('filesystems.disks', array_merge(Config::get('filesystems.disks'), [
            'oss'          => ['driver' => 'oss'],
            'cos'          => ['driver' => 'cos'],
            'qiniu'        => ['driver' => 'qiniu'],
            's3compatible' => [
                'driver'                  => 's3',
                'key'                     => Config::get('storage.S3_ACCESS_KEY_ID'),
                'secret'                  => Config::get('storage.S3_SECRET_ACCESS_KEY'),
                'region'                  => Config::get('storage.S3_DEFAULT_REGION'),
                'bucket'                  => Config::get('storage.S3_BUCKET'),
                'url'                     => Config::get('storage.S3_URL'),
                'endpoint'                => Config::get('storage.S3_ENDPOINT'),
                'use_path_style_endpoint' => true,
                'throw'                   => false,
            ],
        ]));
    }

    private function registerDefaultDisk(): void
    {
        $defaultDisk = Config::get('storage.FILESYSTEM_DISK');

        # 默认注册阿里云，这个要从直播拉文件回来
        app('filesystem')->extend('oss', function () {
            $adapter = new OssAdapter(Config::get('storage'));

            return new FilesystemAdapter(
                new Filesystem($adapter),
                $adapter,
                Config::get('storage')
            );
        });

        switch ($defaultDisk) {
            case 'oss':
                break;
            case 'cos':
                app('filesystem')->extend('cos', function () {
                    $adapter = new CosAdapter(Config::get('storage'));

                    return new FilesystemAdapter(
                        new Filesystem($adapter),
                        $adapter,
                        Config::get('storage')
                    );
                });
                break;
            case 'qiniu':
                app('filesystem')->extend('obs', function () {
                    $adapter = new QiNiuAdapter(Config::get('storage'));

                    return new FilesystemAdapter(
                        new Filesystem($adapter),
                        $adapter,
                        Config::get('storage')
                    );
                });
                break;
        }
    }

    public function provides(): array
    {
        return [];
    }
}
