<?php

namespace Modules\Storage\Adapters;

use DateTimeInterface;
use League\Flysystem\FileAttributes;
use League\Flysystem\FilesystemAdapter;
use League\Flysystem\PathPrefixer;
use OSS\OssClient;
use Qcloud\Cos\Client as CosClient;
use Qiniu\Storage\BucketManager;

abstract class CoreAdapter implements FilesystemAdapter
{
    protected PathPrefixer $pathPrefixer;

    protected string $appId;

    protected string $bucket;

    protected string $endPoint;

    protected string $region;

    protected bool $useSSL;

    protected bool $isCname;

    protected ?string $cdnHost;

    protected bool $signedUrl;

    protected cosClient $cosClient;

    protected OssClient $ossClient;

    protected BucketManager $qiniuClient;

    public function __construct(protected array $config)
    {
        $this->pathPrefixer = new PathPrefixer('', DIRECTORY_SEPARATOR);
        $this->initClient();
    }

    /**
     * Notes   : 初始化CLIENT
     *
     * @Date   : 2022/12/19 11:27
     * <AUTHOR> <Jason.C>
     */
    abstract protected function initClient(): void;

    /**
     * Notes   : 获取文件URL
     *
     * @Date   : 2022/12/17 15:32
     * <AUTHOR> <Jason.C>
     * @param  string  $path  文件路径
     * @return string  可访问的URL地址
     */
    abstract public function getUrl(string $path): string;

    /**
     * Notes   : 获取加密的地址
     *
     * @Date   : 2022/12/17 15:33
     * <AUTHOR> <Jason.C>
     * @param  string  $path  文件路径
     * @param  DateTimeInterface  $expiration  加密过期时间
     * @param  array  $options  选项
     * @return bool|string
     */
    abstract public function getTemporaryUrl(
        string $path,
        DateTimeInterface $expiration,
        array $options = []
    ): bool|string;

    /**
     * Notes   : 获取文件的元数据
     *
     * @Date   : 2022/12/19 11:24
     * <AUTHOR> <Jason.C>
     * @param  string  $path  文件路径
     * @return \League\Flysystem\FileAttributes
     */
    abstract protected function getMetadata(string $path): FileAttributes;
}
