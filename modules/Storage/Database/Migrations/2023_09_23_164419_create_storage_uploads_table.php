<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    public function up(): void
    {
        Schema::create('storage_uploads', function (Blueprint $table) {
            $table->id();
            $table->user()
                ->nullable();
            $table->string('hash', 32)
                ->comment('哈希')
                ->unique();
            $table->unsignedBigInteger('size')
                ->comment('文件大小')
                ->default(0);
            $table->string('type', 32)
                ->comment('文件类型')
                ->nullable();
            $table->string('original')
                ->comment('原文件名')
                ->nullable();
            $table->string('disk', 32)
                ->comment('存储驱动')
                ->nullable();
            $table->string('path')
                ->comment('存储路径');
            $table->timestamp('created_at')
                ->index()
                ->nullable();
        });
    }

    public function down(): void
    {
        Schema::dropIfExists('storage_uploads');
    }
};
